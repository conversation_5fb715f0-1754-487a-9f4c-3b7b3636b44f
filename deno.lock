{"version": "5", "remote": {"https://deno.land/std@0.224.0/async/delay.ts": "f90dd685b97c2f142b8069082993e437b1602b8e2561134827eeb7c12b95c499", "https://deno.land/std@0.224.0/http/server.ts": "f9313804bf6467a1704f45f76cb6cd0a3396a3b31c316035e6a4c2035d1ea514"}, "workspace": {"packageJson": {"dependencies": ["npm:@emotion/is-prop-valid@latest", "npm:@hookform/resolvers@^3.9.1", "npm:@lottiefiles/dotlottie-react@latest", "npm:@radix-ui/react-accordion@latest", "npm:@radix-ui/react-alert-dialog@latest", "npm:@radix-ui/react-aspect-ratio@latest", "npm:@radix-ui/react-avatar@latest", "npm:@radix-ui/react-checkbox@latest", "npm:@radix-ui/react-collapsible@latest", "npm:@radix-ui/react-context-menu@latest", "npm:@radix-ui/react-dialog@latest", "npm:@radix-ui/react-dropdown-menu@latest", "npm:@radix-ui/react-hover-card@latest", "npm:@radix-ui/react-label@latest", "npm:@radix-ui/react-menubar@latest", "npm:@radix-ui/react-navigation-menu@latest", "npm:@radix-ui/react-popover@latest", "npm:@radix-ui/react-progress@latest", "npm:@radix-ui/react-radio-group@latest", "npm:@radix-ui/react-scroll-area@latest", "npm:@radix-ui/react-select@latest", "npm:@radix-ui/react-separator@latest", "npm:@radix-ui/react-slider@latest", "npm:@radix-ui/react-slot@latest", "npm:@radix-ui/react-switch@latest", "npm:@radix-ui/react-tabs@latest", "npm:@radix-ui/react-toast@latest", "npm:@radix-ui/react-toggle-group@latest", "npm:@radix-ui/react-toggle@latest", "npm:@radix-ui/react-tooltip@latest", "npm:@supabase/supabase-js@^2.49.4", "npm:@types/node@22", "npm:@types/react-dom@19", "npm:@types/react@19", "npm:autoprefixer@^10.4.20", "npm:canvas-confetti@latest", "npm:class-variance-authority@~0.7.1", "npm:clsx@^2.1.1", "npm:cmdk@latest", "npm:date-fns@4.1.0", "npm:embla-carousel-react@latest", "npm:framer-motion@latest", "npm:input-otp@latest", "npm:lucide-react@0.454", "npm:mapbox-gl@latest", "npm:next-themes@latest", "npm:next@15.2.4", "npm:next@15.3.2", "npm:postcss@8", "npm:react-day-picker@latest", "npm:react-dom@19", "npm:react-hook-form@latest", "npm:react-resizable-panels@latest", "npm:react@19", "npm:recharts@latest", "npm:sonner@latest", "npm:stripe@^18.1.0", "npm:tailwind-merge@^2.5.5", "npm:tailwindcss-animate@^1.0.7", "npm:tailwindcss@^3.4.17", "npm:typescript@5", "npm:vaul@latest", "npm:zod@^3.24.1"]}}}