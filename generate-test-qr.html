<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test QR Code for Fuse.VIP</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .qr-container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
        }
        .qr-code {
            margin: 20px 0;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            padding: 10px;
            background: white;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }
        .business-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 14px;
            line-height: 1.5;
        }
        .qr-data {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }
        .reward-badge {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="qr-container">
        <h1>🎯 Fuse.VIP Test QR Code</h1>
        
        <div class="business-info">
            <h3>📍 Demo Coffee Shop</h3>
            <p><strong>Category:</strong> Restaurant & Café</p>
            <p><strong>Reward:</strong> <span class="reward-badge">100 FUSE Tokens</span></p>
        </div>

        <div id="qrcode" class="qr-code"></div>
        
        <div class="instructions">
            <h4>📱 How to Test:</h4>
            <ol style="text-align: left; padding-left: 20px;">
                <li>Open your Fuse.VIP app or go to <strong>fuse.vip/scan</strong></li>
                <li>Make sure you're logged in as a VIP cardholder</li>
                <li>Scan this QR code with your camera</li>
                <li>You should see a success page with 100 FUSE reward!</li>
            </ol>
        </div>

        <div class="qr-data">
            <strong>QR Data:</strong><br>
            <span id="qr-data-text"></span>
        </div>

        <p style="color: #666; font-size: 12px; margin-top: 20px;">
            This is a test QR code for the Fuse.VIP rewards system.<br>
            Business ID: <code>550e8400-e29b-41d4-a716-************</code>
        </p>
    </div>

    <script>
        // Generate business QR data (matching the format from qr-code-generator.ts)
        const businessId = '550e8400-e29b-41d4-a716-************'; // Test business UUID
        const qrData = JSON.stringify({
            userId: businessId,
            type: 'business',
            timestamp: Date.now()
        });

        // Display the QR data
        document.getElementById('qr-data-text').textContent = qrData;

        // Generate QR code
        QRCode.toCanvas(document.getElementById('qrcode'), qrData, {
            width: 256,
            height: 256,
            margin: 2,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            },
            errorCorrectionLevel: 'M'
        }, function (error) {
            if (error) {
                console.error('Error generating QR code:', error);
                document.getElementById('qrcode').innerHTML = '<p style="color: red;">Error generating QR code</p>';
            } else {
                console.log('QR code generated successfully!');
            }
        });
    </script>
</body>
</html>
