/**
 * Test script to verify logo upload functionality
 * This script tests the business logo upload feature that was recently fixed
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables manually
let supabaseUrl, supabaseAnonKey;

try {
  const envContent = fs.readFileSync('.env.local', 'utf8');
  const envLines = envContent.split('\n');

  envLines.forEach(line => {
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1].trim();
    }
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) {
      supabaseAnonKey = line.split('=')[1].trim();
    }
  });
} catch (err) {
  console.error('❌ Could not read .env.local file:', err.message);
}

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing');
  console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', supabaseAnonKey ? '✅ Set' : '❌ Missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testStorageBucketAccess() {
  console.log('\n🧪 Testing Storage Bucket Access...');
  
  try {
    // Test if business-logos bucket exists and is accessible
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error('❌ Error listing buckets:', error.message);
      return false;
    }
    
    const businessLogosBucket = buckets.find(bucket => bucket.name === 'business-logos');
    if (businessLogosBucket) {
      console.log('✅ business-logos bucket exists');
      console.log('   - Public:', businessLogosBucket.public);
      console.log('   - File size limit:', businessLogosBucket.file_size_limit || 'No limit');
      return true;
    } else {
      console.error('❌ business-logos bucket not found');
      console.log('Available buckets:', buckets.map(b => b.name));
      return false;
    }
  } catch (err) {
    console.error('❌ Exception testing bucket access:', err.message);
    return false;
  }
}

async function testFileUpload() {
  console.log('\n🧪 Testing File Upload (without authentication)...');
  
  try {
    // Create a small test image file (1x1 PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x5D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);
    
    const fileName = `test-${Date.now()}-${Math.random().toString(36).substring(2, 15)}.png`;
    
    console.log('   Attempting to upload:', fileName);
    
    const { data, error } = await supabase.storage
      .from('business-logos')
      .upload(fileName, testImageBuffer, {
        contentType: 'image/png'
      });
    
    if (error) {
      console.error('❌ Upload failed:', error.message);
      console.log('   Error details:', error);
      
      // Check if it's an RLS policy error
      if (error.message.includes('row level security') || error.message.includes('policy')) {
        console.log('   🔍 This appears to be an RLS policy issue');
        console.log('   💡 This is expected for unauthenticated users');
        return { success: false, reason: 'RLS_POLICY', error };
      }
      
      return { success: false, reason: 'UPLOAD_ERROR', error };
    }
    
    console.log('✅ Upload successful!');
    console.log('   File path:', data.path);
    
    // Test getting public URL
    const { data: urlData } = supabase.storage
      .from('business-logos')
      .getPublicUrl(data.path);
    
    console.log('✅ Public URL generated:', urlData.publicUrl);
    
    // Clean up - delete the test file
    const { error: deleteError } = await supabase.storage
      .from('business-logos')
      .remove([data.path]);
    
    if (deleteError) {
      console.log('⚠️  Could not delete test file:', deleteError.message);
    } else {
      console.log('✅ Test file cleaned up');
    }
    
    return { success: true, data, publicUrl: urlData.publicUrl };
    
  } catch (err) {
    console.error('❌ Exception during file upload test:', err.message);
    return { success: false, reason: 'EXCEPTION', error: err };
  }
}

async function testRLSPolicies() {
  console.log('\n🧪 Testing RLS Policies...');
  
  try {
    const { data, error } = await supabase
      .from('pg_policies')
      .select('policyname, cmd, roles, qual, with_check')
      .eq('schemaname', 'storage')
      .eq('tablename', 'objects');
    
    if (error) {
      console.error('❌ Error querying RLS policies:', error.message);
      return false;
    }
    
    console.log('✅ Found', data.length, 'storage RLS policies:');
    data.forEach(policy => {
      console.log(`   - ${policy.policyname} (${policy.cmd}) for ${policy.roles}`);
    });
    
    // Check for required policies
    const requiredPolicies = [
      { cmd: 'INSERT', roles: 'authenticated' },
      { cmd: 'SELECT', roles: 'public' }
    ];
    
    let allPoliciesFound = true;
    requiredPolicies.forEach(required => {
      const found = data.some(policy => 
        policy.cmd === required.cmd && 
        policy.roles.includes(required.roles)
      );
      
      if (found) {
        console.log(`   ✅ ${required.cmd} policy for ${required.roles} users: Found`);
      } else {
        console.log(`   ❌ ${required.cmd} policy for ${required.roles} users: Missing`);
        allPoliciesFound = false;
      }
    });
    
    return allPoliciesFound;
    
  } catch (err) {
    console.error('❌ Exception testing RLS policies:', err.message);
    return false;
  }
}

async function testNetworkApplicationsTable() {
  console.log('\n🧪 Testing Network Applications Table...');
  
  try {
    // Test if we can query the table structure (should work for public access)
    const { data, error } = await supabase
      .from('network_applications')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Error querying network_applications:', error.message);
      return false;
    }
    
    console.log('✅ network_applications table is accessible');
    console.log('   Records found:', data.length);
    
    return true;
    
  } catch (err) {
    console.error('❌ Exception testing network_applications table:', err.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Logo Upload Functionality Tests');
  console.log('=' .repeat(50));
  
  const results = {
    bucketAccess: await testStorageBucketAccess(),
    rlsPolicies: await testRLSPolicies(),
    fileUpload: await testFileUpload(),
    networkApplications: await testNetworkApplicationsTable()
  };
  
  console.log('\n📊 Test Results Summary');
  console.log('=' .repeat(50));
  
  console.log('Storage Bucket Access:', results.bucketAccess ? '✅ PASS' : '❌ FAIL');
  console.log('RLS Policies:', results.rlsPolicies ? '✅ PASS' : '❌ FAIL');
  console.log('Network Applications Table:', results.networkApplications ? '✅ PASS' : '❌ FAIL');
  
  if (results.fileUpload.success) {
    console.log('File Upload (Unauthenticated):', '✅ PASS');
  } else if (results.fileUpload.reason === 'RLS_POLICY') {
    console.log('File Upload (Unauthenticated):', '⚠️  EXPECTED FAIL (RLS Policy)');
    console.log('   This is correct behavior - uploads should require authentication');
  } else {
    console.log('File Upload (Unauthenticated):', '❌ FAIL');
  }
  
  console.log('\n💡 Next Steps:');
  console.log('1. Test with authenticated user in browser');
  console.log('2. Navigate to /register-business page');
  console.log('3. Try uploading a logo file');
  console.log('4. Check browser console for any errors');
  
  const overallSuccess = results.bucketAccess && results.rlsPolicies && results.networkApplications;
  
  if (overallSuccess) {
    console.log('\n🎉 Infrastructure tests PASSED! Logo upload should work for authenticated users.');
  } else {
    console.log('\n⚠️  Some infrastructure tests failed. Check the issues above.');
  }
  
  return overallSuccess;
}

// Run the tests
runAllTests()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(err => {
    console.error('💥 Test runner crashed:', err);
    process.exit(1);
  });
