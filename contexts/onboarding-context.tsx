"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "./auth-context"

type OnboardingStep = "welcome" | "profile" | "wallet" | "complete"

interface OnboardingContextType {
  currentStep: OnboardingStep
  progress: number
  goToStep: (step: OnboardingStep) => void
  nextStep: () => void
  prevStep: () => void
  completeOnboarding: () => void
  skipOnboarding: () => void
  isOnboardingComplete: boolean
  setProfileData: (data: any) => void
  profileData: any
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined)

const STEPS: OnboardingStep[] = ["welcome", "profile", "wallet", "complete"]

export function OnboardingProvider({ children }: { children: ReactNode }) {
  const [currentStep, setCurrentStep] = useState<OnboardingStep>("welcome")
  const [isOnboardingComplete, setIsOnboardingComplete] = useState(false)
  const [profileData, setProfileData] = useState<any>({})
  const router = useRouter()
  const { user, extendSession } = useAuth()

  // Calculate progress percentage based on current step
  const progress = ((STEPS.indexOf(currentStep) + 1) / STEPS.length) * 100

  // Set up session extension interval - extend session every 2 minutes
  useEffect(() => {
    // Only set up the interval if we have a user
    if (!user?.id) return
    
    console.log("Setting up session extension interval for onboarding")
    
    // Extend the session immediately when onboarding starts
    extendSession()
    
    // Then set up an interval to extend it every 2 minutes (120000ms)
    // This ensures the session doesn't expire during onboarding
    const intervalId = setInterval(() => {
      console.log("Extending session during onboarding")
      extendSession()
    }, 120000)
    
    // Clean up the interval when the component unmounts
    return () => {
      console.log("Clearing session extension interval")
      clearInterval(intervalId)
    }
  }, [user?.id, extendSession])

  useEffect(() => {
    // Check if onboarding is already complete
    const onboardingStatus = localStorage.getItem(`onboarding_complete_${user?.id}`)
    if (onboardingStatus === "true") {
      setIsOnboardingComplete(true)
    }

    // Load saved progress if available
    const savedStep = localStorage.getItem(`onboarding_step_${user?.id}`)
    if (savedStep && STEPS.includes(savedStep as OnboardingStep)) {
      setCurrentStep(savedStep as OnboardingStep)
    }

    // Load saved profile data if available
    const savedProfileData = localStorage.getItem(`onboarding_profile_${user?.id}`)
    if (savedProfileData) {
      try {
        setProfileData(JSON.parse(savedProfileData))
      } catch (e) {
        console.error("Error parsing saved profile data", e)
      }
    }

    // Load saved interests if available
    const savedInterests = localStorage.getItem(`onboarding_interests_${user?.id}`)
    if (savedInterests) {
      try {
        setSelectedInterests(JSON.parse(savedInterests))
      } catch (e) {
        console.error("Error parsing saved interests", e)
      }
    }
  }, [user?.id])

  // Save current step to localStorage whenever it changes
  useEffect(() => {
    if (user?.id) {
      localStorage.setItem(`onboarding_step_${user.id}`, currentStep)
    }
  }, [currentStep, user?.id])

  const goToStep = (step: OnboardingStep) => {
    setCurrentStep(step)
  }

  const nextStep = () => {
    const currentIndex = STEPS.indexOf(currentStep)
    if (currentIndex < STEPS.length - 1) {
      setCurrentStep(STEPS[currentIndex + 1])
    }
  }

  const prevStep = () => {
    const currentIndex = STEPS.indexOf(currentStep)
    if (currentIndex > 0) {
      setCurrentStep(STEPS[currentIndex - 1])
    }
  }

  const completeOnboarding = () => {
    if (user?.id) {
      localStorage.setItem(`onboarding_complete_${user.id}`, "true")
      setIsOnboardingComplete(true)
      
      // Check if user is a business applicant
      const isBusinessApplicant = localStorage.getItem(`old(is_admin)${user.id}`) === 'true'
      
      // Clear business application data from localStorage
      localStorage.removeItem(`network_application_${user.id}`)
      localStorage.removeItem(`is_business_applicant_${user.id}`)
      
      // Redirect to appropriate dashboard
      if (isBusinessApplicant) {
        router.push("/dashboard?tab=business")
      } else {
        router.push("/dashboard")
      }
    }
  }

  const skipOnboarding = () => {
    if (user?.id) {
      localStorage.setItem(`onboarding_complete_${user.id}`, "true")
      setIsOnboardingComplete(true)
      router.push("/dashboard")
    }
  }

  const handleSetProfileData = (data: any) => {
    setProfileData({ ...profileData, ...data })
    if (user?.id) {
      localStorage.setItem(`onboarding_profile_${user.id}`, JSON.stringify({ ...profileData, ...data }))
    }
  }


  return (
    <OnboardingContext.Provider
      value={{
        currentStep,
        progress,
        goToStep,
        nextStep,
        prevStep,
        completeOnboarding,
        skipOnboarding,
        isOnboardingComplete,
        setProfileData: handleSetProfileData,
        profileData,
      }}
    >
      {children}
    </OnboardingContext.Provider>
  )
}

export const useOnboarding = () => {
  const context = useContext(OnboardingContext)
  if (context === undefined) {
    throw new Error("useOnboarding must be used within an OnboardingProvider")
  }
  return context
}
