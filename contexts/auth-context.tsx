"use client"

import { createContext, useContext, useEffect, useState, type ReactNode } from "react"
import {
  getSession,
  onAuthStateChange,
  getUserRoles,
  getUserProfile,
  signInWithEmail,
  signUpWithEmail,
  signOut as supabaseSignOut,
  updateSession as supabaseUpdateSession,
  supabase,
} from "@/lib/supabase"
import {
  signInWithJWT,
  signOutWithJWT,
  getCurrentUser,
  refreshAuthToken,
  setupAutoTokenRefresh
} from "@/lib/auth-client"
import type { User, Session } from "@supabase/supabase-js"

// Create a default context value to prevent the "must be used within a provider" error
const defaultContextValue: AuthContextType = {
  user: null,
  session: null,
  profile: null,
  isLoading: false,
  isAdmin: false,
  isBusinessOwner: false,
  roles: [],
  portalRoles: [],
  referralCount: 0,
  signIn: async () => ({ error: new Error("Auth context not initialized") }),
  signUp: async () => ({ error: new Error("Auth context not initialized"), data: null }),
  signOut: async () => {},
  refreshSession: async () => {},
  getSession: async () => ({ error: new Error("Auth context not initialized"), data: null }),
}

interface AuthContextType {
  user: User | null
  session: Session | null
  profile: UserProfile | null
  isLoading: boolean
  isAdmin: boolean
  isBusinessOwner: boolean
  roles: string[]
  portalRoles: string[]
  referralCount: number
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>
  signUp: (email: string, password: string, metadata?: object) => Promise<{ data: any; error: any }>
  signOut: () => Promise<void>
  refreshSession: () => Promise<void>
  getSession: () => Promise<{ data: any; error: any }>
  extendSession: () => Promise<boolean>
}

// Update the context creation line to use the default value:
const AuthContext = createContext<AuthContextType>(defaultContextValue)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  interface UserProfile {
    [key: string]: any
    businessId?: string
  }

  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(false) // Start with false to avoid unnecessary loading
  const [roles, setRoles] = useState<string[]>([])
  const [portalRoles, setPortalRoles] = useState<string[]>([])
  const [isAdmin, setIsAdmin] = useState(false)
  const [isBusinessOwnerState, setIsBusinessOwner] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)
  const [referralCount, setReferralCount] = useState(0)
  const [isLoadingProfile, setIsLoadingProfile] = useState(false)
  const [autoRefreshCleanup, setAutoRefreshCleanup] = useState<(() => void) | null>(null)

  // Initialize auth state and setup auto-refresh
  useEffect(() => {
    // Setup auto token refresh
    const cleanup = setupAutoTokenRefresh()
    setAutoRefreshCleanup(() => cleanup)

    // Check for existing JWT authentication
    const checkJWTAuth = async () => {
      setIsLoading(true)
      try {
        const result = await getCurrentUser()
        if (!result.error && result.user) {
          // Create a mock user object for compatibility
          const mockUser = {
            id: result.user.id,
            email: result.user.email,
            role: result.user.role,
          } as User

          setUser(mockUser)
          setProfile(result.profile)

          // Load additional user data
          await loadUserData(mockUser)
        } else {
          setIsLoading(false)
        }
      } catch (error) {
        console.error("Error checking JWT auth:", error)
        setIsLoading(false)
      }
    }

    checkJWTAuth()

    // Also maintain Supabase auth state for compatibility
    const {
      data: { subscription },
    } = onAuthStateChange(async (_event, session) => {
      try {
        setSession(session)
        setUser(session?.user ?? null)

        if (session?.user) {
          await loadUserData(session.user)
        } else {
          setProfile(null)
          setRoles([])
          setPortalRoles([])
          setIsAdmin(false)
          setIsBusinessOwner(false)
          setReferralCount(0)
          setIsLoading(false)
        }
      } catch (error) {
        console.error("Error in auth state change:", error)
        setIsLoading(false)
      }
    })

    return () => {
      subscription.unsubscribe()
      if (autoRefreshCleanup) {
        autoRefreshCleanup()
      }
    }
  }, [])

  async function loadUserData(user: User) {
    setIsLoading(true)

    try {
      // Load user profile from profiles table
      const profileData = await getUserProfile(user.id)
      setProfile(profileData)

      // Query the admin_users table
      const { data: adminUserData, error: adminUserError } = await supabase
        .from("admin_users")
        .select("id")
        .eq("id", user.id)
        .single()

      if (adminUserError && adminUserError.code !== "PGRST116") {
        // Ignore "No rows found" error (PGRST116), but log other errors
        console.error("Error querying admin_users table:", adminUserError)
      }

      // Set admin status based on user_roles, portal_roles, and admin_users table
      setIsAdmin(
        !!adminUserData // If the user exists in the admin_users table
      )

      // Check if business owner - now checking user_id column in businesses table
      const { data: businessData } = await supabase
        .from("businesses")
        .select("id")
        .eq("user_id", user.id)
        .single() as { data: { id: string } | null }
      
      setProfile((prevProfile: UserProfile | null) => businessData ? { ...prevProfile, businessId: businessData.id } : prevProfile)
      setIsBusinessOwner(!!businessData)

      // Optionally extend profile with business ID if needed
      if (businessData) {
        setProfile((prevProfile) => ({ ...prevProfile, businessId: businessData.id }))
      }

      if (businessData) {
        const { count } = await supabase
          .from("referrals")
          .select("id", { count: "exact" })
          .eq("business_id", businessData.id)

        setReferralCount(count || 0)
      }
    } catch (error) {
      console.error("Error loading user data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    // Initialize auth state when user tries to sign in
    setIsInitialized(true)
    setIsLoading(true)

    try {
      const { error } = await signInWithEmail(email, password)
      setIsLoading(false)
      return { error }
    } catch (error) {
      console.error("Error signing in:", error)
      setIsLoading(false)
      return { error }
    }
  }

  const signUp = async (email: string, password: string, metadata = {}) => {
    // Initialize auth state when user tries to sign up
    setIsInitialized(true)
    setIsLoading(true)

    try {
      const { data, error } = await signUpWithEmail(email, password, metadata)
      setIsLoading(false)
      return { data, error }
    } catch (error) {
      console.error("Error signing up:", error)
      setIsLoading(false)
      return { data: null, error }
    }
  }

  const signOut = async () => {
    setIsLoading(true)
    try {
      // Sign out from both JWT and Supabase
      await signOutWithJWT()
      await supabaseSignOut()

      // Clear local state
      setUser(null)
      setSession(null)
      setProfile(null)
      setRoles([])
      setPortalRoles([])
      setIsAdmin(false)
      setIsBusinessOwner(false)
      setReferralCount(0)

      setIsLoading(false)
    } catch (error) {
      console.error("Error signing out:", error)
      setIsLoading(false)
    }
  }

  const refreshSession = async () => {
    try {
      setIsLoading(true)

      // Try to refresh JWT token first
      const refreshResult = await refreshAuthToken()

      if (refreshResult.success) {
        // Get updated user data
        const result = await getCurrentUser()
        if (!result.error && result.user) {
          const mockUser = {
            id: result.user.id,
            email: result.user.email,
            role: result.user.role,
          } as User

          setUser(mockUser)
          setProfile(result.profile)
          await loadUserData(mockUser)
        }
      } else {
        // If JWT refresh fails, try Supabase session refresh
        const { data, error } = await supabase.auth.refreshSession()
        if (!error && data?.session?.user) {
          setSession(data.session)
          setUser(data.session.user)
          await loadUserData(data.session.user)
        } else if (user) {
          // Fallback to loading existing user data
          await loadUserData(user)
        }
      }
    } catch (error) {
      console.error("Error refreshing session:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const getSession = async () => {
    try {
      const { data, error } = await supabase.auth.getSession()
      if (error) {
        console.error("Error updating session:", error)
        return { data: null, error }
      }
      
      // If session was successfully updated, refresh user data
      if (data?.session?.user) {
        setSession(data.session)
        setUser(data.session.user)
        await loadUserData(data.session.user)
      }
      
      return { data, error: null }
    } catch (error) {
      console.error("Exception getting session:", error)
      return { data: null, error }
    }
  }

  // Add a more robust session extension function
  const extendSession = async () => {
    try {
      console.log("Attempting to extend session...")
      
      // First try to refresh the session
      const { data, error } = await supabase.auth.refreshSession()
      
      if (error) {
        console.error("Error extending session:", error)
        
        // If refresh fails, try to get the current session
        const { data: currentSession } = await supabase.auth.getSession()
        
        if (currentSession?.session) {
          console.log("Retrieved current session")
          setSession(currentSession.session)
          return true
        }
        
        return false
      }
      
      if (data?.session) {
        console.log("Session extended successfully")
        setSession(data.session)
        setUser(data.user)
        return true
      }
      
      return false
    } catch (error) {
      console.error("Exception extending session:", error)
      return false
    }
  }

  const value = {
    user,
    session,
    profile,
    isLoading,
    isAdmin,
    isBusinessOwner: isBusinessOwnerState,
    roles,
    portalRoles,
    referralCount,
    signIn,
    signUp,
    signOut,
    refreshSession,
    getSession,
    extendSession, // Add this to the context
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
