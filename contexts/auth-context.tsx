"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import { Session, User } from "@supabase/supabase-js";

type AuthContextType = {
  session: Session | null;
  user: User | null;
  loading: boolean;
  error: Error | null;
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<void>;
  isAdmin: boolean;
  isBusinessOwner: boolean;
  roles: string[];
  portalRoles: string[];
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isBusinessOwner, setIsBusinessOwner] = useState(false);
  const [roles, setRoles] = useState<string[]>([]);
  const [portalRoles, setPortalRoles] = useState<string[]>([]);
  const [refreshAttempted, setRefreshAttempted] = useState(false);

  // Initialize auth state
  useEffect(() => {
    if (!supabase) return;

    // Set initial session
    const initializeAuth = async () => {
      try {
        setLoading(true);
        
        // Get session from Supabase
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          throw error;
        }
        
        if (data?.session) {
          setSession(data.session);
          setUser(data.session.user);
          
          // Set roles
          setIsAdmin(data.session.user.role === 'admin');
          setRoles(data.session.user.app_metadata?.roles || []);
          setPortalRoles(data.session.user.app_metadata?.portal_roles || []);
          
          // Prefetch user data to ensure API works
          try {
            const response = await fetch('/api/auth/me', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
              },
              credentials: 'include',
            });
            
            if (response.ok) {
              const userData = await response.json();
              setIsBusinessOwner(userData.user?.is_business_owner || false);
            } else {
              // If API call fails, try to refresh the session
              if (!refreshAttempted) {
                setRefreshAttempted(true);
                await refreshSession();
              }
            }
          } catch (e) {
            console.warn('Failed to prefetch user data:', e);
            // Try to refresh the session if API call fails
            if (!refreshAttempted) {
              setRefreshAttempted(true);
              await refreshSession();
            }
          }
        } else {
          // No session, try to refresh
          if (!refreshAttempted) {
            setRefreshAttempted(true);
            await refreshSession();
          }
        }
      } catch (e) {
        console.error('Auth initialization error:', e);
        setError(e instanceof Error ? e : new Error(String(e)));
      } finally {
        setLoading(false);
      }
    };

    // Call initialization
    initializeAuth();

    // Set up auth state change listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        console.log('Auth state changed:', event);
        setSession(newSession);
        setUser(newSession?.user ?? null);
        
        if (newSession?.user) {
          setIsAdmin(newSession.user.role === 'admin');
          setRoles(newSession.user.app_metadata?.roles || []);
          setPortalRoles(newSession.user.app_metadata?.portal_roles || []);
        } else {
          setIsAdmin(false);
          setRoles([]);
          setPortalRoles([]);
        }
        
        // If signed in, ensure API session is working
        if (newSession && (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED')) {
          try {
            const response = await fetch('/api/auth/me', {
              method: 'GET',
              credentials: 'include',
            });
            
            if (response.ok) {
              const userData = await response.json();
              setIsBusinessOwner(userData.user?.is_business_owner || false);
            }
          } catch (e) {
            console.warn('Failed to validate API session after auth change:', e);
          }
        }
      }
    );

    // Cleanup listener on unmount
    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [refreshAttempted]);

  // Sign in function
  const signIn = async (email: string, password: string) => {
    try {
      if (!supabase) throw new Error('Supabase client not initialized');
      
      // Use the API endpoint for sign in to ensure cookies are set properly
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Sign in failed');
      }
      
      // Refresh the session after login
      await supabase.auth.getSession();
      
      return { error: null };
    } catch (e) {
      console.error('Sign in error:', e);
      return { error: e instanceof Error ? e : new Error(String(e)) };
    }
  };

  // Sign out function
  const signOut = async () => {
    try {
      if (!supabase) throw new Error('Supabase client not initialized');
      
      // Use the API endpoint for sign out to ensure cookies are cleared properly
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
      
      // Also sign out on the client
      await supabase.auth.signOut();
      
      // Clear state
      setSession(null);
      setUser(null);
      setIsAdmin(false);
      setIsBusinessOwner(false);
      setRoles([]);
      setPortalRoles([]);
    } catch (e) {
      console.error('Sign out error:', e);
    }
  };

  // Refresh session function
  const refreshSession = async () => {
    try {
      if (!supabase) throw new Error('Supabase client not initialized');
      
      setLoading(true);
      
      // Try to refresh via API first (handles cookies properly)
      try {
        const response = await fetch('/api/auth/refresh', {
          method: 'POST',
          credentials: 'include',
        });
        
        if (response.ok) {
          // API refresh successful, get the updated session
          const { data, error } = await supabase.auth.getSession();
          
          if (error) throw error;
          
          if (data.session) {
            setSession(data.session);
            setUser(data.session.user);
            setIsAdmin(data.session.user.role === 'admin');
            setRoles(data.session.user.app_metadata?.roles || []);
            setPortalRoles(data.session.user.app_metadata?.portal_roles || []);
            return;
          }
        }
      } catch (e) {
        console.warn('API refresh failed, trying client refresh:', e);
      }
      
      // Fallback to client-side refresh
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) throw error;
      
      if (data.session) {
        setSession(data.session);
        setUser(data.session.user);
        setIsAdmin(data.session.user.role === 'admin');
        setRoles(data.session.user.app_metadata?.roles || []);
        setPortalRoles(data.session.user.app_metadata?.portal_roles || []);
      }
    } catch (e) {
      console.error('Session refresh error:', e);
      setError(e instanceof Error ? e : new Error(String(e)));
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        loading,
        error,
        signIn,
        signOut,
        refreshSession,
        isAdmin,
        isBusinessOwner,
        roles,
        portalRoles,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
