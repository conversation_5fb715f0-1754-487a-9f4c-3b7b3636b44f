'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { useWallet } from '@/hooks/use-wallet'
import { supabase } from '@/lib/supabase'
import { 
  checkFuseTrustline, 
  createFuseTokenPayment, 
  generateGamingReturnUrl,
  generateMagneticDexUrl,
  verifyFuseTransaction,
  hasSufficientFuseBalance,
  FUSE_TOKEN,
  GAMING_WALLET
} from '@/lib/fuse-token-utils'

// Types for FUSE gaming
export interface FuseGamingSession {
  id: string
  user_id: string
  game_type: 'fuse-bird' | 'rock-paper-scissors' | '2048'
  session_type: 'competitive'
  entry_fee_fuse: number
  final_score: number
  game_duration_seconds?: number
  moves_count?: number
  highest_tile?: number
  game_data?: any
  wallet_address: string
  transaction_hash?: string
  fuse_transaction_verified: boolean
  trustline_verified: boolean
  is_active: boolean
  created_at: string
  started_at?: string
  ended_at?: string
}

export interface FuseTrustline {
  id: string
  user_id: string
  wallet_address: string
  fuse_issuer: string
  trustline_limit: number
  current_balance: number
  trustline_verified: boolean
  last_verified_at?: string
  created_at: string
  updated_at: string
}

export interface FuseLeaderboard {
  id: string
  game_type: string
  leaderboard_type: 'daily' | 'weekly' | 'monthly' | 'all-time'
  entry_fee_fuse: number
  prize_pool_fuse: number
  total_entries: number
  is_active: boolean
  starts_at: string
  ends_at?: string
  created_at: string
}

export interface FuseLeaderboardEntry {
  id: string
  leaderboard_id: string
  gaming_session_id: string
  user_id: string
  score: number
  rank: number
  prize_amount_fuse: number
  prize_paid: boolean
  prize_transaction_hash?: string
  created_at: string
  // Joined data
  user_profile?: {
    first_name?: string
    last_name?: string
  }
}

interface FuseGamingContextType {
  // State
  currentFuseSession: FuseGamingSession | null
  userTrustline: FuseTrustline | null
  fuseLeaderboards: FuseLeaderboard[]
  fuseLeaderboardEntries: { [leaderboardId: string]: FuseLeaderboardEntry[] }
  isLoading: boolean
  trustlineStatus: {
    checking: boolean
    hasTrustline: boolean
    balance: string
    error?: string
  }

  // Actions
  checkUserTrustline: () => Promise<void>
  startFuseCompetitiveGame: (gameType: string, entryFee?: number) => Promise<string | null>
  createFusePayment: (sessionId: string, gameType: string) => Promise<any>
  verifyFusePayment: (transactionHash: string, sessionId: string) => Promise<boolean>
  submitFuseGameScore: (sessionId: string, score: number, gameData?: any) => Promise<boolean>
  fetchFuseLeaderboards: (gameType?: string) => Promise<void>
  fetchFuseLeaderboardEntries: (leaderboardId: string) => Promise<void>
  getMagneticDexUrl: (sessionId: string, gameType: string) => string
}

const FuseGamingContext = createContext<FuseGamingContextType | undefined>(undefined)

export function FuseGamingProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()
  const { isConnected, walletAddress, createPayment } = useWallet()
  
  // State
  const [currentFuseSession, setCurrentFuseSession] = useState<FuseGamingSession | null>(null)
  const [userTrustline, setUserTrustline] = useState<FuseTrustline | null>(null)
  const [fuseLeaderboards, setFuseLeaderboards] = useState<FuseLeaderboard[]>([])
  const [fuseLeaderboardEntries, setFuseLeaderboardEntries] = useState<{ [key: string]: FuseLeaderboardEntry[] }>({})
  const [isLoading, setIsLoading] = useState(false)
  const [trustlineStatus, setTrustlineStatus] = useState({
    checking: false,
    hasTrustline: false,
    balance: '0',
    error: undefined as string | undefined
  })

  // Check user's FUSE trustline
  const checkUserTrustline = async () => {
    if (!walletAddress) {
      setTrustlineStatus({
        checking: false,
        hasTrustline: false,
        balance: '0',
        error: 'No wallet connected'
      })
      return
    }

    setTrustlineStatus(prev => ({ ...prev, checking: true }))

    try {
      const result = await checkFuseTrustline(walletAddress)
      
      setTrustlineStatus({
        checking: false,
        hasTrustline: result.hasTrustline,
        balance: result.balance,
        error: result.error
      })

      // Update database record if user is authenticated
      if (user && result.hasTrustline) {
        await supabase.rpc('upsert_fuse_trustline', {
          p_user_id: user.id,
          p_wallet_address: walletAddress,
          p_trustline_limit: parseFloat(result.limit),
          p_current_balance: parseFloat(result.balance),
          p_trustline_verified: true
        })
      }

    } catch (error) {
      console.error('Error checking FUSE trustline:', error)
      setTrustlineStatus({
        checking: false,
        hasTrustline: false,
        balance: '0',
        error: 'Failed to check trustline'
      })
    }
  }

  // Start FUSE competitive game
  const startFuseCompetitiveGame = async (gameType: string, entryFee: number = 1): Promise<string | null> => {
    if (!user || !walletAddress) {
      throw new Error('User must be authenticated and wallet connected')
    }

    try {
      setIsLoading(true)

      // Create FUSE gaming session
      const { data: session, error } = await supabase
        .from('fuse_gaming_sessions')
        .insert({
          user_id: user.id,
          game_type: gameType,
          session_type: 'competitive',
          entry_fee_fuse: entryFee,
          wallet_address: walletAddress,
          trustline_verified: trustlineStatus.hasTrustline
        })
        .select()
        .single()

      if (error) throw error

      setCurrentFuseSession(session)
      return session.id
    } catch (error) {
      console.error('Error starting FUSE competitive game:', error)
      return null
    } finally {
      setIsLoading(false)
    }
  }

  // Create FUSE token payment
  const createFusePayment = async (sessionId: string, gameType: string) => {
    if (!isConnected || !createPayment || !walletAddress) {
      throw new Error('Wallet not connected')
    }

    try {
      const returnUrl = generateGamingReturnUrl(sessionId, gameType)
      const memo = `Fuse.vip ${gameType} entry fee: 1 FUSE`
      
      const payload = createFuseTokenPayment(GAMING_WALLET, '1', memo, returnUrl)
      
      return await createPayment(payload.txjson.Destination, payload.txjson.Amount, memo)
    } catch (error) {
      console.error('Error creating FUSE payment:', error)
      throw error
    }
  }

  // Verify FUSE payment
  const verifyFusePayment = async (transactionHash: string, sessionId: string): Promise<boolean> => {
    try {
      const result = await verifyFuseTransaction(transactionHash, '1', GAMING_WALLET)
      
      if (result.verified) {
        // Update session as verified
        const { error } = await supabase
          .from('fuse_gaming_sessions')
          .update({
            transaction_hash: transactionHash,
            fuse_transaction_verified: true,
            started_at: new Date().toISOString()
          })
          .eq('id', sessionId)

        if (error) {
          console.error('Error updating session verification:', error)
          return false
        }

        // Record transaction
        await supabase
          .from('fuse_gaming_transactions')
          .insert({
            gaming_session_id: sessionId,
            user_id: user?.id,
            transaction_type: 'entry_fee',
            fuse_amount: 1.0,
            from_wallet: result.source || walletAddress,
            to_wallet: GAMING_WALLET,
            transaction_hash: transactionHash,
            xrpl_verified: true,
            verified_at: new Date().toISOString()
          })

        return true
      }

      return false
    } catch (error) {
      console.error('Error verifying FUSE payment:', error)
      return false
    }
  }

  // Submit FUSE game score
  const submitFuseGameScore = async (sessionId: string, score: number, gameData?: any): Promise<boolean> => {
    if (!user) return false

    try {
      const session = await supabase
        .from('fuse_gaming_sessions')
        .select('game_type')
        .eq('id', sessionId)
        .single()

      if (!session.data) return false

      const { data, error } = await supabase.rpc('submit_fuse_competitive_score', {
        p_user_id: user.id,
        p_game_type: session.data.game_type,
        p_score: score,
        p_gaming_session_id: sessionId,
        p_game_data: gameData,
        p_moves_count: gameData?.moves_count,
        p_highest_tile: gameData?.highest_tile,
        p_game_duration_seconds: gameData?.game_duration_seconds
      })

      if (error) {
        console.error('Error submitting FUSE game score:', error)
        return false
      }

      return data?.success || false
    } catch (error) {
      console.error('Error submitting FUSE game score:', error)
      return false
    }
  }

  // Fetch FUSE leaderboards
  const fetchFuseLeaderboards = async (gameType?: string) => {
    try {
      setIsLoading(true)
      let query = supabase.from('fuse_leaderboards').select('*').eq('is_active', true)
      
      if (gameType) {
        query = query.eq('game_type', gameType)
      }
      
      const { data, error } = await query.order('game_type').order('leaderboard_type')
      
      if (error) throw error
      setFuseLeaderboards(data || [])
    } catch (error) {
      console.error('Error fetching FUSE leaderboards:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch FUSE leaderboard entries
  const fetchFuseLeaderboardEntries = async (leaderboardId: string) => {
    try {
      const { data, error } = await supabase
        .from('fuse_leaderboard_entries')
        .select(`
          *,
          profiles:user_id (
            first_name,
            last_name
          )
        `)
        .eq('leaderboard_id', leaderboardId)
        .order('rank')
        .limit(100)
      
      if (error) throw error
      
      setFuseLeaderboardEntries(prev => ({
        ...prev,
        [leaderboardId]: data?.map(entry => ({
          ...entry,
          user_profile: entry.profiles
        })) || []
      }))
    } catch (error) {
      console.error('Error fetching FUSE leaderboard entries:', error)
    }
  }

  // Get Magnetic DEX URL with return parameter
  const getMagneticDexUrl = (sessionId: string, gameType: string): string => {
    const returnUrl = generateGamingReturnUrl(sessionId, gameType)
    return generateMagneticDexUrl(returnUrl)
  }

  // Check trustline when wallet connects
  useEffect(() => {
    if (isConnected && walletAddress) {
      checkUserTrustline()
    }
  }, [isConnected, walletAddress])

  // Initialize data on mount
  useEffect(() => {
    if (user) {
      fetchFuseLeaderboards()
    }
  }, [user])

  const value: FuseGamingContextType = {
    currentFuseSession,
    userTrustline,
    fuseLeaderboards,
    fuseLeaderboardEntries,
    isLoading,
    trustlineStatus,
    checkUserTrustline,
    startFuseCompetitiveGame,
    createFusePayment,
    verifyFusePayment,
    submitFuseGameScore,
    fetchFuseLeaderboards,
    fetchFuseLeaderboardEntries,
    getMagneticDexUrl
  }

  return (
    <FuseGamingContext.Provider value={value}>
      {children}
    </FuseGamingContext.Provider>
  )
}

export function useFuseGaming() {
  const context = useContext(FuseGamingContext)
  if (context === undefined) {
    throw new Error('useFuseGaming must be used within a FuseGamingProvider')
  }
  return context
}
