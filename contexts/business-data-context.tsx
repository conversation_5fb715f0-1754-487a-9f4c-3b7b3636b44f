"use client";

import { createContext, useContext, useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/contexts/auth-context";

interface BusinessDataContextType {
  purchases: any[];
  referrals: any[];
  loading: boolean;
}

const BusinessDataContext = createContext<BusinessDataContextType | undefined>(undefined);

export function BusinessDataProvider({ children }: { children: React.ReactNode }) {
  const { user, isBusinessOwner } = useAuth();
  const [purchases, setPurchases] = useState<any[]>([]);
  const [referrals, setReferrals] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchBusinessData() {
      if (!user || !isBusinessOwner) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch purchases
        const { data: purchasesData, error: purchasesError } = await supabase
          .from("purchases")
          .select("*")
          .eq("business_id", user.id);

        if (purchasesError) {
          console.error("Error fetching purchases:", purchasesError);
        } else {
          setPurchases(purchasesData || []);
        }

        // Fetch referrals
        const { data: referralsData, error: referralsError } = await supabase
          .from("referrals")
          .select("*")
          .eq("business_id", user.id);

        if (referralsError) {
          console.error("Error fetching referrals:", referralsError);
        } else {
          setReferrals(referralsData || []);
        }
      } catch (error) {
        console.error("Error fetching business data:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchBusinessData();
  }, [user, isBusinessOwner]);

  return (
    <BusinessDataContext.Provider value={{ purchases, referrals, loading }}>
      {children}
    </BusinessDataContext.Provider>
  );
}

export function useBusinessData() {
  const context = useContext(BusinessDataContext);
  if (!context) {
    throw new Error("useBusinessData must be used within a BusinessDataProvider");
  }
  return context;
}

