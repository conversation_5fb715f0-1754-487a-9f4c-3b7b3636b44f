"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { useAuth } from "./auth-context"
import { supabase } from "@/lib/supabase"

type OnboardingStep = "welcome" | "profile" | "wallet" | "complete"

interface OnboardingModalContextType {
  isOpen: boolean
  currentStep: OnboardingStep
  progress: number
  openOnboarding: () => void
  closeOnboarding: () => void
  goToStep: (step: OnboardingStep) => void
  nextStep: () => void
  prevStep: () => void
  completeOnboarding: () => void
  setProfileData: (data: any) => void
  profileData: any
  isBusinessApplicant: boolean
  applicationData: any
}

const OnboardingModalContext = createContext<OnboardingModalContextType | undefined>(undefined)

const STEPS: OnboardingStep[] = ["welcome", "profile", "wallet", "complete"]

export function OnboardingModalProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false)
  const [currentStep, setCurrentStep] = useState<OnboardingStep>("welcome")
  const [profileData, setProfileData] = useState<any>({})
  const [isBusinessApplicant, setIsBusinessApplicant] = useState(false)
  const [applicationData, setApplicationData] = useState<any>(null)
  const { user } = useAuth()

  // Calculate progress percentage based on current step
  const progress = ((STEPS.indexOf(currentStep) + 1) / STEPS.length) * 100

  // Check if onboarding is needed when user changes
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (!user?.id) return

      try {
        // Check if profile exists
        const { data: profileData, error } = await supabase
          .from("profiles")
          .select("id, is_business_applicant")
          .eq("id", user.id)
          .single()

        if (error) {
          // No profile exists, show onboarding
          console.log("No profile found, showing onboarding modal")
          
          // Check if user is a business applicant
          const isBusinessApplicant = localStorage.getItem(`is_business_applicant_${user.id}`) === 'true'
          setIsBusinessApplicant(isBusinessApplicant)
          
          if (isBusinessApplicant) {
            const applicationDataStr = localStorage.getItem(`network_application_${user.id}`)
            if (applicationDataStr) {
              setApplicationData(JSON.parse(applicationDataStr))
            }
          }
          
          setIsOpen(true)
        } else {
          // Profile exists, check if onboarding is complete
          const onboardingComplete = localStorage.getItem(`onboarding_complete_${user.id}`) === "true"
          
          if (!onboardingComplete) {
            // Onboarding not complete, show modal
            setIsBusinessApplicant(profileData.is_business_applicant || false)
            
            if (profileData.is_business_applicant) {
              // Try to get application data
              const { data: appData } = await supabase
                .from("network_applications")
                .select("*")
                .eq("user_id", user.id)
                .single()
                
              if (appData) {
                setApplicationData(appData)
              }
            }
            
            setIsOpen(true)
          }
        }
      } catch (error) {
        console.error("Error checking onboarding status:", error)
      }
    }

    checkOnboardingStatus()
  }, [user])

  const openOnboarding = () => {
    setIsOpen(true)
  }

  const closeOnboarding = () => {
    setIsOpen(false)
  }

  const goToStep = (step: OnboardingStep) => {
    setCurrentStep(step)
  }

  const nextStep = () => {
    const currentIndex = STEPS.indexOf(currentStep)
    if (currentIndex < STEPS.length - 1) {
      setCurrentStep(STEPS[currentIndex + 1])
    }
  }

  const prevStep = () => {
    const currentIndex = STEPS.indexOf(currentStep)
    if (currentIndex > 0) {
      setCurrentStep(STEPS[currentIndex - 1])
    }
  }

  const completeOnboarding = async () => {
    if (user?.id) {
      try {
        // Create or update profile in Supabase
        const { error: profileError } = await supabase.from("profiles").upsert({
          id: user.id,
          user_email: user.email,
          first_name: profileData.firstName || '',
          last_name: profileData.lastName || '',
          phone: profileData.phone || '',
          updated_at: new Date().toISOString(),
          is_card_holder: false,
          is_business_applicant: isBusinessApplicant
        })
        
        if (profileError) {
          console.error("Error updating profile during onboarding:", profileError)
        }
        
        // Mark onboarding as complete
        localStorage.setItem(`onboarding_complete_${user.id}`, "true")
        
        // Clear business application data from localStorage
        localStorage.removeItem(`network_application_${user.id}`)
        localStorage.removeItem(`is_business_applicant_${user.id}`)
        
        // Close the modal
        setIsOpen(false)
      } catch (error) {
        console.error("Error completing onboarding:", error)
      }
    }
  }

  return (
    <OnboardingModalContext.Provider
      value={{
        isOpen,
        currentStep,
        progress,
        openOnboarding,
        closeOnboarding,
        goToStep,
        nextStep,
        prevStep,
        completeOnboarding,
        setProfileData,
        profileData,
        isBusinessApplicant,
        applicationData
      }}
    >
      {children}
    </OnboardingModalContext.Provider>
  )
}

export const useOnboardingModal = () => {
  const context = useContext(OnboardingModalContext)
  if (context === undefined) {
    throw new Error("useOnboardingModal must be used within an OnboardingModalProvider")
  }
  return context
}