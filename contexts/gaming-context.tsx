'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { useWallet } from '@/hooks/use-wallet'
import { supabase } from '@/lib/supabase'


// Types
export interface GameSession {
  id: string
  user_id: string
  game_type: 'fuse-bird' | 'rock-paper-scissors' | '2048'
  session_type: 'free' | 'competitive'
  entry_fee_xrp: number
  final_score: number
  game_duration_seconds?: number
  moves_count?: number
  highest_tile?: number
  game_data?: any
  wallet_address?: string
  transaction_hash?: string
  is_verified: boolean
  created_at: string
  ended_at?: string
  // Manual payment tracking for competitive sessions
  manual_payment_sent?: boolean
  payment_memo?: string
}

export interface Leaderboard {
  id: string
  game_type: string
  leaderboard_type: 'daily' | 'weekly' | 'monthly' | 'all-time'
  entry_fee_tier: 'free' | 'bronze' | 'silver' | 'gold' | 'platinum'
  min_entry_fee: number
  max_entry_fee?: number
  prize_pool_xrp: number
  total_entries: number
  is_active: boolean
  starts_at: string
  ends_at?: string
}

export interface LeaderboardEntry {
  id: string
  leaderboard_id: string
  game_session_id: string
  user_id: string
  score: number
  rank: number
  prize_amount_xrp: number
  prize_paid: boolean
  created_at: string
  // Manual payment tracking
  manual_payment_sent?: boolean
  payment_memo?: string
  // Joined data
  user_profile?: {
    first_name?: string
    last_name?: string
  }
}

export interface Tournament {
  id: string
  name: string
  description?: string
  game_type: string
  entry_fee_xrp: number
  max_participants?: number
  current_participants: number
  prize_pool_xrp: number
  prize_distribution?: any
  tournament_status: 'upcoming' | 'active' | 'completed' | 'cancelled'
  registration_starts: string
  registration_ends?: string
  tournament_starts?: string
  tournament_ends?: string
}

interface GamingContextType {
  // State
  currentGameSession: GameSession | null
  leaderboards: Leaderboard[]
  leaderboardEntries: { [leaderboardId: string]: LeaderboardEntry[] }
  tournaments: Tournament[]
  userGameStats: any
  isLoading: boolean

  // Actions
  startCompetitiveGame: (gameType: string, entryFee: number) => Promise<string | null>
  fetchLeaderboards: (gameType?: string) => Promise<void>
  fetchLeaderboardEntries: (leaderboardId: string) => Promise<void>
  fetchUserGameStats: () => Promise<void>
  createPaymentTransaction: (entryFee: number, gameType: string) => Promise<any>
  verifyPayment: (transactionHash: string, sessionId: string) => Promise<boolean>

  // Manual payment actions
  markPaymentSent: (sessionId: string, memo: string) => Promise<boolean>
}

const GamingContext = createContext<GamingContextType | undefined>(undefined)

export function GamingProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()
  const { isConnected, walletAddress, createPayment } = useWallet()
  
  // State
  const [currentGameSession, setCurrentGameSession] = useState<GameSession | null>(null)
  const [leaderboards, setLeaderboards] = useState<Leaderboard[]>([])
  const [leaderboardEntries, setLeaderboardEntries] = useState<{ [key: string]: LeaderboardEntry[] }>({})
  const [tournaments, setTournaments] = useState<Tournament[]>([])
  const [userGameStats, setUserGameStats] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Fetch leaderboards
  const fetchLeaderboards = async (gameType?: string) => {
    try {
      setIsLoading(true)
      let query = supabase.from('leaderboards').select('*').eq('is_active', true)
      
      if (gameType) {
        query = query.eq('game_type', gameType)
      }
      
      const { data, error } = await query.order('game_type').order('leaderboard_type').order('entry_fee_tier')
      
      if (error) throw error
      setLeaderboards(data || [])
    } catch (error) {
      console.error('Error fetching leaderboards:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch leaderboard entries
  const fetchLeaderboardEntries = async (leaderboardId: string) => {
    try {
      const { data, error } = await supabase
        .from('leaderboard_entries')
        .select(`
          *,
          profiles:user_id (
            first_name,
            last_name
          )
        `)
        .eq('leaderboard_id', leaderboardId)
        .order('rank')
        .limit(100)
      
      if (error) throw error
      
      setLeaderboardEntries(prev => ({
        ...prev,
        [leaderboardId]: data?.map(entry => ({
          ...entry,
          user_profile: entry.profiles
        })) || []
      }))
    } catch (error) {
      console.error('Error fetching leaderboard entries:', error)
    }
  }

  // Start competitive game
  const startCompetitiveGame = async (gameType: string, entryFee: number): Promise<string | null> => {
    if (!user) {
      throw new Error('User must be authenticated')
    }

    try {
      setIsLoading(true)

      // Create initial game session
      const { data: session, error } = await supabase
        .from('game_sessions')
        .insert({
          user_id: user.id,
          game_type: gameType,
          session_type: 'competitive',
          entry_fee_xrp: entryFee,
          final_score: 0,
          manual_payment_sent: false
        })
        .select()
        .single()

      if (error) throw error

      setCurrentGameSession(session)
      return session.id
    } catch (error) {
      console.error('Error starting competitive game:', error)
      return null
    } finally {
      setIsLoading(false)
    }
  }

  // Create payment transaction (legacy method)
  const createPaymentTransaction = async (entryFee: number, gameType: string) => {
    if (!isConnected || !createPayment) {
      throw new Error('Wallet not connected')
    }

    try {
      // Convert XRP to drops (1 XRP = 1,000,000 drops)
      const amountInDrops = (entryFee * 1000000).toString()

      // Gaming submission wallet
      const gamingWallet = 'rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx'

      const memo = `Fuse.vip ${gameType} entry fee: ${entryFee} XRP`

      return await createPayment(gamingWallet, amountInDrops, memo)
    } catch (error) {
      console.error('Error creating payment transaction:', error)
      throw error
    }
  }

  // Mark manual payment as sent
  const markPaymentSent = async (sessionId: string, memo: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('game_sessions')
        .update({
          manual_payment_sent: true,
          payment_memo: memo,
          is_verified: false // Will be verified manually later
        })
        .eq('id', sessionId)

      if (error) throw error
      return true
    } catch (error) {
      console.error('Error marking payment as sent:', error)
      return false
    }
  }







  // Verify payment
  const verifyPayment = async (transactionHash: string, sessionId: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('game_sessions')
        .update({
          transaction_hash: transactionHash,
          is_verified: true
        })
        .eq('id', sessionId)
      
      if (error) throw error
      return true
    } catch (error) {
      console.error('Error verifying payment:', error)
      return false
    }
  }

  // Fetch user game stats
  const fetchUserGameStats = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from('game_sessions')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
      
      if (error) throw error
      setUserGameStats(data)
    } catch (error) {
      console.error('Error fetching user game stats:', error)
    }
  }

  // Initialize data on mount
  useEffect(() => {
    if (user) {
      fetchLeaderboards()
      fetchUserGameStats()
    }
  }, [user])

  const value: GamingContextType = {
    currentGameSession,
    leaderboards,
    leaderboardEntries,
    tournaments,
    userGameStats,
    isLoading,
    startCompetitiveGame,
    fetchLeaderboards,
    fetchLeaderboardEntries,
    fetchUserGameStats,
    createPaymentTransaction,
    verifyPayment,
    // Manual payment functions
    markPaymentSent
  }

  return (
    <GamingContext.Provider value={value}>
      {children}
    </GamingContext.Provider>
  )
}

export function useGaming() {
  const context = useContext(GamingContext)
  if (context === undefined) {
    throw new Error('useGaming must be used within a GamingProvider')
  }
  return context
}
