"use client";

import { createContext, useContext, useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";

interface ProfileContextType {
  profile: any;
  loading: boolean;
  profileExists: boolean;
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

export function ProfileProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [profileExists, setProfileExists] = useState(false);
  const router = useRouter();

  useEffect(() => {
    async function fetchProfile() {
      if (!user) return;

      // Get the current session
      const { data: sessionData } = await supabase.auth.getSession();
      
      // Check if user has a profile
      if (!sessionData?.session?.user?.user_metadata?.profile_id) {
        // No profile found, redirect to onboarding
        console.log("No profile found for user, redirecting to onboarding");
        setProfileExists(false);
        
        // Create a profile_id in user metadata
        if (user.email) {
          try {
            // Update user metadata with profile_id
            const { data, error } = await supabase.auth.updateUser({
              data: {
                profile_id: user.id,
              }
            });
            
            if (error) {
              console.error("Error updating user metadata:", error);
            }
          } catch (err) {
            console.error("Error in profile setup:", err);
          }
        }
        
        // Check if we're not already on the onboarding page to avoid redirect loops
        if (!window.location.pathname.includes('/onboarding')) {
          router.push('/onboarding');
        }
        
        return;
      }
      const { data: profileData, error } = await supabase
        .from("profiles")
        .select("id, first_name, last_name, created_at, is_business_applicant")
        .eq("id", user.id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // Profile not found (PGRST116 is the "no rows returned" error)
          console.log("No profile found for user, redirecting to onboarding");
          setProfileExists(false);
          
          // Check if we're not already on the onboarding page to avoid redirect loops
          if (!window.location.pathname.includes('/onboarding')) {
            router.push('/onboarding');
          }
        } else {
          console.error("Error fetching profile:", error);
        }
      } else {
        setProfile(profileData);
        setProfileExists(true);
        
        // If user has a profile and is on the login or onboarding page, redirect to dashboard
        const currentPath = window.location.pathname;
        if (currentPath === '/login' || currentPath === '/onboarding') {
          // If user is a business applicant, redirect to business tab
          if (profileData.is_business_applicant) {
            router.push('/dashboard?tab=business');
          } else {
            router.push('/dashboard');
          }
        }
      }

      setLoading(false);
    }

    fetchProfile();
  }, [user, router]);

  return (
    <ProfileContext.Provider value={{ profile, loading, profileExists }}>
      {children}
    </ProfileContext.Provider>
  );
}

export function useProfile() {
  const context = useContext(ProfileContext);
  if (!context) {
    throw new Error("useProfile must be used within a ProfileProvider");
  }
  return context;
}
