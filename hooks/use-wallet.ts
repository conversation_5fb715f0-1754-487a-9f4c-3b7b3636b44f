import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { supabase } from '@/lib/supabase';

// Types for interacting with XUMM SDK
interface XummPayload {
  txjson: {
    TransactionType: string;
    Destination: string;
    Amount: string;
    Memos?: any[];
    [key: string]: any;
  };
  options?: {
    return_url?: {
      app?: string;
      web?: string;
    };
    force_network?: 'MAINNET' | 'TESTNET';
  };
  custom_meta?: {
    identifier?: string;
    instruction?: string;
    blob?: {
      appName?: string;
      appIcon?: string;
    };
  };
}

interface XummPayloadResponse {
  uuid: string;
  next: {
    always: string;
    no_push_msg_received?: string;
  };
  refs: {
    qr_png: string;
    qr_matrix: string;
    qr_uri_quality_opts: string[];
    websocket_status: string;
  };
  pushed: boolean;
}

interface XummSDK {
  authorize: () => Promise<any>;
  logout: () => Promise<any>;
  on: (event: string, callback: () => void) => void;
  user: {
    account: Promise<string>;
  };
  payload: {
    create: (payload: XummPayload) => Promise<XummPayloadResponse>;
  };
}

declare global {
  interface Window {
    Xumm: new (
      apiKey: string,
      options?: {
        appName?: string;
        appDescription?: string;
        appIcon?: string;
      }
    ) => XummSDK;
    xumm: XummSDK;
  }
}

export function useWallet() {
  const { user, profile: profiles } = useAuth() as any;
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [walletAddress, setWalletAddress] = useState('');
  const [accountInfo, setAccountInfo] = useState<any>(null);
  const [xummSDK, setXummSDK] = useState<XummSDK | null>(null);

  // Load and initialize XUMM SDK
  useEffect(() => {
    const loadXummSDK = () => {
      if (typeof window === 'undefined' || window.xumm) return;

      const script = document.createElement('script');
      script.src = 'https://xumm.app/assets/cdn/xumm.min.js';
      script.async = true;

      script.onload = () => {
        const apiKey = process.env.NEXT_PUBLIC_XAMAN_API_KEY;
        if (!apiKey) return console.error('Missing XUMM API key');

        window.xumm = new window.Xumm(apiKey, {
          appName: 'Fuse.vip',
          appDescription: 'Loyalty Reimagined. Commerce Reconnected!',
          appIcon:
            'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png',
        });

        window.xumm.on('ready', () => {
          console.log('XUMM SDK ready');
          setXummSDK(window.xumm);
        });

        window.xumm.on('success', async () => {
          try {
            const account = await window.xumm.user.account;
            setWalletAddress(account);
            setIsConnected(true);

            if (user?.id) {
              await supabase
                .from('profiles')
                .update({ xrp_wallet_address: account })
                .eq('id', user.id);
            }
          } catch (err) {
            console.error('Error fetching XUMM account:', err);
          }
        });

        window.xumm.on('logout', () => {
          setWalletAddress('');
          setIsConnected(false);
          setAccountInfo(null);
        });
      };

      document.body.appendChild(script);
    };

    loadXummSDK();
  }, [user?.id]);

  // Sync profile wallet to state
  useEffect(() => {
    if (profiles?.xrp_wallet_address) {
      setIsConnected(true);
      setWalletAddress(profiles.xrp_wallet_address);
    } else {
      setIsConnected(false);
      setWalletAddress('');
    }
  }, [profiles?.xrp_wallet_address]);

  const connect = useCallback(async () => {
    if (!user?.id) return;
    setIsConnecting(true);
    try {
      if (!window.xumm) throw new Error('XUMM SDK not initialized');
      await window.xumm.authorize();
      return true; // success handled by 'success' event
    } catch (err) {
      console.error('Wallet connect failed:', err);
      throw err;
    } finally {
      setIsConnecting(false);
    }
  }, [user?.id]);

  const disconnect = useCallback(async () => {
    if (!user?.id || !window.xumm) return;
    try {
      await window.xumm.logout();
      await supabase
        .from('profiles')
        .update({ xrp_wallet_address: null })
        .eq('id', user.id);
    } catch (err) {
      console.error('Wallet disconnect failed:', err);
    }
  }, [user?.id]);

  const fetchAccountInfo = useCallback(async () => {
    if (!walletAddress) return;
    try {
      // Replace with actual XRPL query later
      setAccountInfo({
        Balance: '**********',
        Sequence: 1,
        OwnerCount: 0,
      });
    } catch (err) {
      console.error('Failed to fetch XRPL account info:', err);
    }
  }, [walletAddress]);

  const createPayment = useCallback(
    async (destination: string, amount: string, memo?: string) => {
      if (!window.xumm || !isConnected) {
        throw new Error('XUMM SDK not initialized or not connected');
      }

      const payload: XummPayload = {
        txjson: {
          TransactionType: 'Payment',
          Destination: destination,
          Amount: amount,
        },
        options: {
          return_url: {
            app: `${window.location.origin}/payment-success?amount=${amount}&destination=${destination}`,
            web: `${window.location.origin}/payment-success?amount=${amount}&destination=${destination}`,
          },
          force_network: 'MAINNET',
        },
        custom_meta: {
          identifier: `fuse_payment_${Date.now()}`,
          instruction:
            memo ||
            'Please sign this transaction to complete your Fuse.vip payment',
          blob: {
            appName: 'Fuse.vip',
            appIcon:
              'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png',
          },
        },
      };

      if (memo) {
        payload.txjson.Memos = [
          {
            Memo: {
              MemoType: Buffer.from('memo').toString('hex').toUpperCase(),
              MemoData: Buffer.from(memo).toString('hex').toUpperCase(),
            },
          },
        ];
      }

      try {
        const response = await window.xumm.payload.create(payload);
        return response;
      } catch (err) {
        console.error('Payment payload creation failed:', err);
        throw err;
      }
    },
    [isConnected]
  );

  return {
    isConnecting,
    isConnected,
    walletAddress,
    xrpWalletAddress: walletAddress,
    accountInfo,
    connect,
    disconnect,
    fetchAccountInfo,
    xummSDK,
    createPayment,
  };
}
