"use client"

import { useState, useEffect } from "react"

interface MapboxToken {
  token: string
  expires: number
}

export function useMapboxToken() {
  const [token, setToken] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    let isMounted = true

    async function fetchToken() {
      try {
        setLoading(true)
        const response = await fetch("/api/mapbox-token")

        if (!response.ok) {
          throw new Error(`Failed to fetch Mapbox token: ${response.status}`)
        }

        const data: MapboxToken = await response.json()

        if (isMounted) {
          setToken(data.token)
          setError(null)

          // Set up token refresh before expiration
          const refreshTime = data.expires - Date.now() - 60000 // 1 minute before expiration
          if (refreshTime > 0) {
            const timeoutId = setTimeout(fetchToken, refreshTime)
            return () => clearTimeout(timeoutId)
          }
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error(String(err)))
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    fetchToken()

    return () => {
      isMounted = false
    }
  }, [])

  return { token, loading, error }
}
