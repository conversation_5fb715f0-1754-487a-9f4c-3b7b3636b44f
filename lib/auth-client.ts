/**
 * Client-side authentication utilities with JWT support
 */

export interface AuthResponse {
  user?: any
  session?: any
  error?: string
}

export interface RefreshResponse {
  success: boolean
  error?: string
}

/**
 * Sign in with email and password using JWT API
 */
export async function signInWithJWT(email: string, password: string): Promise<AuthResponse> {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
      credentials: 'include', // Include cookies
    })

    const data = await response.json()

    if (!response.ok) {
      return { error: data.error || 'Login failed' }
    }

    return {
      user: data.user,
      session: data.session,
    }
  } catch (error) {
    console.error('Login error:', error)
    return { error: 'Network error occurred' }
  }
}

/**
 * Sign out using JWT API
 */
export async function signOutWithJWT(): Promise<{ error?: string }> {
  try {
    const response = await fetch('/api/auth/logout', {
      method: 'POST',
      credentials: 'include',
    })

    const data = await response.json()

    if (!response.ok) {
      console.error('Logout error:', data.error)
    }

    return {}
  } catch (error) {
    console.error('Logout error:', error)
    return { error: 'Network error occurred' }
  }
}

/**
 * Refresh authentication token
 */
export async function refreshAuthToken(): Promise<RefreshResponse> {
  try {
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      credentials: 'include',
    })

    const data = await response.json()

    if (!response.ok) {
      return { success: false, error: data.error || 'Token refresh failed' }
    }

    return { success: true }
  } catch (error) {
    console.error('Token refresh error:', error)
    return { success: false, error: 'Network error occurred' }
  }
}

/**
 * Get current user information
 */
export async function getCurrentUser(): Promise<AuthResponse> {
  try {
    const response = await fetch('/api/auth/me', {
      method: 'GET',
      credentials: 'include',
    })

    const data = await response.json()

    if (!response.ok) {
      return { error: data.error || 'Failed to get user' }
    }

    return {
      user: data.user,
      profile: data.profile,
    }
  } catch (error) {
    console.error('Get user error:', error)
    return { error: 'Network error occurred' }
  }
}

/**
 * Auto-refresh token when needed
 */
export function setupAutoTokenRefresh() {
  // Check for refresh header from middleware
  const observer = new MutationObserver(() => {
    const refreshHeader = document.querySelector('meta[name="x-refresh-token"]')
    if (refreshHeader) {
      refreshAuthToken().then((result) => {
        if (!result.success) {
          console.error('Auto token refresh failed:', result.error)
          // Optionally redirect to login
          window.location.href = '/login'
        }
      })
      refreshHeader.remove()
    }
  })

  observer.observe(document.head, { childList: true })

  // Also set up periodic refresh (every 10 minutes)
  const refreshInterval = setInterval(async () => {
    const result = await refreshAuthToken()
    if (!result.success) {
      console.log('Token refresh not needed or failed')
      // Don't clear interval here as it might just be that no refresh was needed
    }
  }, 10 * 60 * 1000) // 10 minutes

  return () => {
    observer.disconnect()
    clearInterval(refreshInterval)
  }
}

/**
 * Check if user is authenticated by trying to get current user
 */
export async function checkAuthStatus(): Promise<boolean> {
  const result = await getCurrentUser()
  return !result.error
}

/**
 * Handle authentication errors globally
 */
export function handleAuthError(error: string) {
  if (error.includes('Authentication required') || error.includes('Not authenticated')) {
    // Redirect to login
    window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname)
  }
}

/**
 * Enhanced fetch with automatic token refresh
 */
export async function authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
  const response = await fetch(url, {
    ...options,
    credentials: 'include',
  })

  // If we get a 401, try to refresh the token once
  if (response.status === 401) {
    const refreshResult = await refreshAuthToken()
    
    if (refreshResult.success) {
      // Retry the original request
      return fetch(url, {
        ...options,
        credentials: 'include',
      })
    } else {
      // Refresh failed, redirect to login
      handleAuthError('Authentication required')
    }
  }

  return response
}
