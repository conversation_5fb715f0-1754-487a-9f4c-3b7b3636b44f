/**
 * Perplexity AI API Client
 * Handles communication with Perplexity API for chat functionality
 */

interface PerplexityMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface PerplexityResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    finish_reason: string;
    message: {
      role: string;
      content: string;
    };
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class PerplexityClient {
  private apiKey: string;
  private baseUrl = 'https://api.perplexity.ai';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async chat(messages: PerplexityMessage[], model = 'llama-3.1-sonar-small-128k-online'): Promise<PerplexityResponse> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages,
        max_tokens: 1000,
        temperature: 0.2,
        top_p: 0.9,
        return_citations: true,
        search_domain_filter: ["fuse.vip"],
        search_recency_filter: "month",
        top_k: 0,
        stream: false,
        presence_penalty: 0,
        frequency_penalty: 1
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Perplexity API error: ${response.status} - ${error}`);
    }

    return response.json();
  }

  async streamChat(messages: PerplexityMessage[], model = 'llama-3.1-sonar-small-128k-online'): Promise<ReadableStream> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages,
        max_tokens: 1000,
        temperature: 0.2,
        top_p: 0.9,
        return_citations: true,
        search_domain_filter: ["fuse.vip"],
        search_recency_filter: "month",
        top_k: 0,
        stream: true,
        presence_penalty: 0,
        frequency_penalty: 1
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Perplexity API error: ${response.status} - ${error}`);
    }

    return response.body!;
  }
}

// Rate limiting utility
const rateLimitMap = new Map<string, { count: number; timestamp: number }>();
const RATE_LIMIT = 10; // requests per minute
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute in milliseconds

export function checkRateLimit(identifier: string): boolean {
  const now = Date.now();
  const userLimit = rateLimitMap.get(identifier) || { count: 0, timestamp: now };

  // Reset counter if window has passed
  if (now - userLimit.timestamp > RATE_LIMIT_WINDOW) {
    userLimit.count = 0;
    userLimit.timestamp = now;
  }

  userLimit.count++;
  rateLimitMap.set(identifier, userLimit);

  return userLimit.count <= RATE_LIMIT;
}

export function getRateLimitStatus(identifier: string): { remaining: number; resetTime: number } {
  const userLimit = rateLimitMap.get(identifier) || { count: 0, timestamp: Date.now() };
  const remaining = Math.max(0, RATE_LIMIT - userLimit.count);
  const resetTime = userLimit.timestamp + RATE_LIMIT_WINDOW;
  
  return { remaining, resetTime };
}

// Utility to clean up old rate limit entries
export function cleanupRateLimit(): void {
  const now = Date.now();
  for (const [key, value] of rateLimitMap.entries()) {
    if (now - value.timestamp > RATE_LIMIT_WINDOW) {
      rateLimitMap.delete(key);
    }
  }
}

// Auto cleanup every 5 minutes
setInterval(cleanupRateLimit, 5 * 60 * 1000);
