// This file provides a unified way to access environment variables
// regardless of whether we're in a Vite or Next.js environment

// Client-safe environment variables
export const clientEnv = {
  // Supabase
  SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.VITE_SUPABASE_URL || "",
  SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY || "",

  // Stripe
  STRIPE_PUBLISHABLE_KEY:
    process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || process.env.VITE_STRIPE_PUBLISHABLE_KEY || "",
}

// Server-only environment variables - DO NOT IMPORT THIS IN CLIENT COMPONENTS
export const serverEnv = {
  // Mapbox - server-only
  MAPBOX_ACCESS_TOKEN: process.env.MAPBOX_ACCESS_TOKEN || "",

  // Xaman - server-only
  XAMAN_API_KEY: process.env.XAMAN_API_KEY || "",
}

// For backward compatibility - ONLY USE IN SERVER COMPONENTS
export const env = {
  ...clientEnv,
  // These are intentionally not included to prevent client exposure
  // MAPBOX_ACCESS_TOKEN: serverEnv.MAPBOX_ACCESS_TOKEN,
  // XAMAN_API_KEY: serverEnv.XAMAN_API_KEY,
}

// Helper function to check if required environment variables are set
export function checkRequiredEnvVars(requiredVars: string[], envObject = clientEnv) {
  const missing = requiredVars.filter((key) => !envObject[key as keyof typeof envObject])

  if (missing.length > 0) {
    console.warn(`Missing required environment variables: ${missing.join(", ")}`)
    return false
  }

  return true
}

// Helper to validate a URL string
export function isValidUrl(urlString: string): boolean {
  try {
    new URL(urlString)
    return true
  } catch (err) {
    return false
  }
}
