import { createClient } from '@supabase/supabase-js';

// Create a Supabase client for client-side database access
export function getSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
  
  if (!supabaseUrl || !supabaseAnonKey) {
    console.error("Missing Supabase environment variables");
    return null;
  }
  
  return createClient(supabaseUrl, supabaseAnonKey);
}

// Use this function in client components instead of direct pg connections
export async function executeQuery(query: string, params?: any[]) {
  const supabase = getSupabaseClient();
  if (!supabase) {
    throw new Error("Supabase client not initialized");
  }
  
  // Use Supabase's RPC function to execute raw SQL
  // Note: You need to create a PostgreSQL function that can execute the query
  const { data, error } = await supabase.rpc('execute_query', {
    query_text: query,
    query_params: params
  });
  
  if (error) throw error;
  return data;
}

// For user session data, use Supabase auth instead of direct DB queries
export async function getUserData(userId: string) {
  const supabase = getSupabaseClient();
  if (!supabase) {
    throw new Error("Supabase client not initialized");
  }
  
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
    
  if (error) throw error;
  return data;
}
