/**
 * Server-side authentication utilities for Next.js pages and API routes
 */

import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { createClient, getUserProfile, getUserSession } from '@/lib/supabase/server'
import type { JWTPayload } from '@/lib/jwt'

export interface AuthUser {
  id: string
  email: string
  role?: string
}

export interface AuthResult {
  user: AuthUser | null
  profile: any | null
  isAuthenticated: boolean
}

/**
 * Get authenticated user for server components
 */
export async function getServerAuth(): Promise<AuthResult> {
  try {
    // Get Supabase session directly
    const { session } = await getUserSession()
    const jwtUser = session?.user

    if (jwtUser) {
      const profile = await getUserProfile(jwtUser.id)

      return {
        user: {
          id: jwtUser.id,
          email: jwtUser.email,
          role: profile?.role || 'user',
        },
        profile,
        isAuthenticated: true,
      }
    }


    return {
      user: null,
      profile: null,
      isAuthenticated: false,
    }
  } catch (error) {
    console.error('Server auth error:', error)
    return {
      user: null,
      profile: null,
      isAuthenticated: false,
    }
  }
}

/**
 * Require authentication for server components
 * Redirects to login if not authenticated
 */
export async function requireAuth(redirectTo?: string): Promise<AuthResult> {
  const auth = await getServerAuth()
  
  if (!auth.isAuthenticated) {
    const loginUrl = redirectTo 
      ? `/login?redirect=${encodeURIComponent(redirectTo)}`
      : '/login'
    redirect(loginUrl)
  }
  
  return auth
}

/**
 * Require specific role for server components
 */
export async function requireRole(role: string, redirectTo?: string): Promise<AuthResult> {
  const auth = await requireAuth(redirectTo)
  
  if (auth.user?.role !== role) {
    redirect('/dashboard') // Redirect to dashboard if insufficient permissions
  }
  
  return auth
}

/**
 * Require admin access for server components
 */
export async function requireAdmin(redirectTo?: string): Promise<AuthResult> {
  return requireRole('admin', redirectTo)
}

/**
 * Check if user is business owner
 */
export async function isBusinessOwner(userId: string): Promise<boolean> {
  try {
    const { createClient } = await import('@/lib/supabase/server')
    const supabase = await createClient()

    const { data, error } = await supabase
      .from('businesses')
      .select('id')
      .eq('user_id', userId)
      .single()

    return !!data && !error
  } catch (error) {
    console.error('Error checking business owner status:', error)
    return false
  }
}

/**
 * Require business owner access
 */
export async function requireBusinessOwner(redirectTo?: string): Promise<AuthResult> {
  const auth = await requireAuth(redirectTo)
  
  const isBizOwner = await isBusinessOwner(auth.user!.id)
  
  if (!isBizOwner) {
    redirect('/dashboard')
  }
  
  return auth
}

/**
 * Get user roles from database
 */
export async function getUserRoles(userId: string): Promise<string[]> {
  try {
    const { createClient } = await import('@/lib/supabase/server')
    const supabase = await createClient()

    const { data, error } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', userId)

    if (error) {
      console.error('Error fetching user roles:', error)
      return []
    }

    return data.map(item => item.role)
  } catch (error) {
    console.error('Error fetching user roles:', error)
    return []
  }
}

/**
 * Check if user has specific role
 */
export async function hasRole(userId: string, role: string): Promise<boolean> {
  const roles = await getUserRoles(userId)
  return roles.includes(role)
}

/**
 * Middleware helper for API routes
 */
export async function withAuth<T>(
  handler: (auth: AuthResult) => Promise<T>,
  options: { requireAuth?: boolean; requiredRole?: string } = {}
): Promise<T | Response> {
  try {
    const auth = await getServerAuth()
    
    if (options.requireAuth && !auth.isAuthenticated) {
      return new Response(
        JSON.stringify({ error: 'Authentication required' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }
    
    if (options.requiredRole && auth.user?.role !== options.requiredRole) {
      return new Response(
        JSON.stringify({ error: 'Insufficient permissions' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      )
    }
    
    return await handler(auth)
  } catch (error) {
    console.error('Auth middleware error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
