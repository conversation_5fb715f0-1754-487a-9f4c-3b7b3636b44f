import { SignJWT, jwtVerify } from 'jose'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// JWT Configuration
const JWT_SECRET = new TextEncoder().encode(
  process.env.SUPABASE_JWT_SECRET || process.env.JWT_SECRET || 'your-secret-key'
)
const JWT_ALGORITHM = 'HS256'

// Token expiration times
export const ACCESS_TOKEN_EXPIRES_IN = '15m' // 15 minutes
export const REFRESH_TOKEN_EXPIRES_IN = '7d' // 7 days

// <PERSON>ie names
export const ACCESS_TOKEN_COOKIE = 'sb-access-token'
export const REFRESH_TOKEN_COOKIE = 'sb-refresh-token'

// JWT Payload interface
export interface JWTPayload {
  sub: string // user ID
  email: string
  role?: string
  iat?: number
  exp?: number
  aud?: string
  iss?: string
}

/**
 * Create a JWT token
 */
export async function createJWT(payload: JWTPayload, expiresIn: string = ACCESS_TOKEN_EXPIRES_IN): Promise<string> {
  const jwt = await new SignJWT(payload)
    .setProtectedHeader({ alg: JWT_ALGORITHM })
    .setIssuedAt()
    .setExpirationTime(expiresIn)
    .setAudience('authenticated')
    .setIssuer('supabase')
    .sign(JWT_SECRET)

  return jwt
}

/**
 * Verify and decode a JWT token
 */
export async function verifyJWT(token: string): Promise<JWTPayload | null> {
  try {
    if (!token || typeof token !== 'string') {
      console.error('Invalid token format:', token)
      return null
    }
    
    const { payload } = await jwtVerify(token, JWT_SECRET, {
      algorithms: [JWT_ALGORITHM],
      audience: 'authenticated',
      issuer: 'supabase',
    })

    return payload as JWTPayload
  } catch (error) {
    console.error('JWT verification failed:', error)
    return null
  }
}

/**
 * Get JWT token from cookies
 */
export async function getTokenFromCookies(cookieStore: ReturnType<typeof cookies>, tokenType: 'access' | 'refresh' = 'access'): Promise<string | null> {
  const cookieName = tokenType === 'access' ? ACCESS_TOKEN_COOKIE : REFRESH_TOKEN_COOKIE
  try {
    // In Next.js App Router, cookies() doesn't need to be awaited, but the get() method should be used carefully
    // We'll handle it defensively to avoid runtime errors
    let token = null;
    try {
      token = cookieStore.get(cookieName);
    } catch (cookieError) {
      console.error(`Error accessing cookie ${cookieName}:`, cookieError);
    }
    return token?.value || null;
  } catch (error) {
    console.error(`Error getting ${tokenType} token from cookies:`, error);
    return null;
  }
}

/**
 * Get JWT token from request headers or cookies
 */
export function getTokenFromRequest(request: NextRequest): string | null {
  // First, try to get from Authorization header
  const authHeader = request.headers.get('Authorization')
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }

  // Then try to get from cookies
  const token = request.cookies.get(ACCESS_TOKEN_COOKIE)
  return token?.value || null
}

/**
 * Set JWT tokens in cookies
 */
export function setTokenCookies(response: NextResponse, accessToken: string, refreshToken?: string) {
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax' as const,
    path: '/',
  }

  // Set access token cookie
  response.cookies.set(ACCESS_TOKEN_COOKIE, accessToken, {
    ...cookieOptions,
    maxAge: 15 * 60, // 15 minutes
  })

  // Set refresh token cookie if provided
  if (refreshToken) {
    response.cookies.set(REFRESH_TOKEN_COOKIE, refreshToken, {
      ...cookieOptions,
      maxAge: 7 * 24 * 60 * 60, // 7 days
    })
  }
}

/**
 * Clear JWT tokens from cookies
 */
export function clearTokenCookies(response: NextResponse) {
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax' as const,
    path: '/',
    maxAge: 0,
  }

  response.cookies.set(ACCESS_TOKEN_COOKIE, '', cookieOptions)
  response.cookies.set(REFRESH_TOKEN_COOKIE, '', cookieOptions)
}

/**
 * Check if a token is expired
 */
export function isTokenExpired(payload: JWTPayload): boolean {
  if (!payload.exp) return true
  return Date.now() >= payload.exp * 1000
}

/**
 * Get time until token expires (in seconds)
 */
export function getTokenExpiryTime(payload: JWTPayload): number {
  if (!payload.exp) return 0
  return Math.max(0, payload.exp - Math.floor(Date.now() / 1000))
}

/**
 * Create tokens from Supabase session
 */
export async function createTokensFromSupabaseSession(session: any): Promise<{ accessToken: string; refreshToken: string }> {
  const payload: JWTPayload = {
    sub: session.user.id,
    email: session.user.email,
    role: session.user.role || 'authenticated',
  }

  const accessToken = await createJWT(payload, ACCESS_TOKEN_EXPIRES_IN)
  const refreshToken = await createJWT(payload, REFRESH_TOKEN_EXPIRES_IN)

  return { accessToken, refreshToken }
}
