import { createClient } from "@supabase/supabase-js"

let supabaseInstance: ReturnType<typeof createClient> | null = null

// Initialize Supabase client with environment variables
export function getSupabaseClient() {
  if (supabaseInstance) return supabaseInstance

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ""
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ""

  if (!supabaseUrl || !supabaseAnonKey) {
    console.warn("Supabase environment variables are missing.")
    return null
  }

  try {
    new URL(supabaseUrl)
    supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
      },
    })
    return supabaseInstance
  } catch (error) {
    console.error("Invalid Supabase URL:", error)
    return null
  }
}

// Export default client if on the browser
export const supabase = typeof window !== "undefined" ? getSupabaseClient() : undefined

// --------- AUTH HELPERS ---------
export async function signInWithEmail(email: string, password: string) {
  const client = getSupabaseClient()
  if (!client) throw new Error("Supabase client not initialized")
  return client.auth.signInWithPassword({ email, password })
}

export async function signUpWithEmail(email: string, password: string) {
  const client = getSupabaseClient()
  if (!client) throw new Error("Supabase client not initialized")

  // Get the current origin for the redirect URL
  const origin = typeof window !== 'undefined' ? window.location.origin : 'https://fuse.vip'

  // Call Supabase's signUp method with a redirect back to our app
  return client.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${origin}/onboarding?confirmed=true`,
      data: {
        confirmed_at: new Date().toISOString(),
      }
    }
  })
}

export async function signOut() {
  const client = getSupabaseClient()
  if (!client) throw new Error("Supabase client not initialized")
  return client.auth.signOut()
}

export async function getSession() {
  const client = getSupabaseClient()
  if (!client) return { data: { session: null } }
  return client.auth.getSession()
}

export function onAuthStateChange(callback: (event: string, session: any) => void) {
  const client = getSupabaseClient()
  if (!client) return { data: { subscription: { unsubscribe: () => { } } } }
  return client.auth.onAuthStateChange(callback)
}

// Add updateSession function to refresh the user's session
export async function updateSession() {
  const client = getSupabaseClient()
  if (!client) throw new Error("Supabase client not initialized")

  try {
    const { data, error } = await client.auth.refreshSession()
    if (error) {
      console.error("Error refreshing session:", error)
      return { data: null, error }
    }
    return { data, error: null }
  } catch (error) {
    console.error("Exception refreshing session:", error)
    return { data: null, error }
  }
}

// Add a function to refresh the session
export async function refreshSession() {
  const client = getSupabaseClient()
  if (!client) throw new Error("Supabase client not initialized")

  try {
    return await client.auth.refreshSession()
  } catch (error) {
    console.error("Error refreshing session:", error)
    return { data: null, error }
  }
}

// --------- PROFILE HELPERS ---------
export async function getUserProfile(userId: string) {
  const client = getSupabaseClient()
  if (!client || !userId) return null

  const { data, error } = await client.from("profiles").select("*").eq('id', userId).single()
  if (error) {
    console.error("Error fetching profile:", error)
    return null
  }
  return data
}




// --------- BUSINESS HELPERS ---------
export async function isBusinessOwner(userId: string): Promise<boolean> {
  const client = getSupabaseClient()
  if (!client || !userId) return false

  const { data, error } = await client.from("businesses").select("id").eq("user_id", userId).maybeSingle()
  return !!data && !error
}

export async function getApprovedBusinesses() {
  const client = getSupabaseClient()
  if (!client) return []

  const { data, error } = await client
    .from("businesses")
    .select("id, name, category")
    .eq("status", "approved")
    .order("name", { ascending: true })

  if (error) {
    console.error("Error fetching approved businesses:", error)
    return []
  }

  return data
}

// --------- ADMIN HELPERS ---------
export async function getAdminApplications() {
  const client = getSupabaseClient()
  if (!client) return []

  const { data, error } = await client
    .from("network_applications")
    .select("*")
    .order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching network applications:", error)
    return []
  }

  return data
}

export async function updateAdminApplication(applicationId: string, status: string, reviewedBy: string) {
  const client = getSupabaseClient()
  if (!client) return false

  const { error } = await client
    .from("network_applications")
    .update({
      status,
      reviewed_by: reviewedBy,
      reviewed_at: new Date().toISOString(),
    })
    .eq("id", applicationId)

  if (error) {
    console.error("Error updating network application:", error)
    return false
  }

  return true
}

// --------- PURCHASE & REFERRAL HELPERS ---------
export async function getUserCards(userId: string) {
  const client = getSupabaseClient()
  if (!client) return []

  const { data, error } = await client.from("user_cards").select("*").eq("user_id", userId)
  if (error) {
    console.error("Error fetching user cards:", error)
    return []
  }
  return data
}

export async function getBusinessPurchases(businessId: string) {
  const client = getSupabaseClient()
  if (!client) return []

  const { data, error } = await client
    .from("purchases")
    .select(`
      *,
       (
        first_name,
        last_name,
        email
      )
    `)
    .eq("business_id", businessId)
    .order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching purchases:", error)
    return []
  }

  // Process the data to include user information
  return data.map(purchase => ({
    ...purchase,
    customer_name: purchase.profiles ?
      `${purchase.profiles.first_name || ''} ${purchase.profiles.last_name || ''}`.trim() :
      purchase.customer_name || '',
    user_email: purchase.profiles?.email || purchase.user_email || '',
  }))
}

export async function getBusinessReferrals(businessId: string) {
  const client = getSupabaseClient()
  if (!client) return []

  const { data, error } = await client
    .from("referrals")
    .select(`
      *,
      referrer:referrer_id (
        first_name,
        last_name,
        email
      ),
      referred:referred_id (
        first_name,
        last_name,
        email
      )
    `)
    .eq("business_id", businessId)
    .order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching referrals:", error)
    return []
  }

  // Process the data to include user information
  return data.map(referral => ({
    ...referral,
    referrer_name: referral.referrer ?
      `${referral.referrer.first_name || ''} ${referral.referrer.last_name || ''}`.trim() :
      referral.referrer_name || '',
    referrer_email: referral.referrer?.email || referral.referrer_email || '',

    referred_name: referral.referred ?
      `${referral.referred.first_name || ''} ${referral.referred.last_name || ''}`.trim() :
      referral.referred_name || '',
    referred_email: referral.referred?.email || referral.referred_email || '',
  }))
}
