import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from "next/headers"
import { NextRequest, NextResponse } from 'next/server'
import { verifyJWT, getTokenFromCookies, type JWTPayload } from '@/lib/jwt'

export const createClient = async () => {
  // Check if environment variables are defined
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    throw new Error('Supabase environment variables are missing')
  }

  const cookieStore = await cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => cookieStore.set(name, value, options))
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    },
  )
}

/**
 * Create Supabase client for middleware
 */
export const createMiddlewareClient = (request: NextRequest, response: NextResponse) => {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            response.cookies.set(name, value, options)
          )
        },
      },
    },
  )
}

/**
 * Get authenticated user from JWT token
 */
export async function getAuthenticatedUser(): Promise<JWTPayload | null> {
  const token = await getTokenFromCookies(cookies(), 'access')
  if (!token) return null

  return await verifyJWT(token)
}

/**
 * Get user session with enhanced error handling
 */
export async function getUserSession() {
  try {
    const supabase = await createClient()
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error) {
      console.error('Error getting session:', error)
      return { session: null, error }
    }

    return { session, error: null }
  } catch (error) {
    console.error('Exception getting session:', error)
    return { session: null, error }
  }
}

/**
 * Get user profile with caching
 */
export async function getUserProfile(userId: string) {
  try {
    const supabase = await createClient()
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Error fetching profile:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Exception fetching profile:', error)
    return null
  }
}
