import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from "next/headers"
import { NextRequest, NextResponse } from 'next/server'
import { clientEnv } from '@/lib/env'

// Validate environment variables
function validateEnv() {
  if (!clientEnv.SUPABASE_URL || !clientEnv.SUPABASE_ANON_KEY) {
    throw new Error('Supabase environment variables are missing')
  }
}

/**
 * Create Supabase client for server components
 */
export const createClient = async () => {
  validateEnv()
  const cookieStore = cookies()

  return createServerClient(
    clientEnv.SUPABASE_URL,
    clientEnv.SUPABASE_ANON_KEY,
    {
      cookies: {
        get(name: string) {
          try {
            return cookieStore.get(name)?.value
          } catch (error) {
            console.error(`Error getting cookie ${name}:`, error)
            return undefined
          }
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options)
          } catch (error) {
            // Handle cookies in server components
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', { ...options, maxAge: 0 })
          } catch (error) {
            // Handle cookies in server components
          }
        },
      },
    },
  )
}

/**
 * Create Supabase client for middleware
 */
export const createMiddlewareClient = (request: NextRequest, response: NextResponse) => {
  validateEnv()
  
  return createServerClient(
    clientEnv.SUPABASE_URL,
    clientEnv.SUPABASE_ANON_KEY,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          request.cookies.set(name, value)
          response.cookies.set(name, value, options)
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set(name, '')
          response.cookies.set(name, '', { ...options, maxAge: 0 })
        },
      },
    },
  )
}

/**
 * Create admin client with service role key
 * ONLY USE IN SERVER CONTEXTS (API routes, Server Actions)
 */
export function createAdminClient() {
  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is missing')
  }

  return createServerClient(
    clientEnv.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      cookies: {}
    }
  )
}

/**
 * Get user session with enhanced error handling
 */
export async function getUserSession() {
  try {
    const supabase = await createClient()
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error) {
      console.error('Error getting session:', error)
      return { session: null, error }
    }

    return { session, error: null }
  } catch (error) {
    console.error('Exception getting session:', error)
    return { session: null, error }
  }
}

/**
 * Get user profile with caching
 */
export async function getUserProfile(userId: string) {
  try {
    const supabase = await createClient()
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Error fetching profile:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Exception fetching profile:', error)
    return null
  }
}
