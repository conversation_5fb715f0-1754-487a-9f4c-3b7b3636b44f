import { serve } from 'https://deno.land/std@0.131.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.0.0';
const supabaseAdmin = createClient(Deno.env.get('APP_SUPABASE_URL'), Deno.env.get('SERVICE_ROLE_KEY'));
async function isAdmin(userId) {
  const { data, error } = await supabaseAdmin.from('user_roles').select(`
      role_id,
      portal_roles!inner (
        role_name
      )
    `).eq('user_id', userId).eq('portal_roles.role_name', 'admin').is('revoked_at', null).eq('is_active', true).single();
  if (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
  return !!data;
}
serve(async (req)=>{
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    });
  }
  try {
    // Get the code from request body
    const { code } = await req.json();
    // Get user ID from JWT
    const authHeader = req.headers.get('Authorization');
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);
    if (userError || !user) {
      return new Response(JSON.stringify({
        error: 'Unauthorized'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    // Start a transaction
    const { data, error } = await supabaseAdmin.rpc('redeem_vip_code', {
      p_code: code,
      p_user_id: user.id
    });
    if (error) {
      throw new Error(error.message);
    }
    // Update user profile with membership dates
    const now = new Date();
    const oneYearFromNow = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
    const { error: updateError } = await supabaseAdmin.from('profiles').update({
      is_card_holder: true,
      membership_start_date: now.toISOString(),
      membership_end_date: oneYearFromNow.toISOString()
    }).eq('id', user.id);
    if (updateError) {
      throw new Error('Failed to update membership dates: ' + updateError.message);
    }
    return new Response(JSON.stringify({
      success: true
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      status: 200
    });
  } catch (err) {
    return new Response(JSON.stringify({
      error: err.message
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      status: 400
    });
  }
});
