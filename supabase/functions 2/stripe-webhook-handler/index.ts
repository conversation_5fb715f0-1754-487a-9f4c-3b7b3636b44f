import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@12.5.0?target=deno";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.0.0";
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY'), {
  apiVersion: '2022-11-15',
  httpClient: Stripe.createFetchHttpClient()
});
const endpointSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET');
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': '*'
};
serve(async (req)=>{
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  try {
    const body = await req.text();
    const sig = req.headers.get('stripe-signature');
    if (!sig || !endpointSecret) {
      throw new Error('Missing signature or endpoint secret');
    }
    let event;
    try {
      event = await stripe.webhooks.constructEventAsync(body, sig, endpointSecret, undefined, Stripe.createSubtleCryptoProvider());
    } catch (err) {
      console.error('⚠️ Webhook signature verification failed:', err.message);
      return new Response(JSON.stringify({
        error: 'Webhook signature verification failed'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    const supabaseAdmin = createClient(Deno.env.get('APP_SUPABASE_URL'), Deno.env.get('SERVICE_ROLE_KEY'));
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object;
      // Add additional logging
      console.log('Processing webhook for session ID:', session.id);
      console.log('Payment intent:', session.payment_intent);
      console.log('Client reference ID:', session.client_reference_id);
      console.log('Customer email:', session.customer_email);
      console.log('Metadata:', session.metadata);
      if (!session.client_reference_id) {
        throw new Error('No client_reference_id found in session');
      }
      // Check for existing purchase using session ID
      const { data: existingPurchase } = await supabaseAdmin.from('purchases').select().eq('metadata->checkout_session_id', session.id).single();
      if (existingPurchase) {
        console.log('Purchase already processed for session:', session.id);
        return new Response(JSON.stringify({
          received: true,
          status: 'duplicate'
        }), {
          status: 200,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        });
      }
      // Check if this is a guest purchase
      const isGuest = session.client_reference_id.startsWith('guest-') || session.metadata?.isGuest === 'true';
      let userId = session.client_reference_id;
      // For guest purchases, we need to either find or create a user
      if (isGuest && session.customer_email) {
        // Check if a user with this email already exists
        const { data: existingUser } = await supabaseAdmin.auth.admin.listUsers({
          filter: {
            email: session.customer_email
          }
        });
        if (existingUser && existingUser.users && existingUser.users.length > 0) {
          // Use existing user ID
          userId = existingUser.users[0].id;
          console.log('Found existing user for guest purchase:', userId);
        } else {
          // Create a new user with the provided email
          const { data: newUser, error: createUserError } = await supabaseAdmin.auth.admin.createUser({
            email: session.customer_email,
            email_confirm: true,
            user_metadata: {
              source: 'guest_purchase',
              checkout_session_id: session.id
            }
          });
          if (createUserError) {
            console.error('Failed to create user for guest purchase:', createUserError);
          // Continue with guest ID for now
          } else if (newUser) {
            userId = newUser.id;
            console.log('Created new user for guest purchase:', userId);
            // Create profile for the new user
            const { error: profileError } = await supabaseAdmin.from('profiles').insert({
              id: userId,
              email: session.customer_email,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
            if (profileError) {
              console.error('Failed to create profile for new user:', profileError);
            }
          }
        }
      }
      // Check if user is already a card holder (only for non-guest users)
      if (!isGuest) {
        const { data: profileData } = await supabaseAdmin.from('profiles').select('is_card_holder').eq('id', userId).single();
        if (profileData?.is_card_holder) {
          console.log('User is already a card holder:', userId);
          return new Response(JSON.stringify({
            received: true,
            status: 'already_member'
          }), {
            status: 200,
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            }
          });
        }
      }
      // Record the purchase
      const { error: purchaseError, data: purchaseResult } = await supabaseAdmin.from('purchases').insert({
        user_id: userId,
        method: 'stripe',
        amount: session.amount_total / 100,
        status: 'completed',
        transaction_id: session.payment_intent,
        metadata: {
          checkout_session_id: session.id,
          payment_status: session.payment_status,
          customer_email: session.customer_email,
          is_guest: isGuest
        }
      }).select().single();
      if (purchaseError) {
        throw new Error(`Failed to record purchase: ${purchaseError.message}`);
      }
      // Update user profile
      const { error: updateError } = await supabaseAdmin.from('profiles').update({
        is_card_holder: true,
        membership_start_date: new Date().toISOString(),
        membership_end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
      }).eq('id', userId);
      if (updateError) {
        console.error(`Failed to update profile: ${updateError.message}`);
      // Continue processing even if profile update fails
      }
      // After recording the purchase and updating profile, create referral
      await createReferral(supabaseAdmin, userId, purchaseResult.id);
      console.log('Successfully processed purchase for session:', session.id);
    }
    return new Response(JSON.stringify({
      received: true
    }), {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (err) {
    console.error('❌ Error:', err.message);
    return new Response(JSON.stringify({
      error: err.message
    }), {
      status: 400,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
