import { serve } from 'https://deno.land/std@0.131.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.0.0';
const supabaseAdmin = createClient(Deno.env.get('APP_SUPABASE_URL'), Deno.env.get('SERVICE_ROLE_KEY'));
function generateCode(length = 8) {
  const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789' // Excluding similar looking characters
  ;
  let result = '';
  for(let i = 0; i < length; i++){
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
serve(async (req)=>{
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    });
  }
  try {
    const { quantity = 1, expiresAt = null } = await req.json();
    // Verify admin status
    const authHeader = req.headers.get('Authorization');
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);
    if (userError || !user) {
      throw new Error('Unauthorized');
    }
    // Verify admin role (you'll need to set this up in your auth system)
    const { data: profile } = await supabaseAdmin.from('profiles').select('role').eq('id', user.id).single();
    if (profile?.role !== 'admin') {
      throw new Error('Unauthorized - Admin only');
    }
    // Generate codes
    const codes = [];
    for(let i = 0; i < quantity; i++){
      const code = generateCode();
      codes.push({
        code,
        created_by: user.id,
        expires_at: expiresAt
      });
    }
    // Insert codes
    const { data, error } = await supabaseAdmin.from('vip_codes').insert(codes).select();
    if (error) {
      throw error;
    }
    return new Response(JSON.stringify({
      codes: data
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      status: 200
    });
  } catch (err) {
    return new Response(JSON.stringify({
      error: err.message
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      status: 400
    });
  }
});
