import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};
serve(async (req)=>{
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    if (req.method !== 'POST') {
      throw new Error('Method not allowed');
    }
    const { applicationId, businessName, contactEmail, contactName } = await req.json();
    if (!applicationId || !businessName || !contactEmail) {
      throw new Error('Required fields missing');
    }
    const res = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('RESEND_API_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        from: 'Fuse Network <<EMAIL>>',
        to: contactEmail,
        subject: 'Your FUSE Network Application Has Been Approved!',
        html: `
          <p>Dear ${contactName || "Business Owner"},</p>
          <p>Congratulations! Your business, <strong>${businessName}</strong>, has been approved to join the FUSE Network.</p>
          <p>Welcome aboard! Now you can keep track of your Referral Rewards in the Business Portal</p>
          <p>Best regards,<br>The FUSE Team</p>
        `
      })
    });
    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(`Failed to send email: ${errorData.message}`);
    }
    return new Response(JSON.stringify({
      success: true,
      message: 'Email sent successfully'
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 200
    });
  } catch (error) {
    console.error('Error sending email:', error);
    return new Response(JSON.stringify({
      error: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 400
    });
  }
});
