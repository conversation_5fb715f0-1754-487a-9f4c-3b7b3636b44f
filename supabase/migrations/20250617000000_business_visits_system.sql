-- Add auto_created column to businesses table for tracking auto-created businesses
ALTER TABLE businesses ADD COLUMN IF NOT EXISTS auto_created BOOLEAN DEFAULT FALSE;

-- Create business_visits table for QR scanning rewards system
CREATE TABLE IF NOT EXISTS business_visits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  business_id UUID NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
  scanned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one visit per business per day per user
  CONSTRAINT business_visits_unique_daily UNIQUE (user_id, business_id, DATE(scanned_at))
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_business_visits_user_id ON business_visits(user_id);
CREATE INDEX IF NOT EXISTS idx_business_visits_business_id ON business_visits(business_id);
CREATE INDEX IF NOT EXISTS idx_business_visits_scanned_at ON business_visits(scanned_at);
CREATE INDEX IF NOT EXISTS idx_business_visits_user_business ON business_visits(user_id, business_id);

-- Create RLS policies for business_visits
ALTER TABLE business_visits ENABLE ROW LEVEL SECURITY;

-- Users can view their own visits
CREATE POLICY "Users can view their own business visits" ON business_visits
  FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own visits (handled by application logic)
CREATE POLICY "Users can insert their own business visits" ON business_visits
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Business owners can view visits to their businesses
CREATE POLICY "Business owners can view visits to their businesses" ON business_visits
  FOR SELECT USING (
    business_id IN (
      SELECT id FROM businesses WHERE user_id = auth.uid()
    )
  );

-- Admins can view all visits
CREATE POLICY "Admins can view all business visits" ON business_visits
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Create a function to get business visit statistics
CREATE OR REPLACE FUNCTION get_business_visit_stats(business_uuid UUID)
RETURNS TABLE (
  total_visits BIGINT,
  unique_visitors BIGINT,
  visits_this_week BIGINT,
  visits_this_month BIGINT,
  last_visit_date TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_visits,
    COUNT(DISTINCT user_id) as unique_visitors,
    COUNT(*) FILTER (WHERE scanned_at >= NOW() - INTERVAL '7 days') as visits_this_week,
    COUNT(*) FILTER (WHERE scanned_at >= NOW() - INTERVAL '30 days') as visits_this_month,
    MAX(scanned_at) as last_visit_date
  FROM business_visits 
  WHERE business_id = business_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get user's total FUSE earned
CREATE OR REPLACE FUNCTION get_user_fuse_earned(user_uuid UUID)
RETURNS TABLE (
  total_visits BIGINT,
  total_fuse_earned BIGINT,
  visits_this_week BIGINT,
  visits_this_month BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_visits,
    COUNT(*) * 100 as total_fuse_earned, -- 100 FUSE per visit
    COUNT(*) FILTER (WHERE scanned_at >= NOW() - INTERVAL '7 days') as visits_this_week,
    COUNT(*) FILTER (WHERE scanned_at >= NOW() - INTERVAL '30 days') as visits_this_month
  FROM business_visits 
  WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if user can scan a business (cooldown check)
CREATE OR REPLACE FUNCTION can_scan_business(user_uuid UUID, business_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
  last_scan_today TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Check if user has already scanned this business today
  SELECT scanned_at INTO last_scan_today
  FROM business_visits
  WHERE user_id = user_uuid 
    AND business_id = business_uuid
    AND DATE(scanned_at) = CURRENT_DATE
  LIMIT 1;
  
  -- Return true if no scan today, false if already scanned
  RETURN last_scan_today IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a view for business visit analytics
CREATE OR REPLACE VIEW business_visit_analytics AS
SELECT 
  b.id as business_id,
  b.name as business_name,
  b.category as business_category,
  b.user_id as business_owner_id,
  COUNT(bv.id) as total_visits,
  COUNT(DISTINCT bv.user_id) as unique_visitors,
  COUNT(bv.id) FILTER (WHERE bv.scanned_at >= NOW() - INTERVAL '7 days') as visits_this_week,
  COUNT(bv.id) FILTER (WHERE bv.scanned_at >= NOW() - INTERVAL '30 days') as visits_this_month,
  COUNT(bv.id) * 100 as total_fuse_distributed,
  MAX(bv.scanned_at) as last_visit_date,
  MIN(bv.scanned_at) as first_visit_date
FROM businesses b
LEFT JOIN business_visits bv ON b.id = bv.business_id
WHERE b.status = 'approved'
GROUP BY b.id, b.name, b.category, b.user_id
ORDER BY total_visits DESC;

-- Grant permissions for the view
GRANT SELECT ON business_visit_analytics TO authenticated;

-- Create RLS policy for the view
ALTER VIEW business_visit_analytics SET (security_barrier = true);

-- Comments for documentation
COMMENT ON TABLE business_visits IS 'Tracks QR code scans at businesses for FUSE reward distribution';
COMMENT ON FUNCTION get_business_visit_stats IS 'Returns visit statistics for a specific business';
COMMENT ON FUNCTION get_user_fuse_earned IS 'Returns total FUSE earned by a user from business visits';
COMMENT ON FUNCTION can_scan_business IS 'Checks if a user can scan a business QR code (cooldown validation)';
COMMENT ON VIEW business_visit_analytics IS 'Aggregated analytics view for business visit data';

-- Insert some sample data for testing (optional - remove in production)
-- This will only work if there are existing businesses and users
/*
INSERT INTO business_visits (user_id, business_id, scanned_at) 
SELECT 
  u.id as user_id,
  b.id as business_id,
  NOW() - (random() * INTERVAL '30 days') as scanned_at
FROM auth.users u
CROSS JOIN businesses b
WHERE b.status = 'approved'
  AND EXISTS (SELECT 1 FROM profiles p WHERE p.id = u.id AND p.is_card_holder = true)
LIMIT 50
ON CONFLICT (user_id, business_id, DATE(scanned_at)) DO NOTHING;
*/
