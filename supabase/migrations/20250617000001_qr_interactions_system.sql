-- QR Interactions System Migration
-- This migration ensures the qr_interactions table is properly configured for the FUSE QR scanning rewards system
-- Note: businesses table uses is_active instead of status column

-- Ensure qr_interactions table exists with correct structure
CREATE TABLE IF NOT EXISTS qr_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scanner_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  scanned_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  scanned_business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
  interaction_type TEXT NOT NULL CHECK (interaction_type IN ('user_scan', 'business_scan')),
  qr_data TEXT,
  location_data JSONB,
  interaction_metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS qr_interactions_scanner_user_id_idx ON qr_interactions(scanner_user_id);
CREATE INDEX IF NOT EXISTS qr_interactions_scanned_business_id_idx ON qr_interactions(scanned_business_id);
CREATE INDEX IF NOT EXISTS qr_interactions_interaction_type_idx ON qr_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS qr_interactions_created_at_idx ON qr_interactions(created_at);
-- Create index for daily business scan lookups (without DATE function to avoid immutability issues)
CREATE INDEX IF NOT EXISTS qr_interactions_daily_business_scan_idx ON qr_interactions(scanner_user_id, scanned_business_id, created_at) WHERE interaction_type = 'business_scan';

-- Enable RLS
ALTER TABLE qr_interactions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own interactions" ON qr_interactions;
DROP POLICY IF EXISTS "Users can insert their own interactions" ON qr_interactions;
DROP POLICY IF EXISTS "Admins can view all interactions" ON qr_interactions;
DROP POLICY IF EXISTS "Business owners can view interactions" ON qr_interactions;

-- RLS Policies for qr_interactions
CREATE POLICY "Users can view their own interactions" ON qr_interactions
  FOR SELECT USING (
    auth.uid() = scanner_user_id OR 
    auth.uid() = scanned_user_id OR
    auth.uid() IN (
      SELECT user_id FROM businesses WHERE id = scanned_business_id
    )
  );

CREATE POLICY "Users can insert their own interactions" ON qr_interactions
  FOR INSERT WITH CHECK (auth.uid() = scanner_user_id);

CREATE POLICY "Admins can view all interactions" ON qr_interactions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM admin_users
      WHERE admin_users.id = auth.uid()
    )
  );

CREATE POLICY "Business owners can view interactions with their businesses" ON qr_interactions
  FOR SELECT USING (
    scanned_business_id IN (
      SELECT id FROM businesses WHERE user_id = auth.uid()
    )
  );

-- Create a view for business visit analytics
CREATE OR REPLACE VIEW business_visit_analytics AS
SELECT
  b.id as business_id,
  b.name as business_name,
  b.category,
  b.is_active,
  COUNT(qi.id) as total_scans,
  COUNT(DISTINCT qi.scanner_user_id) as unique_visitors,
  COUNT(CASE WHEN qi.created_at >= CURRENT_DATE THEN 1 END) as today_scans,
  COUNT(CASE WHEN qi.created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as week_scans,
  COUNT(CASE WHEN qi.created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as month_scans,
  SUM(CASE WHEN qi.interaction_metadata->>'fuse_tokens_awarded' IS NOT NULL 
           THEN (qi.interaction_metadata->>'fuse_tokens_awarded')::INTEGER 
           ELSE 0 END) as total_fuse_awarded
FROM businesses b
LEFT JOIN qr_interactions qi ON b.id = qi.scanned_business_id 
  AND qi.interaction_type = 'business_scan'
GROUP BY b.id, b.name, b.category, b.is_active;

-- Create a view for user rewards summary
CREATE OR REPLACE VIEW user_rewards_summary AS
SELECT 
  p.id as user_id,
  p.first_name,
  p.last_name,
  COUNT(qi.id) as total_business_scans,
  COUNT(DISTINCT qi.scanned_business_id) as unique_businesses_visited,
  SUM(CASE WHEN qi.interaction_metadata->>'fuse_tokens_awarded' IS NOT NULL 
           THEN (qi.interaction_metadata->>'fuse_tokens_awarded')::INTEGER 
           ELSE 0 END) as total_fuse_earned,
  COUNT(CASE WHEN qi.created_at >= CURRENT_DATE THEN 1 END) as today_scans,
  COUNT(CASE WHEN qi.created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as week_scans,
  COUNT(CASE WHEN qi.created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as month_scans
FROM profiles p
LEFT JOIN qr_interactions qi ON p.id = qi.scanner_user_id 
  AND qi.interaction_type = 'business_scan'
WHERE p.is_card_holder = true
GROUP BY p.id, p.first_name, p.last_name;

-- Grant permissions on views
GRANT SELECT ON business_visit_analytics TO authenticated;
GRANT SELECT ON user_rewards_summary TO authenticated;

-- Add RLS to views
ALTER VIEW business_visit_analytics SET (security_invoker = true);
ALTER VIEW user_rewards_summary SET (security_invoker = true);

-- Create function to check daily scan limit
CREATE OR REPLACE FUNCTION check_daily_business_scan_limit(
  p_scanner_user_id UUID,
  p_business_id UUID
) RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user has already scanned this business today
  -- Using date range instead of DATE() function for better performance
  RETURN NOT EXISTS (
    SELECT 1 FROM qr_interactions
    WHERE scanner_user_id = p_scanner_user_id
    AND scanned_business_id = p_business_id
    AND interaction_type = 'business_scan'
    AND created_at >= CURRENT_DATE::timestamp
    AND created_at < (CURRENT_DATE + INTERVAL '1 day')::timestamp
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on function
GRANT EXECUTE ON FUNCTION check_daily_business_scan_limit(UUID, UUID) TO authenticated;

-- Add comment explaining the system
COMMENT ON TABLE qr_interactions IS 'Stores all QR code scanning interactions including business visits that award FUSE tokens';
COMMENT ON COLUMN qr_interactions.interaction_metadata IS 'JSON metadata including fuse_tokens_awarded, business_name, scan_timestamp, etc.';
COMMENT ON VIEW business_visit_analytics IS 'Analytics view for business owners and admins to track QR scanning activity';
COMMENT ON VIEW user_rewards_summary IS 'Summary view for VIP cardholders to track their FUSE token earnings from business scans';
