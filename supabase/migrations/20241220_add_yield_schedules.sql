-- Add yield schedule support to yield pool deposits
ALTER TABLE yield_pool_deposits 
ADD COLUMN yield_schedule_id VARCHAR(20),
ADD COLUMN yield_schedule_name VARCHAR(50),
ADD COLUMN yield_schedule_duration INTEGER DEFAULT 7,
ADD COLUMN yield_schedule_apy DECIMAL(5,2) DEFAULT 5.0,
ADD COLUMN yield_schedule_penalty DECIMAL(5,2) DEFAULT 0.5,
ADD COLUMN deposit_date TIMESTAMP DEFAULT NOW(),
ADD COLUMN maturity_date TIMESTAMP,
ADD COLUMN early_withdrawal_penalty DECIMAL(10,6) DEFAULT 0;

-- Create index for better query performance
CREATE INDEX idx_yield_pool_deposits_schedule ON yield_pool_deposits(yield_schedule_id);
CREATE INDEX idx_yield_pool_deposits_maturity ON yield_pool_deposits(maturity_date);

-- Add grid bot trading metrics table
CREATE TABLE IF NOT EXISTS grid_bot_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    deposit_id UUID REFERENCES yield_pool_deposits(id) ON DELETE CASCADE,
    asset_type VARCHAR(10) NOT NULL,
    total_trades INTEGER DEFAULT 0,
    total_profit DECIMAL(18,8) DEFAULT 0,
    current_grid_levels INTEGER DEFAULT 20,
    active_buy_orders INTEGER DEFAULT 0,
    active_sell_orders INTEGER DEFAULT 0,
    last_trade_price DECIMAL(18,8),
    last_trade_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create RLS policies for grid bot metrics
ALTER TABLE grid_bot_metrics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own grid bot metrics" ON grid_bot_metrics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own grid bot metrics" ON grid_bot_metrics
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own grid bot metrics" ON grid_bot_metrics
    FOR UPDATE USING (auth.uid() = user_id);

-- Add grid bot trading activity log
CREATE TABLE IF NOT EXISTS grid_bot_activity (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    deposit_id UUID REFERENCES yield_pool_deposits(id) ON DELETE CASCADE,
    asset_type VARCHAR(10) NOT NULL,
    trade_type VARCHAR(10) NOT NULL, -- 'buy' or 'sell'
    price DECIMAL(18,8) NOT NULL,
    amount DECIMAL(18,8) NOT NULL,
    profit DECIMAL(18,8) DEFAULT 0,
    grid_level INTEGER,
    transaction_hash VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create RLS policies for grid bot activity
ALTER TABLE grid_bot_activity ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own grid bot activity" ON grid_bot_activity
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own grid bot activity" ON grid_bot_activity
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX idx_grid_bot_activity_user_date ON grid_bot_activity(user_id, created_at DESC);
CREATE INDEX idx_grid_bot_activity_deposit ON grid_bot_activity(deposit_id);

-- Update existing deposits to have default schedule (7-day flexible)
UPDATE yield_pool_deposits 
SET 
    yield_schedule_id = '7-day',
    yield_schedule_name = '7-Day Flexible',
    yield_schedule_duration = 7,
    yield_schedule_apy = 4.2,
    yield_schedule_penalty = 0.5,
    maturity_date = deposit_date + INTERVAL '7 days'
WHERE yield_schedule_id IS NULL;

-- Function to calculate early withdrawal penalty
CREATE OR REPLACE FUNCTION calculate_early_withdrawal_penalty(
    deposit_amount DECIMAL(18,8),
    penalty_rate DECIMAL(5,2),
    days_remaining INTEGER
) RETURNS DECIMAL(18,8) AS $$
BEGIN
    -- Calculate penalty based on remaining days and penalty rate
    -- More days remaining = higher penalty
    RETURN deposit_amount * (penalty_rate / 100) * (days_remaining / 30.0);
END;
$$ LANGUAGE plpgsql;

-- Function to update maturity date when schedule changes
CREATE OR REPLACE FUNCTION update_maturity_date()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.yield_schedule_duration IS NOT NULL THEN
        NEW.maturity_date = NEW.deposit_date + (NEW.yield_schedule_duration || ' days')::INTERVAL;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update maturity date
CREATE TRIGGER update_deposit_maturity_date
    BEFORE INSERT OR UPDATE ON yield_pool_deposits
    FOR EACH ROW
    EXECUTE FUNCTION update_maturity_date();

-- Add loyalty_reward_frequency column to network_applications table
-- This matches the same column in the businesses table
ALTER TABLE network_applications
ADD COLUMN loyalty_reward_frequency TEXT DEFAULT 'monthly';

-- Add comments for documentation
COMMENT ON COLUMN yield_pool_deposits.yield_schedule_id IS 'ID of the selected yield schedule (7-day, 30-day, 90-day)';
COMMENT ON COLUMN yield_pool_deposits.yield_schedule_apy IS 'Annual Percentage Yield for the selected schedule';
COMMENT ON COLUMN yield_pool_deposits.yield_schedule_penalty IS 'Early withdrawal penalty percentage';
COMMENT ON COLUMN yield_pool_deposits.maturity_date IS 'Date when the deposit matures without penalty';
COMMENT ON COLUMN network_applications.loyalty_reward_frequency IS 'Frequency for loyalty reward distribution (monthly, quarterly, annually)';
COMMENT ON TABLE grid_bot_metrics IS 'Tracks grid bot trading performance metrics for each deposit';
COMMENT ON TABLE grid_bot_activity IS 'Logs individual grid bot trades and activities';
