-- FUSE Token Gaming System Database Schema
-- Creates tables and functions for FUSE token-based gaming with trustline management

-- Create FUSE gaming sessions table
CREATE TABLE IF NOT EXISTS fuse_gaming_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  game_type TEXT NOT NULL CHECK (game_type IN ('fuse-bird', 'rock-paper-scissors', '2048')),
  session_type TEXT NOT NULL DEFAULT 'competitive' CHECK (session_type IN ('competitive')),
  entry_fee_fuse DECIMAL(18,8) NOT NULL DEFAULT 1.0,
  final_score INTEGER DEFAULT 0,
  game_duration_seconds INTEGER,
  moves_count INTEGER,
  highest_tile INTEGER,
  game_data JSONB,
  wallet_address TEXT NOT NULL,
  transaction_hash TEXT,
  fuse_transaction_verified BOOLEAN DEFAULT FALSE,
  trustline_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  started_at TIMESTAMP WITH TIME ZONE,
  ended_at TIMESTAMP WITH TIME ZONE,
  
  -- Indexes for performance
  CONSTRAINT fuse_gaming_sessions_user_game_idx UNIQUE (user_id, id)
);

-- Create FUSE trustlines tracking table
CREATE TABLE IF NOT EXISTS fuse_trustlines (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_address TEXT NOT NULL,
  fuse_issuer TEXT NOT NULL DEFAULT 'rs2G9J95qwL3yw241JTRdgms2hhcLouVHo',
  trustline_limit DECIMAL(18,8) DEFAULT 0,
  current_balance DECIMAL(18,8) DEFAULT 0,
  trustline_verified BOOLEAN DEFAULT FALSE,
  last_verified_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Unique constraint to prevent duplicates
  CONSTRAINT fuse_trustlines_user_wallet_unique UNIQUE (user_id, wallet_address)
);

-- Create FUSE gaming transactions table
CREATE TABLE IF NOT EXISTS fuse_gaming_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  gaming_session_id UUID NOT NULL REFERENCES fuse_gaming_sessions(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  transaction_type TEXT NOT NULL CHECK (transaction_type IN ('entry_fee', 'reward', 'refund')),
  fuse_amount DECIMAL(18,8) NOT NULL,
  from_wallet TEXT NOT NULL,
  to_wallet TEXT NOT NULL,
  transaction_hash TEXT,
  xrpl_verified BOOLEAN DEFAULT FALSE,
  verification_attempts INTEGER DEFAULT 0,
  last_verification_attempt TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  verified_at TIMESTAMP WITH TIME ZONE
);

-- Create FUSE leaderboards table
CREATE TABLE IF NOT EXISTS fuse_leaderboards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  game_type TEXT NOT NULL CHECK (game_type IN ('fuse-bird', 'rock-paper-scissors', '2048')),
  leaderboard_type TEXT NOT NULL CHECK (leaderboard_type IN ('daily', 'weekly', 'monthly', 'all-time')),
  entry_fee_fuse DECIMAL(18,8) NOT NULL DEFAULT 1.0,
  prize_pool_fuse DECIMAL(18,8) NOT NULL DEFAULT 0,
  total_entries INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  starts_at TIMESTAMP WITH TIME ZONE NOT NULL,
  ends_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Unique constraint for leaderboard periods
  CONSTRAINT fuse_leaderboards_unique_period UNIQUE (game_type, leaderboard_type, starts_at)
);

-- Create FUSE leaderboard entries table
CREATE TABLE IF NOT EXISTS fuse_leaderboard_entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  leaderboard_id UUID NOT NULL REFERENCES fuse_leaderboards(id) ON DELETE CASCADE,
  gaming_session_id UUID NOT NULL REFERENCES fuse_gaming_sessions(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  score INTEGER NOT NULL,
  rank INTEGER,
  prize_amount_fuse DECIMAL(18,8) DEFAULT 0,
  prize_paid BOOLEAN DEFAULT FALSE,
  prize_transaction_hash TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Unique constraint to prevent duplicate entries per session
  CONSTRAINT fuse_leaderboard_entries_session_unique UNIQUE (leaderboard_id, gaming_session_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_fuse_gaming_sessions_user_type ON fuse_gaming_sessions(user_id, game_type);
CREATE INDEX IF NOT EXISTS idx_fuse_gaming_sessions_active ON fuse_gaming_sessions(is_active, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_fuse_trustlines_wallet ON fuse_trustlines(wallet_address);
CREATE INDEX IF NOT EXISTS idx_fuse_gaming_transactions_hash ON fuse_gaming_transactions(transaction_hash);
CREATE INDEX IF NOT EXISTS idx_fuse_gaming_transactions_session ON fuse_gaming_transactions(gaming_session_id);
CREATE INDEX IF NOT EXISTS idx_fuse_leaderboard_entries_board_rank ON fuse_leaderboard_entries(leaderboard_id, rank);
CREATE INDEX IF NOT EXISTS idx_fuse_leaderboard_entries_user ON fuse_leaderboard_entries(user_id);

-- Enable Row Level Security
ALTER TABLE fuse_gaming_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE fuse_trustlines ENABLE ROW LEVEL SECURITY;
ALTER TABLE fuse_gaming_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE fuse_leaderboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE fuse_leaderboard_entries ENABLE ROW LEVEL SECURITY;

-- RLS Policies for fuse_gaming_sessions
CREATE POLICY "Users can view their own FUSE gaming sessions" ON fuse_gaming_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own FUSE gaming sessions" ON fuse_gaming_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own FUSE gaming sessions" ON fuse_gaming_sessions
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for fuse_trustlines
CREATE POLICY "Users can view their own FUSE trustlines" ON fuse_trustlines
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own FUSE trustlines" ON fuse_trustlines
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own FUSE trustlines" ON fuse_trustlines
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for fuse_gaming_transactions
CREATE POLICY "Users can view their own FUSE gaming transactions" ON fuse_gaming_transactions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own FUSE gaming transactions" ON fuse_gaming_transactions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for fuse_leaderboards (public read)
CREATE POLICY "Anyone can view FUSE leaderboards" ON fuse_leaderboards
  FOR SELECT USING (true);

-- RLS Policies for fuse_leaderboard_entries (public read, user insert)
CREATE POLICY "Anyone can view FUSE leaderboard entries" ON fuse_leaderboard_entries
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own FUSE leaderboard entries" ON fuse_leaderboard_entries
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Function to create or update FUSE trustline record
CREATE OR REPLACE FUNCTION upsert_fuse_trustline(
  p_user_id UUID,
  p_wallet_address TEXT,
  p_trustline_limit DECIMAL(18,8) DEFAULT 0,
  p_current_balance DECIMAL(18,8) DEFAULT 0,
  p_trustline_verified BOOLEAN DEFAULT TRUE
) RETURNS UUID AS $$
DECLARE
  trustline_id UUID;
BEGIN
  INSERT INTO fuse_trustlines (
    user_id,
    wallet_address,
    trustline_limit,
    current_balance,
    trustline_verified,
    last_verified_at,
    updated_at
  ) VALUES (
    p_user_id,
    p_wallet_address,
    p_trustline_limit,
    p_current_balance,
    p_trustline_verified,
    NOW(),
    NOW()
  )
  ON CONFLICT (user_id, wallet_address)
  DO UPDATE SET
    trustline_limit = EXCLUDED.trustline_limit,
    current_balance = EXCLUDED.current_balance,
    trustline_verified = EXCLUDED.trustline_verified,
    last_verified_at = NOW(),
    updated_at = NOW()
  RETURNING id INTO trustline_id;

  RETURN trustline_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to submit FUSE competitive score and handle leaderboard entry
CREATE OR REPLACE FUNCTION submit_fuse_competitive_score(
  p_user_id UUID,
  p_game_type TEXT,
  p_score INTEGER,
  p_gaming_session_id UUID,
  p_game_data JSONB DEFAULT NULL,
  p_moves_count INTEGER DEFAULT NULL,
  p_highest_tile INTEGER DEFAULT NULL,
  p_game_duration_seconds INTEGER DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  session_record RECORD;
  leaderboard_record RECORD;
  entry_id UUID;
  current_rank INTEGER;
  result JSONB;
BEGIN
  -- Verify the gaming session exists and belongs to the user
  SELECT * INTO session_record
  FROM fuse_gaming_sessions
  WHERE id = p_gaming_session_id
    AND user_id = p_user_id
    AND fuse_transaction_verified = TRUE;

  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Gaming session not found or not verified'
    );
  END IF;

  -- Update the gaming session with final score
  UPDATE fuse_gaming_sessions
  SET
    final_score = p_score,
    game_data = p_game_data,
    moves_count = p_moves_count,
    highest_tile = p_highest_tile,
    game_duration_seconds = p_game_duration_seconds,
    ended_at = NOW()
  WHERE id = p_gaming_session_id;

  -- Find active leaderboard for this game type and entry fee
  SELECT * INTO leaderboard_record
  FROM fuse_leaderboards
  WHERE game_type = p_game_type
    AND entry_fee_fuse = session_record.entry_fee_fuse
    AND is_active = TRUE
    AND starts_at <= NOW()
    AND (ends_at IS NULL OR ends_at > NOW())
  ORDER BY starts_at DESC
  LIMIT 1;

  IF FOUND THEN
    -- Insert leaderboard entry
    INSERT INTO fuse_leaderboard_entries (
      leaderboard_id,
      gaming_session_id,
      user_id,
      score
    ) VALUES (
      leaderboard_record.id,
      p_gaming_session_id,
      p_user_id,
      p_score
    )
    ON CONFLICT (leaderboard_id, gaming_session_id)
    DO UPDATE SET score = EXCLUDED.score
    RETURNING id INTO entry_id;

    -- Update leaderboard total entries
    UPDATE fuse_leaderboards
    SET total_entries = total_entries + 1
    WHERE id = leaderboard_record.id;

    -- Calculate rank (simplified - could be optimized)
    SELECT COUNT(*) + 1 INTO current_rank
    FROM fuse_leaderboard_entries
    WHERE leaderboard_id = leaderboard_record.id
      AND score > p_score;

    -- Update the rank
    UPDATE fuse_leaderboard_entries
    SET rank = current_rank
    WHERE id = entry_id;

    result := jsonb_build_object(
      'success', true,
      'leaderboard_entry_id', entry_id,
      'rank', current_rank,
      'leaderboard_id', leaderboard_record.id
    );
  ELSE
    result := jsonb_build_object(
      'success', true,
      'message', 'Score submitted but no active leaderboard found'
    );
  END IF;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert initial FUSE leaderboards
INSERT INTO fuse_leaderboards (game_type, leaderboard_type, entry_fee_fuse, prize_pool_fuse, starts_at) VALUES
-- Fuse Bird leaderboards
('fuse-bird', 'daily', 1.0, 10.0, NOW()),
('fuse-bird', 'weekly', 1.0, 50.0, DATE_TRUNC('week', NOW())),
('fuse-bird', 'monthly', 1.0, 200.0, DATE_TRUNC('month', NOW())),
('fuse-bird', 'all-time', 1.0, 1000.0, '2025-06-16 00:00:00+00'),

-- 2048 leaderboards
('2048', 'daily', 1.0, 10.0, NOW()),
('2048', 'weekly', 1.0, 50.0, DATE_TRUNC('week', NOW())),
('2048', 'monthly', 1.0, 200.0, DATE_TRUNC('month', NOW())),
('2048', 'all-time', 1.0, 1000.0, '2025-06-16 00:00:00+00'),

-- Rock Paper Scissors leaderboards
('rock-paper-scissors', 'daily', 1.0, 10.0, NOW()),
('rock-paper-scissors', 'weekly', 1.0, 50.0, DATE_TRUNC('week', NOW())),
('rock-paper-scissors', 'monthly', 1.0, 200.0, DATE_TRUNC('month', NOW())),
('rock-paper-scissors', 'all-time', 1.0, 1000.0, '2025-06-16 00:00:00+00')

ON CONFLICT (game_type, leaderboard_type, starts_at) DO NOTHING;

-- Comments for documentation
COMMENT ON TABLE fuse_gaming_sessions IS 'Tracks FUSE token-based competitive gaming sessions';
COMMENT ON TABLE fuse_trustlines IS 'Tracks user FUSE token trustlines for gaming eligibility';
COMMENT ON TABLE fuse_gaming_transactions IS 'Logs all FUSE token transactions related to gaming';
COMMENT ON TABLE fuse_leaderboards IS 'FUSE token-based leaderboards with prize pools';
COMMENT ON TABLE fuse_leaderboard_entries IS 'Individual player entries in FUSE leaderboards';

COMMENT ON FUNCTION upsert_fuse_trustline IS 'Creates or updates FUSE trustline records for users';
COMMENT ON FUNCTION submit_fuse_competitive_score IS 'Submits competitive scores to FUSE leaderboards and calculates rankings';

-- Function to create or update FUSE trustline record
CREATE OR REPLACE FUNCTION upsert_fuse_trustline(
  p_user_id UUID,
  p_wallet_address TEXT,
  p_trustline_limit DECIMAL(18,8) DEFAULT 0,
  p_current_balance DECIMAL(18,8) DEFAULT 0,
  p_trustline_verified BOOLEAN DEFAULT TRUE
) RETURNS UUID AS $$
DECLARE
  trustline_id UUID;
BEGIN
  INSERT INTO fuse_trustlines (
    user_id,
    wallet_address,
    trustline_limit,
    current_balance,
    trustline_verified,
    last_verified_at,
    updated_at
  ) VALUES (
    p_user_id,
    p_wallet_address,
    p_trustline_limit,
    p_current_balance,
    p_trustline_verified,
    NOW(),
    NOW()
  )
  ON CONFLICT (user_id, wallet_address)
  DO UPDATE SET
    trustline_limit = EXCLUDED.trustline_limit,
    current_balance = EXCLUDED.current_balance,
    trustline_verified = EXCLUDED.trustline_verified,
    last_verified_at = NOW(),
    updated_at = NOW()
  RETURNING id INTO trustline_id;

  RETURN trustline_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to submit FUSE competitive score and handle leaderboard entry
CREATE OR REPLACE FUNCTION submit_fuse_competitive_score(
  p_user_id UUID,
  p_game_type TEXT,
  p_score INTEGER,
  p_gaming_session_id UUID,
  p_game_data JSONB DEFAULT NULL,
  p_moves_count INTEGER DEFAULT NULL,
  p_highest_tile INTEGER DEFAULT NULL,
  p_game_duration_seconds INTEGER DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  session_record RECORD;
  leaderboard_record RECORD;
  entry_id UUID;
  current_rank INTEGER;
  result JSONB;
BEGIN
  -- Verify the gaming session exists and belongs to the user
  SELECT * INTO session_record
  FROM fuse_gaming_sessions
  WHERE id = p_gaming_session_id
    AND user_id = p_user_id
    AND fuse_transaction_verified = TRUE;

  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Gaming session not found or not verified'
    );
  END IF;

  -- Update the gaming session with final score
  UPDATE fuse_gaming_sessions
  SET
    final_score = p_score,
    game_data = p_game_data,
    moves_count = p_moves_count,
    highest_tile = p_highest_tile,
    game_duration_seconds = p_game_duration_seconds,
    ended_at = NOW()
  WHERE id = p_gaming_session_id;

  -- Find active leaderboard for this game type and entry fee
  SELECT * INTO leaderboard_record
  FROM fuse_leaderboards
  WHERE game_type = p_game_type
    AND entry_fee_fuse = session_record.entry_fee_fuse
    AND is_active = TRUE
    AND starts_at <= NOW()
    AND (ends_at IS NULL OR ends_at > NOW())
  ORDER BY starts_at DESC
  LIMIT 1;

  IF FOUND THEN
    -- Insert leaderboard entry
    INSERT INTO fuse_leaderboard_entries (
      leaderboard_id,
      gaming_session_id,
      user_id,
      score
    ) VALUES (
      leaderboard_record.id,
      p_gaming_session_id,
      p_user_id,
      p_score
    )
    ON CONFLICT (leaderboard_id, gaming_session_id)
    DO UPDATE SET score = EXCLUDED.score
    RETURNING id INTO entry_id;

    -- Update leaderboard total entries
    UPDATE fuse_leaderboards
    SET total_entries = total_entries + 1
    WHERE id = leaderboard_record.id;

    -- Calculate rank (simplified - could be optimized)
    SELECT COUNT(*) + 1 INTO current_rank
    FROM fuse_leaderboard_entries
    WHERE leaderboard_id = leaderboard_record.id
      AND score > p_score;

    -- Update the rank
    UPDATE fuse_leaderboard_entries
    SET rank = current_rank
    WHERE id = entry_id;

    result := jsonb_build_object(
      'success', true,
      'leaderboard_entry_id', entry_id,
      'rank', current_rank,
      'leaderboard_id', leaderboard_record.id
    );
  ELSE
    result := jsonb_build_object(
      'success', true,
      'message', 'Score submitted but no active leaderboard found'
    );
  END IF;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
