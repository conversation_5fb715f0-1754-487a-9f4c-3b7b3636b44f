-- Create yield strategy allocations table
CREATE TABLE IF NOT EXISTS yield_strategy_allocations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  strategy_id TEXT NOT NULL,
  asset_type TEXT NOT NULL CHECK (asset_type IN ('XRP', 'USDC')),
  amount DECIMAL NOT NULL,
  allocated_at TIMESTAMP WITH TIME ZONE NOT NULL,
  current_value DECIMAL NOT NULL DEFAULT 0,
  unrealized_gains DECIMAL NOT NULL DEFAULT 0,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'withdrawn', 'rebalanced')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create yield performance tracking table
CREATE TABLE IF NOT EXISTS yield_performance_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  allocation_id UUID NOT NULL REFERENCES yield_strategy_allocations(id),
  performance_date TIMESTAMP WITH TIME ZONE NOT NULL,
  value DECIMAL NOT NULL,
  gains_losses DECIMAL NOT NULL DEFAULT 0,
  apy_at_date DECIMAL NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create liquidity reserves table
CREATE TABLE IF NOT EXISTS liquidity_reserves (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  asset_type TEXT NOT NULL CHECK (asset_type IN ('XRP', 'USDC')),
  reserved_amount DECIMAL NOT NULL DEFAULT 0,
  deployed_amount DECIMAL NOT NULL DEFAULT 0,
  total_pool_size DECIMAL NOT NULL DEFAULT 0,
  reserve_ratio DECIMAL NOT NULL DEFAULT 0.2,
  last_rebalanced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert initial liquidity reserves
INSERT INTO liquidity_reserves (asset_type) VALUES ('XRP'), ('USDC')
ON CONFLICT DO NOTHING;

-- Create function to update yield pool stats with real APY
CREATE OR REPLACE FUNCTION update_yield_pool_apy()
RETURNS VOID AS $$
DECLARE
  total_xrp_deployed DECIMAL := 0;
  total_usdc_deployed DECIMAL := 0;
  weighted_xrp_yield DECIMAL := 0;
  weighted_usdc_yield DECIMAL := 0;
  calculated_apy DECIMAL := 0;
  xrp_usd_rate DECIMAL := 0.5; -- Approximate XRP to USD rate
BEGIN
  -- Calculate weighted yields from active allocations
  SELECT 
    COALESCE(SUM(CASE WHEN asset_type = 'XRP' THEN amount ELSE 0 END), 0),
    COALESCE(SUM(CASE WHEN asset_type = 'USDC' THEN amount ELSE 0 END), 0)
  INTO total_xrp_deployed, total_usdc_deployed
  FROM yield_strategy_allocations 
  WHERE status = 'active';

  -- Calculate weighted APY based on strategy performance
  -- This is a simplified calculation - in production, you'd use real performance data
  IF total_xrp_deployed > 0 THEN
    weighted_xrp_yield := total_xrp_deployed * 0.07; -- Average 7% for XRP strategies
  END IF;
  
  IF total_usdc_deployed > 0 THEN
    weighted_usdc_yield := total_usdc_deployed * 0.09; -- Average 9% for USDC strategies
  END IF;

  -- Calculate combined APY weighted by USD value
  IF (total_xrp_deployed * xrp_usd_rate + total_usdc_deployed) > 0 THEN
    calculated_apy := ((weighted_xrp_yield * xrp_usd_rate) + weighted_usdc_yield) / 
                     (total_xrp_deployed * xrp_usd_rate + total_usdc_deployed) * 100;
  ELSE
    calculated_apy := 5.0; -- Default APY when no funds are deployed
  END IF;

  -- Update yield pool stats
  UPDATE yield_pool_stats 
  SET 
    current_apy = calculated_apy,
    last_updated_at = NOW()
  WHERE id = (SELECT id FROM yield_pool_stats LIMIT 1);
END;
$$ LANGUAGE plpgsql;

-- Create function to process yield distributions
CREATE OR REPLACE FUNCTION distribute_yield()
RETURNS VOID AS $$
DECLARE
  distribution_record RECORD;
  user_record RECORD;
  user_xrp_balance DECIMAL;
  user_usdc_balance DECIMAL;
  user_xrp_yield DECIMAL;
  user_usdc_yield DECIMAL;
  current_apy DECIMAL;
  days_since_last_distribution INTEGER := 1; -- Daily distributions
BEGIN
  -- Get current APY
  SELECT current_apy INTO current_apy FROM yield_pool_stats LIMIT 1;
  
  -- Create new distribution record
  INSERT INTO yield_distributions (
    distribution_date,
    apy_at_distribution,
    total_xrp_distributed,
    total_usdc_distributed
  ) VALUES (
    NOW(),
    current_apy,
    0,
    0
  ) RETURNING * INTO distribution_record;

  -- Calculate and distribute yield to each user
  FOR user_record IN 
    SELECT DISTINCT user_id FROM yield_pool_deposits WHERE status = 'confirmed'
  LOOP
    -- Calculate user's current balance
    SELECT 
      COALESCE(SUM(CASE WHEN asset_type = 'XRP' THEN amount ELSE 0 END), 0) -
      COALESCE((SELECT SUM(amount) FROM yield_pool_withdrawals 
                WHERE user_id = user_record.user_id AND asset_type = 'XRP' 
                AND status IN ('processing', 'completed')), 0),
      COALESCE(SUM(CASE WHEN asset_type = 'USDC' THEN amount ELSE 0 END), 0) -
      COALESCE((SELECT SUM(amount) FROM yield_pool_withdrawals 
                WHERE user_id = user_record.user_id AND asset_type = 'USDC' 
                AND status IN ('processing', 'completed')), 0)
    INTO user_xrp_balance, user_usdc_balance
    FROM yield_pool_deposits 
    WHERE user_id = user_record.user_id AND status = 'confirmed';

    -- Calculate daily yield (APY / 365)
    user_xrp_yield := user_xrp_balance * (current_apy / 100 / 365) * days_since_last_distribution;
    user_usdc_yield := user_usdc_balance * (current_apy / 100 / 365) * days_since_last_distribution;

    -- Only create yield record if user has positive yield
    IF user_xrp_yield > 0 OR user_usdc_yield > 0 THEN
      INSERT INTO user_yield_earnings (
        user_id,
        distribution_id,
        xrp_amount,
        usdc_amount
      ) VALUES (
        user_record.user_id,
        distribution_record.id,
        user_xrp_yield,
        user_usdc_yield
      );
    END IF;
  END LOOP;

  -- Update distribution totals
  UPDATE yield_distributions 
  SET 
    total_xrp_distributed = (
      SELECT COALESCE(SUM(xrp_amount), 0) 
      FROM user_yield_earnings 
      WHERE distribution_id = distribution_record.id
    ),
    total_usdc_distributed = (
      SELECT COALESCE(SUM(usdc_amount), 0) 
      FROM user_yield_earnings 
      WHERE distribution_id = distribution_record.id
    )
  WHERE id = distribution_record.id;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_yield_strategy_allocations_status ON yield_strategy_allocations(status);
CREATE INDEX IF NOT EXISTS idx_yield_strategy_allocations_asset_type ON yield_strategy_allocations(asset_type);
CREATE INDEX IF NOT EXISTS idx_yield_performance_history_allocation ON yield_performance_history(allocation_id);
CREATE INDEX IF NOT EXISTS idx_yield_performance_history_date ON yield_performance_history(performance_date);
CREATE INDEX IF NOT EXISTS idx_liquidity_reserves_asset_type ON liquidity_reserves(asset_type);

-- Create RLS policies
ALTER TABLE yield_strategy_allocations ENABLE ROW LEVEL SECURITY;
ALTER TABLE yield_performance_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE liquidity_reserves ENABLE ROW LEVEL SECURITY;

-- Admin-only access to strategy tables
CREATE POLICY "Admin only access to yield strategy allocations"
ON yield_strategy_allocations FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);

CREATE POLICY "Admin only access to yield performance history"
ON yield_performance_history FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);

CREATE POLICY "Admin only access to liquidity reserves"
ON liquidity_reserves FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);
