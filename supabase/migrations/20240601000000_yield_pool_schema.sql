-- Create yield pool tables
CREATE TABLE IF NOT EXISTS yield_pool_stats (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  total_xrp DECIMAL NOT NULL DEFAULT 0,
  total_usdc DECIMAL NOT NULL DEFAULT 0,
  current_apy DECIMAL NOT NULL DEFAULT 5.0,
  participant_count INTEGER NOT NULL DEFAULT 0,
  last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert initial stats record
INSERT INTO yield_pool_stats (id) VALUES (uuid_generate_v4())
ON CONFLICT DO NOTHING;

-- Create deposits table
CREATE TABLE IF NOT EXISTS yield_pool_deposits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  wallet_address TEXT NOT NULL,
  asset_type TEXT NOT NULL CHECK (asset_type IN ('XRP', 'USDC')),
  amount DECIMAL NOT NULL,
  transaction_hash TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  confirmed_at TIMESTAMP WITH TIME ZONE
);

-- Create withdrawals table
CREATE TABLE IF NOT EXISTS yield_pool_withdrawals (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  deposit_id UUID REFERENCES yield_pool_deposits(id),
  wallet_address TEXT NOT NULL,
  asset_type TEXT NOT NULL CHECK (asset_type IN ('XRP', 'USDC')),
  amount DECIMAL NOT NULL,
  transaction_hash TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Create yield distribution table
CREATE TABLE IF NOT EXISTS yield_distributions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  distribution_date TIMESTAMP WITH TIME ZONE NOT NULL,
  total_xrp_distributed DECIMAL NOT NULL DEFAULT 0,
  total_usdc_distributed DECIMAL NOT NULL DEFAULT 0,
  apy_at_distribution DECIMAL NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user yield table
CREATE TABLE IF NOT EXISTS user_yield_earnings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  distribution_id UUID NOT NULL REFERENCES yield_distributions(id),
  xrp_amount DECIMAL NOT NULL DEFAULT 0,
  usdc_amount DECIMAL NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create secure RLS policies
ALTER TABLE yield_pool_deposits ENABLE ROW LEVEL SECURITY;
ALTER TABLE yield_pool_withdrawals ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_yield_earnings ENABLE ROW LEVEL SECURITY;

-- Users can only see their own deposits
CREATE POLICY "Users can view their own deposits"
ON