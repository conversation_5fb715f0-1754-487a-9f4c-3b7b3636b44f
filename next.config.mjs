/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  async redirects() {
    return [
      {
        source: '/card',
        destination: '/',
        permanent: true
      },
      {
        source: '/network',
        destination: '/',
        permanent: true
      },
      {
        source: '/update-password',
        destination: '/account/update-password',
        permanent: false
      }
    ];
  },
  webpack: (config, { isServer }) => {
    // Only include the pg module on the server side
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        dns: false,
        pg: false,
        'pg-native': false,
        'pg-hstore': false,
      };
    }
    return config;
  },
}

export default nextConfig
