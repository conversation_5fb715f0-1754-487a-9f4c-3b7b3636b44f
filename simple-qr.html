<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fuse.VIP Test QR Code</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .qr-code {
            margin: 20px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 10px;
        }
        .business-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .reward {
            background: #4caf50;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            font-weight: bold;
            margin: 10px 0;
        }
        .qr-data {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 11px;
            word-break: break-all;
            margin: 15px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Fuse.VIP Test QR Code</h1>
        
        <div class="business-info">
            <h3>📍 Demo Coffee Shop</h3>
            <p><strong>Category:</strong> Restaurant & Café</p>
            <div class="reward">💰 100 FUSE Tokens</div>
        </div>

        <div class="qr-code">
            <!-- QR Code will be generated here -->
            <img id="qr-image" alt="QR Code" style="max-width: 100%; height: auto;" />
            <div id="loading">Generating QR Code...</div>
        </div>

        <div class="qr-data">
            <strong>QR Data (JSON Format):</strong><br>
            <span id="qr-text"></span>
        </div>

        <div class="qr-data">
            <strong>Legacy Format (for testing):</strong><br>
            <span id="legacy-text"></span>
        </div>

        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h4>📱 How to Test:</h4>
            <ol style="text-align: left; padding-left: 20px;">
                <li>Go to <strong>fuse.vip/scan</strong></li>
                <li>Login as a VIP cardholder</li>
                <li>Scan this QR code</li>
                <li>See the success page with rewards!</li>
            </ol>
        </div>

        <p style="color: #666; font-size: 12px;">
            Business ID: 550e8400-e29b-41d4-a716-446655440000
        </p>
    </div>

    <script>
        // Generate business QR data (exact format expected by the system)
        const businessId = '550e8400-e29b-41d4-a716-446655440000';
        const qrData = JSON.stringify({
            userId: businessId,
            type: 'business',
            timestamp: Date.now()
        });

        // Also create a simple legacy format for testing
        const legacyQrData = businessId;

        // Display the QR data
        document.getElementById('qr-text').textContent = qrData;
        document.getElementById('legacy-text').textContent = legacyQrData;

        // Generate QR code using QR Server API
        const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrData)}`;
        
        const qrImage = document.getElementById('qr-image');
        const loading = document.getElementById('loading');
        
        qrImage.onload = function() {
            loading.style.display = 'none';
            qrImage.style.display = 'block';
        };
        
        qrImage.onerror = function() {
            loading.innerHTML = '❌ Error loading QR code. Please refresh the page.';
        };
        
        qrImage.src = qrCodeUrl;
        qrImage.style.display = 'none';
    </script>
</body>
</html>
