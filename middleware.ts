import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { getTokenFromRequest, verifyJWT, isTokenExpired } from "@/lib/jwt"

// Protected routes that require authentication
const PROTECTED_ROUTES = [
  '/dashboard',
  '/profile',
  '/onboarding',
  '/admin',
  '/business',
]

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/',
  '/login',
  '/register',
  '/reset-password',
  '/update-password',
  '/auth', // Allow auth confirmation routes
  '/about',
  '/contact',
  '/about-us',
  '/solutions',
  '/industry',
  '/fuse',
  '/upgrade',
  '/reviews',
  '/resources',
  '/book-call',
  '/register-business',
  '/privacy',
  '/terms',
  '/yield-pool',
  '/wecare4you',
]

// API routes that require authentication
const PROTECTED_API_ROUTES = [
  '/api/user',
  '/api/profile',
  '/api/admin',
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Handle old incorrect register route
  if (pathname === "/register" && request.nextUrl.searchParams.has("redirect_to_reviews")) {
    return NextResponse.redirect(new URL("/register", request.url))
  }

  // Skip middleware for static files and API auth routes
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api/auth') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next()
  }

  // Get JWT token from request
  const token = getTokenFromRequest(request)
  let isAuthenticated = false
  let tokenPayload = null

  if (token) {
    tokenPayload = await verifyJWT(token)
    isAuthenticated = tokenPayload !== null && !isTokenExpired(tokenPayload)
  }

  // Check if route requires authentication
  const isProtectedRoute = PROTECTED_ROUTES.some(route => pathname.startsWith(route))
  const isProtectedApiRoute = PROTECTED_API_ROUTES.some(route => pathname.startsWith(route))
  const isPublicRoute = PUBLIC_ROUTES.some(route => pathname === route || pathname.startsWith(route))

  // Explicitly allow public routes without any authentication checks
  if (isPublicRoute) {
    return NextResponse.next()
  }

  // Handle protected routes
  if (isProtectedRoute || isProtectedApiRoute) {
    if (!isAuthenticated) {
      if (isProtectedApiRoute) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }

      // Redirect to login with return URL
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(loginUrl)
    }

    // Check for admin routes
    if (pathname.startsWith('/admin') && tokenPayload?.role !== 'admin') {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }
  }

  // Handle authenticated users accessing auth pages
  if (isAuthenticated && (pathname === '/login' || pathname === '/register')) {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  // Auto-refresh token if it's close to expiring (within 5 minutes)
  if (isAuthenticated && tokenPayload) {
    const timeUntilExpiry = tokenPayload.exp! - Math.floor(Date.now() / 1000)

    if (timeUntilExpiry < 300) { // 5 minutes
      const response = NextResponse.next()

      // Set a header to trigger client-side token refresh
      response.headers.set('X-Refresh-Token', 'true')
      return response
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (auth API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico).*)',
  ],
}