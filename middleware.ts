import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// List of public routes that don't require authentication
const publicRoutes = [
  '/login',
  '/signup',
  '/reset-password',
  '/update-password',
  '/auth/confirm',
  '/auth/error',
  '/api/auth/reset-password',
  '/api/auth/update-password',
  '/api/auth/login',
  '/api/auth/signup',
  '/api/auth/refresh'
]

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Debug log for auth-related routes
  if (pathname.includes('/auth/') || pathname.includes('/api/auth/')) {
    console.log(`Middleware processing auth route: ${pathname}`)
  }
  
  // Check if the path is a public route or API route that should be accessible without auth
  const isPublicRoute = publicRoutes.some(route => 
    pathname.startsWith(route) || pathname === route
  )
  
  // Always allow public routes and API routes that handle their own auth
  if (isPublicRoute || pathname.startsWith('/api/auth/')) {
    console.log(`Allowing access to public route: ${pathname}`)
    return NextResponse.next()
  }
  
  // Continue with your existing middleware logic for protected routes
  // ...
}

// Fixed matcher configuration without capturing groups
export const config = {
  matcher: [
    // Protected routes
    '/dashboard/:path*',
    '/account/:path*',
    '/admin/:path*',
    // Auth pages
    '/login',
    '/register',
    // API routes
    '/api/:path*',
    // Static assets exclusion pattern - fixed to avoid capturing groups
    '/((?!api|_next/static|_next/image|favicon.ico).*)'
  ],
}
