"use client";

import React, { useState } from 'react';
import Link from 'next/link';

export default function ResetPasswordPage() {
  const [email, setEmail] = useState('');
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMsg('');
    setSuccessMsg('');
    setLoading(true);

    try {
      console.log('Sending password reset request for email:', email);

      // First try the App Router API endpoint
      let response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      // If that fails with a 401, try the Pages Router API endpoint as fallback
      if (response.status === 401) {
        console.log('App Router API returned 401, trying Pages Router API as fallback');
        response = await fetch('/api/password-reset', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email }),
        });
      }

      const data = await response.json();

      if (!response.ok) {
        console.error('Password reset failed with status:', response.status);
        
        // Handle specific error cases
        if (response.status === 401) {
          throw new Error('Reset failed. Please try again later.');
        } else {
          throw new Error(data.error || 'Password reset request failed');
        }
      }

      console.log('Password reset response:', data);
      setSuccessMsg('If an account with that email exists, a password reset link has been sent.');

    } catch (error: any) {
      console.error('Password reset error:', error);
      // Show a user-friendly error message
      setErrorMsg(error.message || 'Unable to process your request. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={styles.container}>
      <div style={styles.formBox}>
        <h2 style={styles.heading}>Reset Password</h2>

        {errorMsg && <p style={styles.error}>{errorMsg}</p>}
        {successMsg && <p style={styles.success}>{successMsg}</p>}

        <form onSubmit={handleSubmit} style={styles.form}>
          <div style={styles.field}>
            <label style={styles.label}>
              Email Address
              <input
                type="email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                style={styles.input}
                placeholder="Enter your email"
                required
              />
            </label>
          </div>

          <button
            type="submit"
            style={styles.button}
            disabled={loading}
          >
            {loading ? 'Sending Link...' : 'Send Reset Link'}
          </button>
        </form>

        <div style={styles.links}>
          <Link href="/login" style={styles.link}>
            Back to Login
          </Link>
        </div>
        
        {/* Test account hint for development */}
        {process.env.NODE_ENV !== 'production' && (
          <div style={styles.testAccount}>
            <p>For testing: <code><EMAIL></code></p>
          </div>
        )}
      </div>
    </div>
  );
}

// Styles
const styles = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    padding: '20px',
    backgroundColor: '#f5f5f5',
  },
  formBox: {
    width: '100%',
    maxWidth: '400px',
    padding: '30px',
    backgroundColor: 'white',
    borderRadius: '8px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
  },
  heading: {
    fontSize: '24px',
    fontWeight: 600,
    marginBottom: '24px',
    textAlign: 'center' as const,
    color: '#333',
  },
  form: {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '20px',
  },
  field: {
    marginBottom: '15px',
  },
  label: {
    display: 'block',
    marginBottom: '5px',
    fontSize: '14px',
    fontWeight: 500,
    color: '#555',
  },
  input: {
    width: '100%',
    padding: '10px 12px',
    fontSize: '16px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    marginTop: '5px',
  },
  button: {
    width: '100%',
    padding: '12px',
    backgroundColor: '#3A56FF',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    fontSize: '16px',
    fontWeight: 500,
    cursor: 'pointer',
    transition: 'background-color 0.2s',
  },
  error: {
    color: '#e53e3e',
    marginBottom: '15px',
    padding: '10px',
    backgroundColor: '#fff5f5',
    borderRadius: '4px',
    fontSize: '14px',
  },
  success: {
    color: '#38a169',
    marginBottom: '15px',
    padding: '10px',
    backgroundColor: '#f0fff4',
    borderRadius: '4px',
    fontSize: '14px',
  },
  links: {
    marginTop: '20px',
    textAlign: 'center' as const,
  },
  link: {
    color: '#3A56FF',
    textDecoration: 'none',
    fontSize: '14px',
  },
  testAccount: {
    marginTop: '20px',
    padding: '10px',
    backgroundColor: '#f0f4ff',
    borderRadius: '4px',
    fontSize: '12px',
    textAlign: 'center' as const,
    color: '#3A56FF',
  },
};
