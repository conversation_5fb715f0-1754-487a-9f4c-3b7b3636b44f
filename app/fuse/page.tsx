import { <PERSON><PERSON>eader } from "@/components/page-header"
import { CtaSection } from "@/components/cta-section"
import { ArrowRight, CheckCircle2, Wallet, LayoutGrid, Server, Code, ExternalLink, TrendingUp, Zap } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { XrpPrice } from "@/components/xrp-price"
import { LottieAnimation } from "@/components/lottie-animation"


export default function FusePage() {
  const benefits = [
    "Seamless customer loyalty program integration",
    "Automated reward distribution through XRP WebHooks",
    "Enhanced customer engagement and retention",
    "Transparent transaction history on the blockchain",
    "Reduced operational costs for loyalty management",
    "Ability to create custom reward tiers and incentives",
  ]

  const steps = [
    {
      number: "01",
      title: "Initial Consultation",
      description: "We'll assess your business needs and determine the best token integration strategy.",
    },
    {
      number: "02",
      title: "Integration Planning",
      description: "Our team will develop a custom integration plan for your specific business needs.",
    },
    {
      number: "03",
      title: "Digital Needs Assessment",
      description:
        "We'll analyze your existing systems and identify the optimal integration points for Fuse.Vip tokens.",
    },
    {
      number: "04",
      title: "Token Integration",
      description: "Based on the assessment, we'll develop a customized token adoption strategy for your business.",
    },
    {
      number: "05",
      title: "Implementation",
      description: "Our certified advisors will help you implement the token system and train your team.",
    },
    {
      number: "06",
      title: "Ongoing Support",
      description:
        "We provide continuous support to ensure your token system runs smoothly and evolves with your business needs.",
    },
  ]

  const useCases = [
    {
      title: "Customer Relationship Management (CRM)",
      description:
        "Reimagine customer relationships with automated loyalty mechanics, token-driven engagement strategies, and blockchain-backed behavioral insights — all powered by the FUSE network.",
      icon: <LayoutGrid className="w-12 h-12 text-[#316bff]" />,
      benefits: [
        "Reward profile completion and survey participation with FUSE tokens",
        "Dynamically assign loyalty tiers based on frequency of visits and token balance",
        "Enable VIP-only perks or events using token-gated access",
        "Monitor lifetime customer value and reward triggers on-chain with zero risk of manipulation",
      ],
    },
    {
      title: "Web3 Services Integration",
      description:
        "Fuse.vip enables any business to unlock blockchain utility without needing technical expertise — from decentralized payment rails to customer-level token benefits, we bridge the gap with simplicity.",
      icon: <Code className="w-12 h-12 text-[#316bff]" />,
      benefits: [
        "Plug-and-play wallet connection for customers via Xaman and XRP",
        "Token-based loyalty badges and scannable digital membership cards",
        "On-chain identity management for secure, reward-based authentication",
        "Built-in customer voting modules for feedback and governance (coming soon)",
      ],
    },
    {
      title: "Software as a Service (SaaS)",
      description:
        "FUSE empowers small businesses to run like enterprises — offering a token-based SaaS model that unlocks access to 1,000s of AI-powered automations for customer acquisition, retention, and operational scale.",
      icon: <Server className="w-12 h-12 text-[#316bff]" />,
      benefits: [
        "Instant access to tailored AI workflows via Zapier & n8n for outreach, follow-ups, and smart segmentation",
        "T-shirt sized growth tiers — Small, Medium, Large — activated by $FUSE holdings",
        "Token-gated upgrades that evolve with business needs (e.g., CRM, email automation, reputation management)s",
        "Hold more $FUSE = unlock more tools. Simple, scalable, and entirely no-code. Upgrade pathways that give small teams Fortune 500 functionality.",
      ],
    },
  ]

  return (
    <>
      <div className="relative">
        {/* Lottie Animation Background */}
        <div className="absolute inset-0 w-full h-full overflow-hidden opacity-15 z-0">
          <LottieAnimation
            src="https://lottie.host/27edf0b3-aedc-4ff8-85cb-30140a74eda5/Sz0NFk3o3r.lottie"
            width="100%"
            height="100%"
            className="scale-125 object-cover"
          />
        </div>

        {/* Semi-transparent gradient overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-b from-white/80 to-white/90 z-10"></div>

        {/* Page Header Content */}
        <div className="relative z-20">
          <PageHeader
            title="Fuse.Vip Token"
            subtitle="LOYALTY REIMAGINED"
            description="Transform your business with blockchain-powered loyalty through our Fuse.Vip token, designed to revolutionize customer engagement and rewards."
          />
        </div>
      </div>

      {/* FUSE Token Live Announcement */}
      <section className="py-12 bg-gradient-to-r from-[#0f172a] via-[#1e293b] to-[#0f172a] relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-10 left-10 w-20 h-20 bg-gradient-to-r from-[#3A56FF] to-[#6366f1] rounded-full animate-pulse"></div>
          <div className="absolute bottom-10 right-10 w-16 h-16 bg-gradient-to-r from-[#f59e0b] to-[#ef4444] rounded-full animate-bounce"></div>
          <div className="absolute top-1/2 left-1/4 w-12 h-12 bg-gradient-to-r from-[#10b981] to-[#3b82f6] rounded-full animate-ping"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            {/* Live Badge */}
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-6 py-2 rounded-full mb-6 animate-pulse">
              <div className="w-3 h-3 bg-white rounded-full animate-ping"></div>
              <span className="font-bold text-sm uppercase tracking-wider">LIVE NOW</span>
            </div>

            {/* Main Announcement */}
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              🚀 FUSE Token is Now
              <span className="bg-gradient-to-r from-[#3A56FF] to-[#6366f1] bg-clip-text text-transparent"> Trading Live!</span>
            </h2>

            <p className="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
              The wait is over! FUSE tokens are now available for trading on the XRP Ledger DEX.
              Start trading the future of loyalty rewards today.
            </p>

            {/* Trading Info */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div className="space-y-2">
                  <div className="flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-green-400 mr-2" />
                    <span className="text-white font-semibold">Trading Pair</span>
                  </div>
                  <p className="text-2xl font-bold text-white">FUSE / XRP</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-center">
                    <Zap className="h-6 w-6 text-yellow-400 mr-2" />
                    <span className="text-white font-semibold">Network</span>
                  </div>
                  <p className="text-2xl font-bold text-white">XRP Ledger</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-center">
                    <CheckCircle2 className="h-6 w-6 text-green-400 mr-2" />
                    <span className="text-white font-semibold">Status</span>
                  </div>
                  <p className="text-2xl font-bold text-green-400">LIVE</p>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <a
                href="https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gradient-to-r from-[#3A56FF] to-[#6366f1] hover:from-[#2563eb] hover:to-[#4f46e5] text-white px-8 py-4 rounded-lg font-bold text-lg inline-flex items-center justify-center transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <ExternalLink className="mr-2 h-5 w-5" />
                Trade FUSE Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </a>
              <Link
                href="/yield-pool"
                className="bg-white/10 hover:bg-white/20 text-white border-2 border-white/30 hover:border-white/50 px-8 py-4 rounded-lg font-bold text-lg inline-flex items-center justify-center transition-all duration-300"
              >
                Learn About FUSE Utility
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </div>

            {/* Additional Info */}
            <div className="mt-8 text-center">
              <p className="text-white/60 text-sm">
                Powered by <span className="text-white font-semibold">Magnetic DEX</span> •
                Secure • Fast • Decentralized
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 3500% Gain Celebration Banner */}
      <section className="py-8 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 relative overflow-hidden">
        {/* Animated background effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-yellow-400/20 via-orange-500/20 to-red-500/20 animate-pulse"></div>
          <div className="absolute top-2 left-10 text-6xl animate-bounce">🚀</div>
          <div className="absolute top-4 right-20 text-4xl animate-ping">💎</div>
          <div className="absolute bottom-2 left-1/4 text-5xl animate-bounce delay-300">🔥</div>
          <div className="absolute bottom-4 right-10 text-3xl animate-pulse">⚡</div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center">
            <div className="inline-flex items-center space-x-3 bg-white/20 backdrop-blur-sm rounded-full px-8 py-4 mb-4">
              <span className="text-3xl animate-spin">🎯</span>
              <span className="text-white font-bold text-xl">BREAKING NEWS</span>
              <span className="text-3xl animate-spin">🎯</span>
            </div>

            <h2 className="text-4xl md:text-6xl font-black text-white mb-4 animate-pulse">
              +6,800% AND CLIMBING! 🔥🚀
            </h2>

            <p className="text-xl md:text-2xl text-white/90 font-bold mb-6 max-w-3xl mx-auto">
              FUSE Token delivers <span className="text-yellow-200 animate-bounce inline-block">EXPLOSIVE GAINS</span> on XRPL DEX!
              The future of loyalty rewards is here and it's <span className="text-yellow-200">ON FIRE! 🔥</span>
            </p>

            <div className="flex flex-col sm:flex-row justify-center gap-4 items-center">
              <a
                href="https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white text-orange-600 hover:text-orange-700 px-8 py-4 rounded-full font-black text-lg inline-flex items-center transition-all duration-300 transform hover:scale-110 shadow-2xl animate-bounce"
              >
                <TrendingUp className="mr-2 h-6 w-6" />
                Trade Live on Magnetic DEX
                <ExternalLink className="ml-2 h-6 w-6" />
              </a>

              <a
                href="https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-400 hover:to-emerald-400 text-white px-8 py-4 rounded-full font-black text-lg inline-flex items-center transition-all duration-300 transform hover:scale-110 shadow-2xl"
              >
                <Zap className="mr-2 h-6 w-6" />
                Trade Now - Don't Miss Out!
                <ArrowRight className="ml-2 h-6 w-6" />
              </a>
            </div>

            <div className="mt-6 text-center">
              <p className="text-white/80 text-sm font-medium">
                ⚡ Live tracking from multiple XRPL DEX sources ⚡
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Live Price Tracking Section */}
      <section className="py-12 bg-[#f8f9fa]">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4">Live FUSE Token Metrics</h2>
              <p className="text-[#4a4a4a] max-w-2xl mx-auto">
                Real-time price tracking from multiple XRPL DEX sources with cross-validation for accuracy.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Live Price CTA */}
              <div className="bg-gradient-to-br from-[#0f172a] to-[#1e293b] rounded-2xl p-8 text-white relative overflow-hidden">
                {/* Animated background */}
                <div className="absolute inset-0 bg-gradient-to-r from-[#3A56FF]/10 via-transparent to-[#6366f1]/10 animate-pulse"></div>

                <div className="relative z-10">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-[#3A56FF] to-[#6366f1] rounded-full flex items-center justify-center">
                        <Zap className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold">FUSE Token</h3>
                        <p className="text-white/70">Live Trading on Magnetic DEX</p>
                      </div>
                    </div>
                  </div>

                  <div className="text-center py-8">
                    <div className="text-4xl font-bold mb-4 text-yellow-300 animate-pulse">
                      🚀 LIVE NOW! 🚀
                    </div>
                    <p className="text-xl text-white/90 mb-6">
                      FUSE Token is trading live with explosive gains!
                    </p>
                    <div className="text-center space-y-4">
                      <a
                        href="https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center space-x-2 bg-[#3A56FF] hover:bg-[#2563eb] px-8 py-4 rounded-lg transition-colors font-bold text-lg"
                      >
                        <ExternalLink className="w-5 h-5" />
                        <span>View Live Price & Trade Now</span>
                      </a>
                      <div className="text-sm text-white/60">
                        Real-time pricing available on Magnetic DEX
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Trading Info */}
              <div className="space-y-6">
                {/* Quick Stats */}
                <div className="bg-white rounded-2xl p-6 shadow-lg">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2 text-[#3A56FF]" />
                    Trading Information
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg">
                      <div className="text-2xl font-bold text-[#3A56FF]">FUSE/XRP</div>
                      <div className="text-sm text-gray-600">Primary Pair</div>
                    </div>
                    <div className="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">XRPL</div>
                      <div className="text-sm text-gray-600">Network</div>
                    </div>
                  </div>
                </div>

                {/* Trading Platforms */}
                <div className="bg-white rounded-2xl p-6 shadow-lg">
                  <h3 className="text-xl font-bold mb-4 flex items-center">
                    <ExternalLink className="w-5 h-5 mr-2 text-[#3A56FF]" />
                    Where to Trade
                  </h3>
                  <div className="space-y-3">
                    <a
                      href="https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-between p-4 bg-gradient-to-r from-[#3A56FF]/10 to-[#6366f1]/10 rounded-lg hover:from-[#3A56FF]/20 hover:to-[#6366f1]/20 transition-all duration-300 group"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-[#3A56FF] to-[#6366f1] rounded-full flex items-center justify-center">
                          <Zap className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <div className="font-semibold">Magnetic DEX</div>
                          <div className="text-sm text-gray-600">Primary Trading Platform</div>
                        </div>
                      </div>
                      <ArrowRight className="w-5 h-5 text-[#3A56FF] group-hover:translate-x-1 transition-transform" />
                    </a>

                    <a
                      href="https://dexscreener.com/xrpl/4655534500000000000000000000000000000000.rs2g9j95qwl3yw241jtrdgms2hhclouvho_xrp?maker=r3XhydxYWps8EP6xzqs7dEU4DXMjBme95p"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-between p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg hover:from-green-500/20 hover:to-emerald-500/20 transition-all duration-300 group"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                          <TrendingUp className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <div className="font-semibold">DexScreener</div>
                          <div className="text-sm text-gray-600">Price Charts & Analytics</div>
                        </div>
                      </div>
                      <ArrowRight className="w-5 h-5 text-green-500 group-hover:translate-x-1 transition-transform" />
                    </a>

                  </div>
                </div>

                {/* Price Sources Note */}
                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-6 border border-yellow-200">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <TrendingUp className="w-3 h-3 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-yellow-800 mb-1">Cross-Platform Price Validation</h4>
                      <p className="text-sm text-yellow-700">
                        Our price display aggregates data from multiple XRPL DEX sources to ensure accuracy and provide the most reliable market information.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6">What is Fuse.Vip Token?</h2>
              <p className="text-[#4a4a4a] mb-6">
                The Fuse.Vip Token is a native digital asset built on the XRP Ledger, engineered to redefine how
                businesses cultivate customer loyalty and long-term engagement. Now live and trading on the Magnetic DEX, it merges
                traditional reward systems with next-generation blockchain utility.
              </p>
              <p className="text-[#4a4a4a] mb-6">
                By leveraging the Fuse.Vip Token, participating businesses can deploy frictionless loyalty
                infrastructures that elevate retention, incentivize repeat behavior, and foster deeper customer
                relationships - all through fast, secure, and transparent transactions.
              </p>
              <p className="text-[#4a4a4a]">
                As an accredited digital solutions partner, Fuse.Vip offers expert support in token integration and
                strategy design, ensuring businesses unlock scalable value and optimize the customer experience through
                tailored adoption frameworks.
              </p>
            </div>
            <div className="md:w-1/2">
              <div className="relative">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png"
                  width={400}
                  height={400}
                  alt="Fuse.Vip Token"
                  className="mx-auto rounded-lg shadow-lg animate-pulse"
                />
                <div className="absolute inset-0 bg-gradient-radial from-[#3A56FF20] to-transparent rounded-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="bg-[#f8f9fa] py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Benefits of Fuse.Vip Token</h2>
            <p className="text-[#4a4a4a] max-w-2xl mx-auto">
              Our token system offers numerous advantages for businesses looking to enhance their digital presence and
              customer engagement strategies.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-start bg-white p-6 rounded-lg shadow-sm">
                <CheckCircle2 className="h-5 w-5 text-[#316bff] mr-3 flex-shrink-0 mt-0.5" />
                <p>{benefit}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Token Use Cases Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-center mb-12">
            <div className="hidden md:block md:w-1/4">
              <LottieAnimation
                src="https://lottie.host/b41b3564-35ed-4254-93d3-47dc7b0ef90b/806H2SAF2f.lottie"
                width="200px"
                height="200px"
              />
            </div>
            <div className="md:w-1/2 text-center">
              <h2 className="text-3xl font-bold mb-4">Token Use Cases</h2>
              <p className="text-[#4a4a4a] max-w-2xl mx-auto">
                Discover how Fuse.Vip tokens can revolutionize different aspects of your business operations.
              </p>
            </div>
            <div className="hidden md:block md:w-1/4">
              <LottieAnimation
                src="https://lottie.host/b7c330dc-5b31-42c4-aaf7-dd7c1cd187c3/UPgItnnQLJ.lottie"
                width="200px"
                height="200px"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {useCases.map((useCase, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-lg shadow-md border border-gray-100 hover:shadow-lg transition-shadow"
              >
                <div className="mb-6 flex justify-center">{useCase.icon}</div>
                <h3 className="text-xl font-bold mb-3 text-center">{useCase.title}</h3>
                <p className="text-[#4a4a4a] mb-6 text-center">{useCase.description}</p>
                <ul className="space-y-3">
                  {useCase.benefits.map((benefit, idx) => (
                    <li key={idx} className="flex items-start">
                      <CheckCircle2 className="h-5 w-5 text-[#316bff] mr-3 flex-shrink-0 mt-0.5" />
                      <p className="text-sm">{benefit}</p>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">How We Help You Integrate Fuse.Vip Token</h2>
            <p className="text-[#4a4a4a] max-w-2xl mx-auto">
              Our streamlined process makes it easy for your business to benefit from blockchain-based loyalty programs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {steps.map((step) => (
              <div key={step.number} className="border border-gray-200 rounded-lg p-6">
                <div className="text-[#316bff] font-bold text-4xl mb-4">{step.number}</div>
                <h3 className="font-bold text-xl mb-3">{step.title}</h3>
                <p className="text-[#4a4a4a]">{step.description}</p>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/register-business"
              className="bg-[#316bff] text-white px-6 py-3 rounded-md inline-flex items-center"
            >
              Start Your Token Integration
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>

      <section className="bg-[#e9f3ff] py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6">Xaman Wallet Integration</h2>
              <p className="text-[#4a4a4a] mb-6">
                Our platform seamlessly integrates with Xaman (formerly XUMM), the leading wallet for the XRP Ledger.
                This integration allows your customers to easily manage their Fuse.Vip tokens and interact with your
                loyalty program.
              </p>
              <p className="text-[#4a4a4a] mb-6">Benefits of Xaman integration include:</p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-start">
                  <CheckCircle2 className="h-5 w-5 text-[#316bff] mr-3 flex-shrink-0 mt-0.5" />
                  <p>Secure wallet connection for customers</p>
                </li>
                <li className="flex items-start">
                  <CheckCircle2 className="h-5 w-5 text-[#316bff] mr-3 flex-shrink-0 mt-0.5" />
                  <p>Simple token transactions and rewards redemption</p>
                </li>
                <li className="flex items-start">
                  <CheckCircle2 className="h-5 w-5 text-[#316bff] mr-3 flex-shrink-0 mt-0.5" />
                  <p>Transparent transaction history</p>
                </li>
                <li className="flex items-start">
                  <CheckCircle2 className="h-5 w-5 text-[#316bff] mr-3 flex-shrink-0 mt-0.5" />
                  <p>Enhanced security through blockchain technology</p>
                </li>
              </ul>
              <div className="flex space-x-4">
                <button className="bg-[#316bff] text-white px-6 py-3 rounded-md inline-flex items-center">
                  <Wallet className="mr-2 h-4 w-4" />
                  Learn More
                </button>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="bg-white p-8 rounded-lg shadow-md">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-bold">Token Status</h3>
                  <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium animate-pulse">
                    🚀 LIVE NOW
                  </span>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-[#316bff] rounded-full flex items-center justify-center text-white font-bold">
                      ✓
                    </div>
                    <div className="ml-4">
                      <h4 className="font-medium">Development Phase</h4>
                      <p className="text-sm text-green-600 font-medium">Completed</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-[#316bff] rounded-full flex items-center justify-center text-white font-bold">
                      ✓
                    </div>
                    <div className="ml-4">
                      <h4 className="font-medium">Testing & Auditing</h4>
                      <p className="text-sm text-green-600 font-medium">Completed</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-[#316bff] rounded-full flex items-center justify-center text-white font-bold">
                      ✓
                    </div>
                    <div className="ml-4">
                      <h4 className="font-medium">DEX Integration</h4>
                      <p className="text-sm text-green-600 font-medium">Live on Magnetic DEX</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-bold animate-pulse">
                      🔥
                    </div>
                    <div className="ml-4">
                      <h4 className="font-medium">Public Trading</h4>
                      <p className="text-sm text-green-600 font-bold">LIVE - Trade Now!</p>
                    </div>
                  </div>
                </div>
                <div className="mt-6 pt-6 border-t border-gray-200 space-y-4">
                  <XrpPrice />
                  <div className="pt-4 border-t border-gray-100 text-center">
                    <div className="bg-gradient-to-r from-[#3A56FF]/10 to-[#6366f1]/10 rounded-lg p-4">
                      <div className="flex items-center justify-center space-x-2 mb-2">
                        <Zap className="w-4 h-4 text-[#3A56FF]" />
                        <span className="font-semibold text-[#3A56FF]">FUSE Token</span>
                      </div>
                      <a
                        href="https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center space-x-1 text-sm bg-[#3A56FF] hover:bg-[#2563eb] text-white px-4 py-2 rounded-lg transition-colors"
                      >
                        <ExternalLink className="w-3 h-3" />
                        <span>View Live Price</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gradient-to-r from-[#1A1A1A] to-[#2A2A2A] text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Join Our Early Access Program</h2>
          <p className="max-w-2xl mx-auto mb-8">
            Be among the first businesses to integrate the Fuse.Vip Token into your loyalty program. Early adopters
            receive exclusive benefits, including premium support and bonus token allocations.
          </p>
          <div className="bg-[#3A56FF20] border border-[#3A56FF] p-8 rounded-lg shadow-lg max-w-3xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="text-left">
                <h3 className="text-xl font-bold mb-4">Early Adopter Benefits</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-[#3A56FF] mr-3 flex-shrink-0 mt-0.5" />
                    <p>Priority onboarding and setup</p>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-[#3A56FF] mr-3 flex-shrink-0 mt-0.5" />
                    <p>5% bonus token allocation</p>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-[#3A56FF] mr-3 flex-shrink-0 mt-0.5" />
                    <p>Dedicated integration specialist</p>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-[#3A56FF] mr-3 flex-shrink-0 mt-0.5" />
                    <p>Featured in our launch marketing</p>
                  </li>
                </ul>
              </div>
              <div className="text-left">
                <h3 className="text-xl font-bold mb-4">Program Timeline</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-[#3A56FF] mr-3 flex-shrink-0 mt-0.5" />
                    <p>Applications open: March 2025</p>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-[#3A56FF] mr-3 flex-shrink-0 mt-0.5" />
                    <p>Partner selection: March-May 2025</p>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-[#3A56FF] mr-3 flex-shrink-0 mt-0.5" />
                    <p>Integration period: May-June 2025</p>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-[#3A56FF] mr-3 flex-shrink-0 mt-0.5" />
                    <p>Public launch: June 15, 2025</p>
                  </li>
                </ul>
              </div>
            </div>
            <div className="mt-8">
              <Link
                href="/register-business"
                className="bg-[#3A56FF] text-white px-8 py-4 rounded-md inline-flex items-center text-lg font-medium"
              >
                Join Our Network Free and Earn Rewards Today
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      <CtaSection />
    </>
  )
}
