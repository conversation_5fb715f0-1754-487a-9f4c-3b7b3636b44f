import { RegisterForm } from "@/components/auth/register-form"
import { PageHeader } from "@/components/page-header"
import { CtaSection } from "@/components/cta-section"

export default function RegisterPage() {
  return (
    <div className="container max-w-screen-xl mx-auto py-12">
      <PageHeader
        title="Create Your Account then visit the Upgrade tab to purchase your VIP Card"
        subtitle="JOIN FUSE.VIP"
        description="Sign up to access exclusive features, buy your VIP card on the Upgrade tab, and visit Industry tab to find businesses in your area. Business owners can apply for dashboard access after this free registration."
      />

      <div className="mt-8 mb-16">
        <RegisterForm />
        <p className="text-sm text-gray-500 mt-4 text-center">
          After registration, you'll be automatically redirected to your dashboard where you can start exploring features and upgrade to VIP.
        </p>
      </div>

      <CtaSection />
    </div>
  )
}
