"use client";

import { useState } from "react";
import { useAuth } from "@/contexts/auth-context";
import { supabase } from "@/lib/supabase";
import { parseQRData } from "@/lib/qr-code-generator";
import { ProtectedRoute } from "@/components/protected-route";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, TestTube, CheckCircle, XCircle } from "lucide-react";
import { useRouter } from "next/navigation";

export default function DebugQRPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [testInput, setTestInput] = useState('');
  const [results, setResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (step: string, success: boolean, data: any, error?: string) => {
    setResults(prev => [...prev, {
      step,
      success,
      data,
      error,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const testQRParsing = async () => {
    if (!testInput.trim()) {
      addResult("Input Validation", false, null, "No input provided");
      return;
    }

    setIsLoading(true);
    clearResults();

    try {
      // Step 1: Test QR parsing
      addResult("Raw Input", true, { input: testInput });
      
      const qrData = parseQRData(testInput.trim());
      if (!qrData) {
        addResult("QR Parsing", false, null, "Failed to parse QR data");
        return;
      }
      addResult("QR Parsing", true, qrData);

      // Step 2: Check user authentication
      if (!user) {
        addResult("User Authentication", false, null, "User not logged in");
        return;
      }
      addResult("User Authentication", true, { userId: user.id, email: user.email });

      // Step 3: Check if user is cardholder (for business scans)
      if (qrData.type === 'business') {
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('is_card_holder, first_name, last_name')
          .eq('id', user.id)
          .single();

        if (profileError) {
          addResult("Profile Check", false, null, profileError.message);
          return;
        }

        addResult("Profile Check", true, profile);

        if (!profile?.is_card_holder) {
          addResult("Cardholder Validation", false, null, "User is not a VIP cardholder");
          return;
        }
        addResult("Cardholder Validation", true, { isCardHolder: true });

        // Step 4: Check cooldown
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const { data: existingVisit, error: visitError } = await supabase
          .from('business_visits')
          .select('id, scanned_at')
          .eq('user_id', user.id)
          .eq('business_id', qrData.userId)
          .gte('scanned_at', today.toISOString())
          .lt('scanned_at', tomorrow.toISOString())
          .single();

        if (visitError && visitError.code !== 'PGRST116') { // PGRST116 = no rows found
          addResult("Cooldown Check", false, null, visitError.message);
          return;
        }

        if (existingVisit) {
          addResult("Cooldown Check", false, existingVisit, "Already scanned today");
          return;
        }
        addResult("Cooldown Check", true, { canScan: true });

        // Step 5: Check if business exists
        const { data: business, error: businessError } = await supabase
          .from('businesses')
          .select('id, name, category, status')
          .eq('id', qrData.userId)
          .single();

        if (businessError) {
          addResult("Business Check", false, null, `Business not found: ${businessError.message}`);
          return;
        }

        addResult("Business Check", true, business);

        if (business.status !== 'approved') {
          addResult("Business Status", false, business, "Business not approved");
          return;
        }
        addResult("Business Status", true, { status: business.status });

        addResult("✅ ALL CHECKS PASSED", true, { 
          message: "Ready to log business visit and award 100 FUSE tokens!" 
        });

      } else {
        // User QR code
        addResult("QR Type", true, { type: "User QR Code", userId: qrData.userId });
        
        // Check if scanned user exists
        const { data: scannedProfile, error: scannedError } = await supabase
          .from('profiles')
          .select('id, first_name, last_name, is_card_holder')
          .eq('id', qrData.userId)
          .single();

        if (scannedError) {
          addResult("Scanned User Check", false, null, scannedError.message);
          return;
        }

        addResult("Scanned User Check", true, scannedProfile);
        addResult("✅ USER SCAN READY", true, { 
          message: "Ready to log user interaction!" 
        });
      }

    } catch (error) {
      addResult("Unexpected Error", false, null, error instanceof Error ? error.message : String(error));
    } finally {
      setIsLoading(false);
    }
  };

  const testSampleData = () => {
    const sampleBusinessQR = JSON.stringify({
      userId: '550e8400-e29b-41d4-a716-446655440000',
      type: 'business',
      timestamp: Date.now()
    });
    setTestInput(sampleBusinessQR);
  };

  const testLegacyData = () => {
    setTestInput('550e8400-e29b-41d4-a716-446655440000');
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <Button
              onClick={() => router.push('/dashboard')}
              variant="ghost"
              className="text-white hover:bg-white/10 mr-4"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Dashboard
            </Button>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">
                🔬 QR System Debug Tool
              </h1>
              <p className="text-gray-300">
                Test QR code parsing and validation step by step
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TestTube className="h-5 w-5" />
                    QR Code Testing
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      QR Code Data
                    </label>
                    <Input
                      value={testInput}
                      onChange={(e) => setTestInput(e.target.value)}
                      placeholder="Paste QR code data here..."
                      className="w-full"
                    />
                  </div>

                  <div className="flex gap-2">
                    <Button 
                      onClick={testQRParsing} 
                      disabled={isLoading || !testInput.trim()}
                      className="flex-1"
                    >
                      {isLoading ? 'Testing...' : 'Test QR Code'}
                    </Button>
                    <Button onClick={clearResults} variant="outline">
                      Clear
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <Button onClick={testSampleData} variant="outline" size="sm" className="w-full">
                      Load Sample Business QR
                    </Button>
                    <Button onClick={testLegacyData} variant="outline" size="sm" className="w-full">
                      Load Legacy Format
                    </Button>
                  </div>

                  <div className="bg-blue-50 rounded-lg p-3">
                    <h4 className="text-sm font-semibold text-blue-900 mb-1">Current User:</h4>
                    <p className="text-blue-800 text-sm">
                      {user?.email || 'Not logged in'}
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Results Section */}
              <Card>
                <CardHeader>
                  <CardTitle>Test Results</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {results.length === 0 ? (
                      <p className="text-gray-500 text-center py-8">
                        No tests run yet. Enter QR data and click "Test QR Code".
                      </p>
                    ) : (
                      results.map((result, index) => (
                        <div 
                          key={index}
                          className={`p-3 rounded-lg border-l-4 ${
                            result.success 
                              ? 'bg-green-50 border-green-500' 
                              : 'bg-red-50 border-red-500'
                          }`}
                        >
                          <div className="flex items-center gap-2 mb-1">
                            {result.success ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-600" />
                            )}
                            <span className="font-medium text-sm">
                              {result.step}
                            </span>
                            <span className="text-xs text-gray-500 ml-auto">
                              {result.timestamp}
                            </span>
                          </div>
                          
                          {result.error && (
                            <p className="text-red-700 text-sm mb-2">
                              {result.error}
                            </p>
                          )}
                          
                          {result.data && (
                            <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                              {JSON.stringify(result.data, null, 2)}
                            </pre>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
