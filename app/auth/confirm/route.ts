import { type EmailOtpType } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  console.log('Auth confirmation route called')
  const { searchParams } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const type = searchParams.get('type') as EmailOtpType | null
  const next = searchParams.get('next') ?? '/'
  const redirectTo = request.nextUrl.clone()
  redirectTo.pathname = next

  console.log('Auth confirmation params:', { 
    type, 
    hasTokenHash: !!token_hash, 
    next 
  })

  if (token_hash && type) {
    try {
      const supabase = await createClient()

      const { error } = await supabase.auth.verifyOtp({
        type,
        token_hash,
      })
      
      if (error) {
        console.error('OTP verification error:', error)
        redirectTo.pathname = '/error'
        redirectTo.searchParams.set('message', 'Invalid or expired verification link')
        return NextResponse.redirect(redirectTo)
      }
      
      console.log('OTP verification successful, redirecting to:', next)

      // For recovery flows, pass the token info and email to the destination page
      if (type === 'recovery') {
        redirectTo.searchParams.set('token_hash', token_hash);
        redirectTo.searchParams.set('type', type);
        
        // Extract and pass the email from the token if possible
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (user?.email) {
            redirectTo.searchParams.set('email', user.email);
          }
        } catch (err) {
          console.error('Error getting user email:', err);
        }
      }
      
      return NextResponse.redirect(redirectTo)
    } catch (error) {
      console.error('Auth confirmation error:', error)
      redirectTo.pathname = '/error'
      return NextResponse.redirect(redirectTo)
    }
  }

  console.error('Missing token_hash or type in auth confirmation')
  redirectTo.pathname = '/error'
  redirectTo.searchParams.set('message', 'Invalid verification link')
  return NextResponse.redirect(redirectTo)
}
