"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { supabase } from "@/lib/supabase"
import Image from "next/image"

export default function OnboardingPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const ensureUserSession = async () => {
      setIsLoading(true)
      
      // Check for stored user ID in localStorage
      const storedUserId = localStorage.getItem("userId")
      
      if (storedUserId && !user) {
        try {
          console.log("Attempting to restore session")
          
          // Use Supabase auth to refresh the session instead of direct DB connection
          await supabase.auth.refreshSession()
          
          // If session refresh fails, redirect to login
          const { data } = await supabase.auth.getSession()
          if (!data.session) {
            router.push("/login")
          }
        } catch (err) {
          console.error("Failed to restore session:", err)
          router.push("/login")
        }
      }
      
      setIsLoading(false)
    }
    
    if (!user) {
      ensureUserSession()
    } else {
      setIsLoading(false)
    }
  }, [user, router])

  // Redirect if no user after loading
  useEffect(() => {
    if (!isLoading && !user) {
      router.push("/login")
    }
  }, [isLoading, user, router])

  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>
  }

  return (
    <div className="container mx-auto px-4 py-8 text-center">
      <h1 className="text-3xl font-bold mb-6">Welcome to Fuse.Vip</h1>
      <div className="flex justify-center mb-6">
        <Image
          src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png"
          width={80}
          height={80}
          alt="Fuse.vip Logo"
          className="w-20 h-20"
        />
      </div>
      <h2 className="text-xl font-semibold mb-4">Customers & Business Owners earn $FUSE tokens with every purchase.</h2>
      <h3 className="text-gray-600 mb-8">
        Thank you for joining FUSE.VIP!
        Let's get you ready to start earning rewards and connecting with local businesses.
        Visit the dashboard & get a head start. Got a business? Register it on the dashboard.
      </h3>

      {/* Onboarding content here */}
    </div>
  )
}
