"use client";

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/auth-context';
import Link from 'next/link';

function UpdatePasswordForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const [loading, setLoading] = useState(false);
  const [tokenStatus, setTokenStatus] = useState<'valid' | 'expired' | 'unknown'>('unknown');
  const router = useRouter();
  const searchParams = useSearchParams();
  const { session, user } = useAuth();

  // Check for recovery flow and pre-fill email if available
  useEffect(() => {
    const checkRecoveryAndEmail = async () => {
      // Check if this is a recovery flow from email link
      const tokenHash = searchParams.get('token_hash');
      const type = searchParams.get('type');
      
      if (tokenHash && type === 'recovery') {
        console.log('Recovery flow detected with token:', tokenHash);
        
        // Verify the token is valid
        try {
          // Try to verify the token
          const { error } = await supabase.auth.verifyOtp({
            token_hash: tokenHash,
            type: 'recovery',
          });
          
          if (error) {
            console.error('Token verification failed:', error);
            setTokenStatus('expired');
            setErrorMsg('Your password reset link has expired. Please request a new one.');
          } else {
            setTokenStatus('valid');
          }
        } catch (error) {
          console.error('Token verification error:', error);
          setTokenStatus('expired');
          setErrorMsg('Your password reset link has expired. Please request a new one.');
        }
      }

      // Pre-fill email if available from session or URL
      if (user?.email) {
        setEmail(user.email);
      } else {
        const emailParam = searchParams.get('email');
        if (emailParam) {
          setEmail(decodeURIComponent(emailParam));
        }
      }
    };

    checkRecoveryAndEmail();
  }, [user, searchParams]);

  // Password validation state
  const getPasswordValidation = (password: string) => {
    return {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
  };

  const passwordValidation = getPasswordValidation(password);

  const validatePassword = (password: string) => {
    if (password.length < 8) return 'Password must be at least 8 characters long';
    if (!/[A-Z]/.test(password)) return 'Password must include an uppercase letter';
    if (!/[a-z]/.test(password)) return 'Password must include a lowercase letter';
    if (!/[0-9]/.test(password)) return 'Password must include a number';
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) return 'Password must include a special character';
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMsg('');
    setSuccessMsg('');
    setLoading(true);

    try {
      if (password !== confirmPassword) {
        throw new Error('Passwords do not match');
      }

      const passwordError = validatePassword(password);
      if (passwordError) {
        throw new Error(passwordError);
      }

      if (!email) {
        throw new Error('Email is required');
      }

      console.log('Attempting to update password via API...');

      // Always use the admin API endpoint for consistent behavior
      const response = await fetch('/api/auth/update-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Password update failed');
      }

      console.log('Password updated successfully via admin API');

      // Determine where to redirect based on session state
      if (session) {
        // User has an active session, redirect to dashboard
        setSuccessMsg('Password updated successfully! Redirecting to dashboard...');
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);
      } else {
        // No active session, redirect to login
        setSuccessMsg('Password updated successfully! Redirecting to login...');
        setTimeout(async () => {
          if (supabase) {
            await supabase.auth.signOut();
          }
          router.push('/login');
        }, 2000);
      }

    } catch (error: any) {
      console.error('Password update failed:', error);
      setErrorMsg(error.message || 'Password update failed');
    } finally {
      setLoading(false);
    }
  };

  const styles = {
    container: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '100vh',
      padding: '20px',
      backgroundColor: '#f5f5f5',
    },
    formBox: {
      backgroundColor: 'white',
      padding: '40px',
      borderRadius: '8px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      width: '100%',
      maxWidth: '400px',
    },
    heading: {
      textAlign: 'center' as const,
      marginBottom: '30px',
      color: '#333',
      fontSize: '24px',
      fontWeight: 'bold',
    },
    form: {
      display: 'flex',
      flexDirection: 'column' as const,
      gap: '20px',
    },
    field: {
      display: 'flex',
      flexDirection: 'column' as const,
    },
    label: {
      marginBottom: '8px',
      color: '#555',
      fontSize: '14px',
      fontWeight: '500',
    },
    input: {
      padding: '12px',
      border: '1px solid #ddd',
      borderRadius: '4px',
      fontSize: '16px',
      transition: 'border-color 0.2s',
      outline: 'none',
    },
    button: {
      padding: '12px',
      backgroundColor: loading ? '#ccc' : '#007bff',
      color: 'white',
      border: 'none',
      borderRadius: '4px',
      fontSize: '16px',
      fontWeight: '500',
      cursor: loading ? 'not-allowed' : 'pointer',
      transition: 'background-color 0.2s',
    },
    error: {
      color: '#dc3545',
      fontSize: '14px',
      marginBottom: '20px',
      textAlign: 'center' as const,
    },
    success: {
      color: '#28a745',
      fontSize: '14px',
      marginBottom: '20px',
      textAlign: 'center' as const,
    },
    linkButton: {
      display: 'block',
      width: '100%',
      padding: '12px',
      backgroundColor: '#6c757d',
      color: 'white',
      border: 'none',
      borderRadius: '4px',
      fontSize: '16px',
      fontWeight: '500',
      cursor: 'pointer',
      transition: 'background-color 0.2s',
      textAlign: 'center' as const,
      textDecoration: 'none',
      marginTop: '10px',
    }
  };

  // If token is expired, show a message and option to request a new reset link
  if (tokenStatus === 'expired') {
    return (
      <div style={styles.container}>
        <div style={styles.formBox}>
          <h2 style={styles.heading}>Password Reset Link Expired</h2>
          
          <div style={{ 
            marginBottom: '20px', 
            padding: '15px', 
            backgroundColor: '#f8d7da', 
            borderRadius: '4px', 
            border: '1px solid #f5c6cb',
            color: '#721c24',
            textAlign: 'center' as const
          }}>
            <p style={{ margin: '0 0 10px 0', fontSize: '14px' }}>
              Your password reset link has expired or is invalid.
            </p>
            <p style={{ margin: '0', fontSize: '14px' }}>
              Please request a new password reset link.
            </p>
          </div>
          
          <Link href="/reset-password" style={styles.linkButton}>
            Request New Reset Link
          </Link>
          
          <Link href="/login" style={{
            ...styles.linkButton,
            backgroundColor: '#28a745',
            marginTop: '10px'
          }}>
            Back to Login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div style={styles.container}>
      <div style={styles.formBox}>
        <h2 style={styles.heading}>Update Password</h2>

        <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '4px', border: '1px solid #e9ecef' }}>
          <p style={{ margin: '0 0 10px 0', fontSize: '14px', color: '#6c757d', textAlign: 'center' }}>
            Enter your email and new password below to update your account.
          </p>
        </div>

        <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#e7f3ff', borderRadius: '4px', border: '1px solid #b3d9ff' }}>
          <h4 style={{ margin: '0 0 10px 0', fontSize: '14px', color: '#0066cc', fontWeight: 'bold' }}>
            Password Requirements:
          </h4>
          <ul style={{ margin: 0, paddingLeft: '20px', fontSize: '13px', color: '#0066cc' }}>
            <li>At least 8 characters long</li>
            <li>One uppercase letter (A-Z)</li>
            <li>One lowercase letter (a-z)</li>
            <li>One number (0-9)</li>
            <li>One special character (!@#$%^&*(),.?":{}|&lt;&gt;)</li>
          </ul>
        </div>

        {errorMsg && <p style={styles.error}>{errorMsg}</p>}
        {successMsg && <p style={styles.success}>{successMsg}</p>}

        <form onSubmit={handleSubmit} style={styles.form}>
          {/* Always show email field for simplicity */}
          <div style={styles.field}>
            <label style={styles.label}>
              Email Address
              <input
                type="email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                style={styles.input}
                placeholder="Enter your email address"
                required
                disabled={loading}
              />
            </label>
          </div>

          <div style={styles.field}>
            <label style={styles.label}>
              New Password
              <input
                type="password"
                value={password}
                onChange={e => setPassword(e.target.value)}
                style={styles.input}
                placeholder="Enter new password"
                required
                disabled={loading}
              />
            </label>

            {password && (
              <div style={{ marginTop: '8px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px', fontSize: '12px' }}>
                <div style={{ marginBottom: '5px', fontWeight: 'bold', color: '#495057' }}>Password Requirements:</div>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                  <div style={{ color: passwordValidation.length ? '#28a745' : '#dc3545' }}>
                    {passwordValidation.length ? '✓' : '✗'} At least 8 characters
                  </div>
                  <div style={{ color: passwordValidation.uppercase ? '#28a745' : '#dc3545' }}>
                    {passwordValidation.uppercase ? '✓' : '✗'} One uppercase letter
                  </div>
                  <div style={{ color: passwordValidation.lowercase ? '#28a745' : '#dc3545' }}>
                    {passwordValidation.lowercase ? '✓' : '✗'} One lowercase letter
                  </div>
                  <div style={{ color: passwordValidation.number ? '#28a745' : '#dc3545' }}>
                    {passwordValidation.number ? '✓' : '✗'} One number
                  </div>
                  <div style={{ color: passwordValidation.special ? '#28a745' : '#dc3545' }}>
                    {passwordValidation.special ? '✓' : '✗'} One special character
                  </div>
                </div>
              </div>
            )}
          </div>

          <div style={styles.field}>
            <label style={styles.label}>
              Confirm Password
              <input
                type="password"
                value={confirmPassword}
                onChange={e => setConfirmPassword(e.target.value)}
                style={styles.input}
                placeholder="Confirm new password"
                required
                disabled={loading}
              />
            </label>

            {confirmPassword && (
              <div style={{ marginTop: '8px', fontSize: '12px' }}>
                <div style={{ color: password === confirmPassword ? '#28a745' : '#dc3545' }}>
                  {password === confirmPassword ? '✓ Passwords match' : '✗ Passwords do not match'}
                </div>
              </div>
            )}
          </div>

          <button
            type="submit"
            style={{
              ...styles.button,
              backgroundColor: loading || !Object.values(passwordValidation).every(Boolean) || password !== confirmPassword || !email
                ? '#ccc'
                : '#007bff',
              cursor: loading || !Object.values(passwordValidation).every(Boolean) || password !== confirmPassword || !email
                ? 'not-allowed'
                : 'pointer'
            }}
            disabled={loading || !Object.values(passwordValidation).every(Boolean) || password !== confirmPassword || !email}
          >
            {loading ? 'Processing...' : 'Update Password'}
          </button>
        </form>
      </div>
    </div>
  );
}

export default function UpdatePassword() {
  return (
    <UpdatePasswordForm />
  );
}
