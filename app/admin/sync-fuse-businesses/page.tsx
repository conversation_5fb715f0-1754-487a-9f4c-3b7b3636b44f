"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import { supabase } from "@/lib/supabase";
import { ProtectedRoute } from "@/components/protected-route";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Download, Upload, CheckCircle, AlertTriangle, Building2, RefreshCw } from "lucide-react";
import { useRouter } from "next/navigation";

export default function SyncFuseBusinessesPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [businesses, setBusinesses] = useState<any[]>([]);
  const [autoCreatedBusinesses, setAutoCreatedBusinesses] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [syncResults, setSyncResults] = useState<any[]>([]);

  useEffect(() => {
    loadBusinesses();
  }, []);

  const loadBusinesses = async () => {
    try {
      // Get all businesses
      const { data: allBusinesses, error } = await supabase
        .from('businesses')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading businesses:', error);
      } else {
        const regular = allBusinesses?.filter(b => !b.auto_created) || [];
        const autoCreated = allBusinesses?.filter(b => b.auto_created) || [];
        setBusinesses(regular);
        setAutoCreatedBusinesses(autoCreated);
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const createSampleFuseBusinesses = async () => {
    setSyncing(true);
    setSyncResults([]);

    // Comprehensive list of FUSE Network businesses for QR scanning rewards
    const fuseNetworkBusinesses = [
      // Health & Wellness
      { name: "Redlands Chiropractic", category: "Health & Wellness", description: "Specialized chiropractic care with FUSE rewards for loyal patients", website: "redlandschiropractic.com", address: "Redlands, California, USA" },
      { name: "Jorritsma Therapeutics", category: "Health & Wellness", description: "Specialized shockwave therapy with loyalty rewards", website: "jorritsmatherapeutics.stem-wave.com", address: "Redlands, California, USA" },
      { name: "FUSE Wellness Center", category: "Health & Wellness", description: "Comprehensive wellness and therapy services" },
      { name: "FUSE Dental Care", category: "Health & Wellness", description: "Complete dental services with VIP rewards" },
      { name: "FUSE Physical Therapy", category: "Health & Wellness", description: "Professional rehabilitation and therapy" },
      { name: "FUSE Fitness Studio", category: "Health & Wellness", description: "Personal training and fitness programs" },
      { name: "FUSE Yoga & Meditation", category: "Health & Wellness", description: "Mindfulness and wellness practices" },
      { name: "FUSE Nutrition Center", category: "Health & Wellness", description: "Nutritional counseling and supplements" },

      // Food & Beverage
      { name: "Protein Plus", category: "Food & Beverage", description: "Healthy meal options with special discounts for FUSE members", website: "protein-plus1.square.site", address: "San Bernardino, California, USA" },
      { name: "FUSE Coffee Roasters", category: "Food & Beverage", description: "Premium coffee and artisan pastries" },
      { name: "FUSE Pizza Kitchen", category: "Food & Beverage", description: "Artisan pizzas and Italian cuisine" },
      { name: "FUSE Smoothie Bar", category: "Food & Beverage", description: "Fresh smoothies and healthy snacks" },
      { name: "FUSE Burger Co", category: "Food & Beverage", description: "Gourmet burgers and craft beverages" },
      { name: "FUSE Sushi Bar", category: "Food & Beverage", description: "Fresh sushi and Japanese cuisine" },
      { name: "FUSE Bakery", category: "Food & Beverage", description: "Fresh baked goods and desserts" },
      { name: "FUSE Juice Bar", category: "Food & Beverage", description: "Cold-pressed juices and wellness shots" },

      // Retail & Shopping
      { name: "FUSE Fashion Boutique", category: "Retail", description: "Trendy clothing and accessories" },
      { name: "FUSE Electronics", category: "Retail", description: "Latest tech gadgets and accessories" },
      { name: "FUSE Bookstore", category: "Retail", description: "Books, magazines, and educational materials" },
      { name: "FUSE Market", category: "Retail", description: "Fresh groceries and local products" },
      { name: "FUSE Jewelry", category: "Retail", description: "Fine jewelry and custom pieces" },
      { name: "FUSE Home Decor", category: "Retail", description: "Furniture and home accessories" },
      { name: "FUSE Sports Store", category: "Retail", description: "Athletic gear and equipment" },
      { name: "FUSE Pet Store", category: "Retail", description: "Pet supplies and accessories" },

      // Services
      { name: "FUSE Tech Repair", category: "Service", description: "Electronics and device repair services" },
      { name: "FUSE Auto Service", category: "Service", description: "Complete automotive repair and maintenance" },
      { name: "FUSE Hair Salon", category: "Service", description: "Professional hair styling and beauty services" },
      { name: "FUSE Cleaning Service", category: "Service", description: "Residential and commercial cleaning" },
      { name: "FUSE Legal Services", category: "Service", description: "Legal consultation and representation" },
      { name: "FUSE Accounting", category: "Service", description: "Tax preparation and financial services" },
      { name: "FUSE Photography", category: "Service", description: "Professional photography services" },
      { name: "FUSE Landscaping", category: "Service", description: "Garden design and maintenance" },

      // Entertainment & Recreation
      { name: "FUSE Gaming Lounge", category: "Entertainment", description: "Video games and esports tournaments" },
      { name: "FUSE Movie Theater", category: "Entertainment", description: "Latest movies with VIP seating" },
      { name: "FUSE Bowling Alley", category: "Entertainment", description: "Bowling and arcade games" },
      { name: "FUSE Escape Room", category: "Entertainment", description: "Immersive puzzle experiences" },
      { name: "FUSE Karaoke Bar", category: "Entertainment", description: "Private karaoke rooms and drinks" },
      { name: "FUSE Mini Golf", category: "Entertainment", description: "Family-friendly mini golf course" },
      { name: "FUSE Arcade", category: "Entertainment", description: "Classic and modern arcade games" },
      { name: "FUSE Sports Bar", category: "Entertainment", description: "Live sports and craft beverages" },

      // Professional Services
      { name: "FUSE Real Estate", category: "Professional", description: "Property sales and rentals" },
      { name: "FUSE Insurance", category: "Professional", description: "Comprehensive insurance solutions" },
      { name: "FUSE Financial Planning", category: "Professional", description: "Investment and retirement planning" },
      { name: "FUSE Marketing Agency", category: "Professional", description: "Digital marketing and branding" },
      { name: "FUSE Web Design", category: "Professional", description: "Website development and design" },
      { name: "FUSE Consulting", category: "Professional", description: "Business strategy and consulting" },
      { name: "FUSE Translation", category: "Professional", description: "Document and interpretation services" },
      { name: "FUSE Event Planning", category: "Professional", description: "Wedding and corporate events" },

      // Automotive
      { name: "FUSE Car Wash", category: "Automotive", description: "Premium car detailing services" },
      { name: "FUSE Tire Center", category: "Automotive", description: "Tire sales and installation" },
      { name: "FUSE Oil Change", category: "Automotive", description: "Quick oil change and maintenance" },
      { name: "FUSE Auto Parts", category: "Automotive", description: "Automotive parts and accessories" },
      { name: "FUSE Body Shop", category: "Automotive", description: "Collision repair and painting" },
      { name: "FUSE Car Rental", category: "Automotive", description: "Vehicle rental services" },

      // Beauty & Personal Care
      { name: "FUSE Nail Salon", category: "Beauty", description: "Manicures, pedicures, and nail art" },
      { name: "FUSE Spa", category: "Beauty", description: "Relaxation and rejuvenation treatments" },
      { name: "FUSE Barbershop", category: "Beauty", description: "Traditional and modern haircuts" },
      { name: "FUSE Massage Therapy", category: "Beauty", description: "Therapeutic and relaxation massage" },
      { name: "FUSE Skincare Clinic", category: "Beauty", description: "Advanced skincare treatments" },
      { name: "FUSE Tanning Salon", category: "Beauty", description: "UV and spray tanning services" },

      // Education & Training
      { name: "FUSE Learning Center", category: "Education", description: "Tutoring and test preparation" },
      { name: "FUSE Music School", category: "Education", description: "Music lessons and instrument rental" },
      { name: "FUSE Art Studio", category: "Education", description: "Art classes and creative workshops" },
      { name: "FUSE Language School", category: "Education", description: "Foreign language instruction" },
      { name: "FUSE Driving School", category: "Education", description: "Driver education and training" },
      { name: "FUSE Computer Training", category: "Education", description: "Technology and software training" },

      // Specialty Services
      { name: "FUSE Dry Cleaning", category: "Specialty", description: "Professional garment care" },
      { name: "FUSE Locksmith", category: "Specialty", description: "Lock installation and repair" },
      { name: "FUSE Printing", category: "Specialty", description: "Business cards and promotional materials" },
      { name: "FUSE Storage", category: "Specialty", description: "Self-storage units and moving supplies" },
      { name: "FUSE Florist", category: "Specialty", description: "Fresh flowers and arrangements" },
      { name: "FUSE Travel Agency", category: "Specialty", description: "Vacation planning and booking" },

      // Additional FUSE Partners
      { name: "FUSE Coworking Space", category: "Professional", description: "Shared office space and meeting rooms" },
      { name: "FUSE Bike Shop", category: "Retail", description: "Bicycles, repairs, and accessories" },
      { name: "FUSE Pharmacy", category: "Health & Wellness", description: "Prescription medications and health products" },
      { name: "FUSE Hardware Store", category: "Retail", description: "Tools, supplies, and home improvement" }
    ];

    const results = [];

    for (const business of fuseNetworkBusinesses) {
      try {
        const { data, error } = await supabase
          .from('businesses')
          .insert({
            name: business.name,
            category: business.category,
            description: business.description,
            is_active: true,
            user_id: user?.id,
            website: business.website || `https://fuse.vip/${business.name.toLowerCase().replace(/\s+/g, '-')}`,
            phone: '(*************',
            email: `contact@${business.name.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '')}.com`,
            address: business.address || `123 FUSE Street, FUSE City, FC 12345`,
            auto_created: false // These are proper FUSE Network businesses
          })
          .select()
          .single();

        if (error) {
          results.push({ business: business.name, success: false, error: error.message });
        } else {
          results.push({ business: business.name, success: true, id: data.id });
        }
      } catch (error) {
        results.push({ business: business.name, success: false, error: String(error) });
      }
    }

    setSyncResults(results);
    await loadBusinesses();
    setSyncing(false);
  };

  const approveAutoCreatedBusinesses = async () => {
    try {
      const { error } = await supabase
        .from('businesses')
        .update({
          is_active: true,
          name: 'FUSE Network Partner',
          category: 'FUSE Partner',
          description: 'Verified FUSE Network partner business'
        })
        .eq('auto_created', true)
        .eq('is_active', false);

      if (error) {
        console.error('Error approving businesses:', error);
        alert(`Error: ${error.message}`);
      } else {
        await loadBusinesses();
        alert('Auto-created businesses approved!');
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Failed to approve businesses');
    }
  };

  const exportBusinessIds = () => {
    const allBusinesses = [...businesses, ...autoCreatedBusinesses];
    const businessData = allBusinesses.map(b => ({
      id: b.id,
      name: b.name,
      category: b.category,
      status: b.status,
      auto_created: b.auto_created || false
    }));

    const dataStr = JSON.stringify(businessData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'fuse-businesses.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <ProtectedRoute requiredRole="admin">
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredRole="admin">
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <Button
              onClick={() => router.push('/admin')}
              variant="ghost"
              className="text-white hover:bg-white/10 mr-4"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Admin
            </Button>
          </div>

          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">
                🔄 FUSE Network Business Sync
              </h1>
              <p className="text-gray-300">
                Manage and sync businesses from the FUSE Network
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <Card>
                <CardContent className="p-6 text-center">
                  <Building2 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{businesses.length}</div>
                  <div className="text-gray-600 text-sm">Regular Businesses</div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <RefreshCw className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{autoCreatedBusinesses.length}</div>
                  <div className="text-gray-600 text-sm">Auto-Created from QR</div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <CheckCircle className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{businesses.length + autoCreatedBusinesses.length}</div>
                  <div className="text-gray-600 text-sm">Total Businesses</div>
                </CardContent>
              </Card>
            </div>

            {/* Actions */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Upload className="h-5 w-5" />
                    Sync FUSE Network Businesses
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-600 text-sm">
                    Create all 74+ FUSE Network businesses with QR codes for the rewards system. Each business will be auto-approved and ready for QR scanning.
                  </p>

                  <Button
                    onClick={createSampleFuseBusinesses}
                    disabled={syncing}
                    className="w-full bg-blue-600 hover:bg-blue-700"
                  >
                    {syncing ? 'Creating 74+ Businesses...' : 'Create All FUSE Network Businesses'}
                  </Button>

                  <Button
                    onClick={approveAutoCreatedBusinesses}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    Approve Auto-Created Businesses
                  </Button>

                  <Button
                    onClick={exportBusinessIds}
                    variant="outline"
                    className="w-full"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export Business Data
                  </Button>

                  {syncResults.length > 0 && (
                    <div className="bg-gray-50 rounded-lg p-4 max-h-40 overflow-y-auto">
                      <h4 className="font-semibold text-gray-900 mb-2">Sync Results:</h4>
                      {syncResults.map((result, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm">
                          {result.success ? (
                            <CheckCircle className="h-3 w-3 text-green-600" />
                          ) : (
                            <AlertTriangle className="h-3 w-3 text-red-600" />
                          )}
                          <span className={result.success ? 'text-green-700' : 'text-red-700'}>
                            {result.business}: {result.success ? 'Created' : result.error}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Current Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="font-semibold text-blue-900 mb-2">What's Happening:</h4>
                    <ul className="text-blue-800 text-sm space-y-1">
                      <li>• QR codes contain business IDs from FUSE Network</li>
                      <li>• Some businesses exist in network but not local database</li>
                      <li>• Auto-creation handles missing businesses gracefully</li>
                      <li>• Manual sync creates proper business profiles</li>
                    </ul>
                  </div>

                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="font-semibold text-green-900 mb-2">Solutions:</h4>
                    <ul className="text-green-800 text-sm space-y-1">
                      <li>✓ Auto-create missing businesses from QR scans</li>
                      <li>✓ Create sample FUSE businesses for testing</li>
                      <li>✓ Approve auto-created businesses in bulk</li>
                      <li>✓ Export business data for analysis</li>
                    </ul>
                  </div>

                  <Button
                    onClick={() => router.push('/test-qr-scan')}
                    className="w-full bg-purple-600 hover:bg-purple-700"
                  >
                    Test QR Scanning
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Auto-Created Businesses */}
            {autoCreatedBusinesses.length > 0 && (
              <Card className="mt-8">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <RefreshCw className="h-5 w-5" />
                    Auto-Created Businesses ({autoCreatedBusinesses.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm mb-4">
                    These businesses were automatically created when users scanned QR codes for businesses not in the database.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {autoCreatedBusinesses.map((business) => (
                      <div key={business.id} className="border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900">{business.name}</h4>
                          <span className={`text-xs px-2 py-1 rounded ${
                            business.status === 'approved' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {business.status}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600">{business.category}</p>
                        <p className="text-xs text-gray-400 mt-1">ID: {business.id.slice(0, 8)}...</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
