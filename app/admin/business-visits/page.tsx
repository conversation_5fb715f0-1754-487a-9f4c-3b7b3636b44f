"use client";

import { useState, useEffect } from "react";
import { PageHeader } from "@/components/page-header";
import { AnimatedCard } from "@/components/animated-card";
import { useAuth } from "@/contexts/auth-context";
import { supabase } from "@/lib/supabase";
import { ProtectedRoute } from "@/components/protected-route";
import { BarChart3, TrendingUp, Users, Calendar, MapPin } from "lucide-react";

interface BusinessVisitStats {
  business_id: string;
  business_name: string;
  business_category: string;
  total_visits: number;
  unique_visitors: number;
  visits_this_week: number;
  visits_this_month: number;
  last_visit: string;
}

export default function AdminBusinessVisitsPage() {
  const { user } = useAuth();
  const [stats, setStats] = useState<BusinessVisitStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalStats, setTotalStats] = useState({
    totalVisits: 0,
    totalBusinesses: 0,
    totalRewards: 0,
    visitsThisWeek: 0
  });

  useEffect(() => {
    loadBusinessVisitStats();
  }, []);

  async function loadBusinessVisitStats() {
    setLoading(true);
    try {
      // Get current date ranges
      const now = new Date();
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Fetch business visit data with business information
      const { data: visits, error } = await supabase
        .from('business_visits')
        .select(`
          business_id,
          user_id,
          scanned_at,
          businesses:business_id (
            name,
            category
          )
        `)
        .order('scanned_at', { ascending: false });

      if (error) {
        console.error("Error loading business visits:", error);
        return;
      }

      // Process the data to create statistics
      const businessStatsMap = new Map<string, BusinessVisitStats>();
      let totalVisits = 0;
      let visitsThisWeek = 0;

      visits?.forEach(visit => {
        const businessId = visit.business_id;
        const visitDate = new Date(visit.scanned_at);
        const businessName = visit.businesses?.name || 'Unknown Business';
        const businessCategory = visit.businesses?.category || 'Unknown';

        totalVisits++;
        if (visitDate >= oneWeekAgo) {
          visitsThisWeek++;
        }

        if (!businessStatsMap.has(businessId)) {
          businessStatsMap.set(businessId, {
            business_id: businessId,
            business_name: businessName,
            business_category: businessCategory,
            total_visits: 0,
            unique_visitors: 0,
            visits_this_week: 0,
            visits_this_month: 0,
            last_visit: visit.scanned_at
          });
        }

        const stats = businessStatsMap.get(businessId)!;
        stats.total_visits++;
        
        if (visitDate >= oneWeekAgo) {
          stats.visits_this_week++;
        }
        
        if (visitDate >= oneMonthAgo) {
          stats.visits_this_month++;
        }

        // Update last visit if this one is more recent
        if (new Date(visit.scanned_at) > new Date(stats.last_visit)) {
          stats.last_visit = visit.scanned_at;
        }
      });

      // Calculate unique visitors for each business
      for (const [businessId, stats] of businessStatsMap) {
        const uniqueVisitors = new Set(
          visits
            ?.filter(v => v.business_id === businessId)
            .map(v => v.user_id)
        ).size;
        stats.unique_visitors = uniqueVisitors;
      }

      const businessStats = Array.from(businessStatsMap.values())
        .sort((a, b) => b.total_visits - a.total_visits);

      setStats(businessStats);
      setTotalStats({
        totalVisits,
        totalBusinesses: businessStats.length,
        totalRewards: totalVisits * 100, // 100 FUSE per visit
        visitsThisWeek
      });

    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <ProtectedRoute requiredRole="admin">
      <PageHeader
        title="Business Visit Analytics"
        subtitle="ADMIN PORTAL"
        description="Monitor QR code scans and FUSE reward distribution across all businesses."
      />

      <section className="py-16">
        <div className="container mx-auto px-4">
          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm text-center">
              <BarChart3 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-3xl font-bold text-gray-900">{totalStats.totalVisits}</div>
              <div className="text-gray-600 text-sm">Total Visits</div>
            </AnimatedCard>

            <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm text-center">
              <MapPin className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-3xl font-bold text-gray-900">{totalStats.totalBusinesses}</div>
              <div className="text-gray-600 text-sm">Active Businesses</div>
            </AnimatedCard>

            <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm text-center">
              <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-3xl font-bold text-gray-900">{totalStats.totalRewards.toLocaleString()}</div>
              <div className="text-gray-600 text-sm">FUSE Distributed</div>
            </AnimatedCard>

            <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm text-center">
              <Calendar className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="text-3xl font-bold text-gray-900">{totalStats.visitsThisWeek}</div>
              <div className="text-gray-600 text-sm">This Week</div>
            </AnimatedCard>
          </div>

          {/* Business Visit Table */}
          <AnimatedCard className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-bold text-gray-900">Business Performance</h2>
              <p className="text-gray-600 text-sm">QR code scan statistics by business</p>
            </div>

            {loading ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#3A56FF]"></div>
              </div>
            ) : stats.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Business
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Visits
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unique Visitors
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        This Week
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        This Month
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        FUSE Distributed
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Last Visit
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {stats.map((business) => (
                      <tr key={business.business_id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {business.business_name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {business.business_category}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-semibold text-gray-900">
                            {business.total_visits}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {business.unique_visitors}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {business.visits_this_week}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {business.visits_this_month}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-semibold text-green-600">
                            {(business.total_visits * 100).toLocaleString()} FUSE
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {formatDate(business.last_visit)}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <BarChart3 className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  No business visits yet
                </h3>
                <p className="text-gray-600">
                  Business visit data will appear here once customers start scanning QR codes.
                </p>
              </div>
            )}
          </AnimatedCard>
        </div>
      </section>
    </ProtectedRoute>
  );
}
