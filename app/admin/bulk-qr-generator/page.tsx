"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import { supabase } from "@/lib/supabase";
import { ProtectedRoute } from "@/components/protected-route";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft, Download, QrCode, CheckCircle, AlertTriangle, Package } from "lucide-react";
import { useRouter } from "next/navigation";
import JSZip from "jszip";

interface Business {
  id: string;
  name: string;
  category: string;
  is_active: boolean;
}

interface QRGenerationResult {
  business: Business;
  success: boolean;
  qrCodeUrl?: string;
  error?: string;
}

export default function BulkQRGeneratorPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<QRGenerationResult[]>([]);
  const [currentBusiness, setCurrentBusiness] = useState<string>('');

  useEffect(() => {
    loadBusinesses();
  }, []);

  const loadBusinesses = async () => {
    try {
      const { data, error } = await supabase
        .from('businesses')
        .select('id, name, category, is_active')
        .eq('is_active', true)
        .order('name');

      if (error) {
        console.error('Error loading businesses:', error);
      } else {
        setBusinesses(data || []);
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateQRCode = async (business: Business): Promise<QRGenerationResult> => {
    try {
      setCurrentBusiness(business.name);

      const response = await fetch('/api/generate-business-qr', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessId: business.id,
          size: 512 // High resolution for printing
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate QR code');
      }

      return {
        business,
        success: true,
        qrCodeUrl: result.qrCodeUrl
      };
    } catch (error) {
      return {
        business,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };

  const generateAllQRCodes = async () => {
    if (businesses.length === 0) return;

    setGenerating(true);
    setProgress(0);
    setResults([]);

    const generationResults: QRGenerationResult[] = [];

    for (let i = 0; i < businesses.length; i++) {
      const business = businesses[i];
      const result = await generateQRCode(business);
      
      generationResults.push(result);
      setResults([...generationResults]);
      setProgress(((i + 1) / businesses.length) * 100);

      // Small delay to prevent overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    setCurrentBusiness('');
    setGenerating(false);
  };

  const downloadAllQRCodes = async () => {
    const successfulResults = results.filter(r => r.success && r.qrCodeUrl);
    
    if (successfulResults.length === 0) {
      alert('No QR codes to download');
      return;
    }

    const zip = new JSZip();
    const qrFolder = zip.folder("FUSE_Business_QR_Codes");

    for (const result of successfulResults) {
      try {
        // Convert data URL to blob
        const response = await fetch(result.qrCodeUrl!);
        const blob = await response.blob();
        
        // Clean filename
        const filename = `${result.business.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_qr_code.png`;
        qrFolder?.file(filename, blob);
      } catch (error) {
        console.error(`Failed to add ${result.business.name} to zip:`, error);
      }
    }

    // Generate and download zip file
    try {
      const content = await zip.generateAsync({ type: "blob" });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(content);
      link.download = `FUSE_Business_QR_Codes_${new Date().toISOString().split('T')[0]}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    } catch (error) {
      console.error('Failed to generate zip file:', error);
      alert('Failed to create download file');
    }
  };

  const downloadIndividualQR = async (result: QRGenerationResult) => {
    if (!result.qrCodeUrl) return;

    const link = document.createElement('a');
    link.href = result.qrCodeUrl;
    link.download = `${result.business.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_qr_code.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <ProtectedRoute requiredRole="admin">
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredRole="admin">
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <Button
              onClick={() => router.push('/admin')}
              variant="ghost"
              className="text-white hover:bg-white/10 mr-4"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Admin
            </Button>
          </div>

          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">
                📱 Bulk QR Code Generator
              </h1>
              <p className="text-gray-300">
                Generate high-resolution QR codes for all {businesses.length} active businesses
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardContent className="p-6 text-center">
                  <QrCode className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{businesses.length}</div>
                  <div className="text-gray-600 text-sm">Total Businesses</div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{results.filter(r => r.success).length}</div>
                  <div className="text-gray-600 text-sm">Generated</div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <AlertTriangle className="h-8 w-8 text-red-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{results.filter(r => !r.success).length}</div>
                  <div className="text-gray-600 text-sm">Failed</div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <Package className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">512px</div>
                  <div className="text-gray-600 text-sm">High Resolution</div>
                </CardContent>
              </Card>
            </div>

            {/* Controls */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>QR Code Generation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {generating && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Generating QR codes...</span>
                      <span>{Math.round(progress)}%</span>
                    </div>
                    <Progress value={progress} className="w-full" />
                    {currentBusiness && (
                      <p className="text-sm text-gray-600">
                        Current: {currentBusiness}
                      </p>
                    )}
                  </div>
                )}

                <div className="flex gap-4">
                  <Button
                    onClick={generateAllQRCodes}
                    disabled={generating || businesses.length === 0}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {generating ? 'Generating...' : `Generate All ${businesses.length} QR Codes`}
                  </Button>

                  {results.length > 0 && (
                    <Button
                      onClick={downloadAllQRCodes}
                      disabled={generating}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download All as ZIP
                    </Button>
                  )}
                </div>

                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-900 mb-2">What you'll get:</h4>
                  <ul className="text-blue-800 text-sm space-y-1">
                    <li>• High-resolution 512x512px PNG files</li>
                    <li>• One QR code per active business</li>
                    <li>• Files named by business name</li>
                    <li>• All files in a single ZIP download</li>
                    <li>• Ready for printing and display</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Results */}
            {results.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Generation Results</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {results.map((result, index) => (
                      <div 
                        key={index}
                        className={`flex items-center justify-between p-3 rounded-lg border ${
                          result.success 
                            ? 'bg-green-50 border-green-200' 
                            : 'bg-red-50 border-red-200'
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          {result.success ? (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          ) : (
                            <AlertTriangle className="h-5 w-5 text-red-600" />
                          )}
                          <div>
                            <h4 className="font-medium">{result.business.name}</h4>
                            <p className="text-sm text-gray-600">{result.business.category}</p>
                            {result.error && (
                              <p className="text-sm text-red-600">{result.error}</p>
                            )}
                          </div>
                        </div>
                        
                        {result.success && result.qrCodeUrl && (
                          <div className="flex items-center gap-2">
                            <img 
                              src={result.qrCodeUrl} 
                              alt={`QR for ${result.business.name}`}
                              className="w-12 h-12 border rounded"
                            />
                            <Button
                              onClick={() => downloadIndividualQR(result)}
                              size="sm"
                              variant="outline"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
