"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import { supabase } from "@/lib/supabase";
import { ProtectedRoute } from "@/components/protected-route";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ArrowLeft, Plus, Building2, QrCode, CheckCircle, AlertTriangle } from "lucide-react";
import { useRouter } from "next/navigation";
import { BusinessQRCode } from "@/components/business-qr-code";

export default function QRTestSetupPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [businesses, setBusinesses] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [newBusiness, setNewBusiness] = useState({
    name: '',
    category: 'Restaurant',
    description: ''
  });

  useEffect(() => {
    loadBusinesses();
  }, []);

  const loadBusinesses = async () => {
    try {
      const { data, error } = await supabase
        .from('businesses')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading businesses:', error);
      } else {
        setBusinesses(data || []);
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const createTestBusiness = async () => {
    if (!newBusiness.name.trim()) return;

    setCreating(true);
    try {
      const { data, error } = await supabase
        .from('businesses')
        .insert({
          name: newBusiness.name,
          category: newBusiness.category,
          description: newBusiness.description || `Test business: ${newBusiness.name}`,
          status: 'approved', // Auto-approve for testing
          user_id: user?.id,
          website: `https://example.com/${newBusiness.name.toLowerCase().replace(/\s+/g, '-')}`,
          phone: '(*************',
          email: `contact@${newBusiness.name.toLowerCase().replace(/\s+/g, '')}.com`,
          address: '123 Test Street, Test City, TC 12345'
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating business:', error);
        alert(`Error creating business: ${error.message}`);
      } else {
        setBusinesses(prev => [data, ...prev]);
        setNewBusiness({ name: '', category: 'Restaurant', description: '' });
        alert('Test business created successfully!');
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Failed to create business');
    } finally {
      setCreating(false);
    }
  };

  const approveAllBusinesses = async () => {
    try {
      const { error } = await supabase
        .from('businesses')
        .update({ status: 'approved' })
        .neq('status', 'approved');

      if (error) {
        console.error('Error approving businesses:', error);
        alert(`Error: ${error.message}`);
      } else {
        await loadBusinesses();
        alert('All businesses approved!');
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Failed to approve businesses');
    }
  };

  const categories = [
    'Restaurant', 'Retail', 'Service', 'Entertainment', 'Health & Wellness',
    'Technology', 'Education', 'Finance', 'Real Estate', 'Other'
  ];

  if (loading) {
    return (
      <ProtectedRoute requiredRole="admin">
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredRole="admin">
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <Button
              onClick={() => router.push('/admin')}
              variant="ghost"
              className="text-white hover:bg-white/10 mr-4"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Admin
            </Button>
          </div>

          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">
                🧪 QR Testing Setup
              </h1>
              <p className="text-gray-300">
                Create test businesses and generate QR codes for testing
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Create Test Business */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Plus className="h-5 w-5" />
                    Create Test Business
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Business Name</label>
                    <Input
                      value={newBusiness.name}
                      onChange={(e) => setNewBusiness(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Demo Coffee Shop"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Category</label>
                    <select
                      value={newBusiness.category}
                      onChange={(e) => setNewBusiness(prev => ({ ...prev, category: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {categories.map(cat => (
                        <option key={cat} value={cat}>{cat}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Description (Optional)</label>
                    <Input
                      value={newBusiness.description}
                      onChange={(e) => setNewBusiness(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Brief description..."
                    />
                  </div>

                  <Button
                    onClick={createTestBusiness}
                    disabled={creating || !newBusiness.name.trim()}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    {creating ? 'Creating...' : 'Create Test Business'}
                  </Button>

                  <div className="bg-blue-50 rounded-lg p-3">
                    <p className="text-blue-800 text-sm">
                      <strong>Note:</strong> Test businesses are automatically approved and ready for QR generation.
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button
                    onClick={approveAllBusinesses}
                    className="w-full bg-blue-600 hover:bg-blue-700"
                  >
                    Approve All Pending Businesses
                  </Button>

                  <Button
                    onClick={() => router.push('/test-qr-scan')}
                    className="w-full bg-purple-600 hover:bg-purple-700"
                  >
                    <QrCode className="h-4 w-4 mr-2" />
                    Go to QR Testing Page
                  </Button>

                  <Button
                    onClick={() => router.push('/admin/businesses')}
                    variant="outline"
                    className="w-full"
                  >
                    <Building2 className="h-4 w-4 mr-2" />
                    Manage All Businesses
                  </Button>

                  <div className="bg-yellow-50 rounded-lg p-3">
                    <h4 className="text-yellow-800 font-semibold text-sm mb-1">Testing Checklist:</h4>
                    <ul className="text-yellow-700 text-xs space-y-1">
                      <li>✓ Create approved businesses</li>
                      <li>✓ Set user as VIP cardholder</li>
                      <li>✓ Run database migration</li>
                      <li>✓ Test QR scanning</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Existing Businesses */}
            <Card className="mt-8">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Existing Businesses ({businesses.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {businesses.length === 0 ? (
                  <div className="text-center py-8">
                    <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      No Businesses Yet
                    </h3>
                    <p className="text-gray-600">
                      Create your first test business above to start testing QR codes.
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {businesses.map((business) => (
                      <div key={business.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-semibold text-gray-900">{business.name}</h4>
                          <div className="flex items-center gap-1">
                            {business.status === 'approved' ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                              <AlertTriangle className="h-4 w-4 text-yellow-600" />
                            )}
                            <span className={`text-xs px-2 py-1 rounded ${
                              business.status === 'approved' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {business.status}
                            </span>
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-3">{business.category}</p>
                        <p className="text-xs text-gray-400 mb-3">ID: {business.id.slice(0, 8)}...</p>
                        
                        {business.status === 'approved' && (
                          <div className="space-y-3">
                            <BusinessQRCode
                              businessId={business.id}
                              businessName={business.name}
                              size={150}
                              showActions={false}
                              className="border-0 shadow-none p-0"
                            />
                            <Button
                              onClick={() => router.push(`/test-qr-scan?business=${business.id}`)}
                              size="sm"
                              className="w-full bg-blue-600 hover:bg-blue-700"
                            >
                              Test This QR Code
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
