'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { useWallet } from '@/hooks/use-wallet';
import { PageHeader } from '@/components/page-header';
import { XamanWalletConnect } from '@/components/wallet/xaman-wallet-connect';
import { PaymentForm } from '@/components/wallet/payment-form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle } from 'lucide-react';
import { AnimatedSection } from '@/components/animated-section';
import { WalletDebug } from '@/components/wallet/wallet-debug';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function WalletPage() {
  const { user } = useAuth();
  const { isConnected, xrpWalletAddress } = useWallet();
  const [showSuccess, setShowSuccess] = useState(false);
  const isDev = process.env.NODE_ENV === 'development';
  
  useEffect(() => {
    if (isConnected && xrpWalletAddress) {
      // Show success message when wallet is connected
      if (user?.id) {
        setShowSuccess(true);
        
        // Hide success message after 5 seconds
        const timer = setTimeout(() => {
          setShowSuccess(false);
        }, 5000);
        
        return () => clearTimeout(timer);
      }
    }
  }, [isConnected, xrpWalletAddress, user?.id]);
  
  return (
    <>
      <PageHeader
        title="Wallet Dashboard"
        subtitle="MANAGE YOUR WALLET"
        description="Connect your wallet to manage your tokens, track your rewards, and upgrade your membership tier."
      />

      <section className="py-12 bg-[#121212]">
        <div className="container mx-auto px-4">
          {showSuccess && (
            <Alert className="mb-6 bg-green-900/20 border-green-800 text-green-400">
              <CheckCircle className="h-4 w-4 mr-2" />
              <AlertDescription>
                Wallet successfully connected and saved to your profile!
              </AlertDescription>
            </Alert>
          )}
          
          <AnimatedSection>
            <Tabs defaultValue="connect" className="w-full max-w-md mx-auto">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="connect">Connect</TabsTrigger>
                <TabsTrigger value="send" disabled={!isConnected}>Send XRP</TabsTrigger>
              </TabsList>
              <TabsContent value="connect">
                <XamanWalletConnect />
              </TabsContent>
              <TabsContent value="send">
                <PaymentForm />
              </TabsContent>
            </Tabs>
          </AnimatedSection>
          
          {isDev && <WalletDebug />}
        </div>
      </section>
    </>
  );
}
