"use client";

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import Link from 'next/link';

// Password validation function
function validatePassword(password: string): string | null {
  if (password.length < 8) {
    return 'Password must be at least 8 characters long';
  }
  
  // Check for at least one uppercase letter
  if (!/[A-Z]/.test(password)) {
    return 'Password must contain at least one uppercase letter';
  }
  
  // Check for at least one lowercase letter
  if (!/[a-z]/.test(password)) {
    return 'Password must contain at least one lowercase letter';
  }
  
  // Check for at least one number
  if (!/[0-9]/.test(password)) {
    return 'Password must contain at least one number';
  }
  
  return null;
}

export default function UpdatePasswordPage() {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [email, setEmail] = useState('');
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const [loading, setLoading] = useState(false);
  const [hasSession, setHasSession] = useState(false);
  const [tokenStatus, setTokenStatus] = useState<'checking' | 'valid' | 'expired'>('checking');
  
  const router = useRouter();
  const searchParams = useSearchParams();

  // Check for active session and email parameter on component mount
  useEffect(() => {
    const checkSessionAndEmail = async () => {
      // Check for email in URL parameters
      const emailParam = searchParams.get('email');
      if (emailParam) {
        const decodedEmail = decodeURIComponent(emailParam);
        console.log('Setting email from URL param:', decodedEmail);
        setEmail(decodedEmail);
      }

      // Check for token hash and type
      const tokenHash = searchParams.get('token_hash');
      const type = searchParams.get('type');

      // If we have token parameters, this is a recovery flow
      if (tokenHash && type === 'recovery') {
        console.log('Recovery flow detected with token:', tokenHash);
        
        // Verify the token is valid
        try {
          // Try to verify the token
          const { error } = await supabase.auth.verifyOtp({
            token_hash: tokenHash,
            type: 'recovery',
          });
          
          if (error) {
            console.error('Token verification failed:', error);
            setTokenStatus('expired');
            setErrorMsg('Your password reset link has expired. Please request a new one.');
          } else {
            setTokenStatus('valid');
          }
        } catch (error) {
          console.error('Token verification error:', error);
          setTokenStatus('expired');
          setErrorMsg('Your password reset link has expired. Please request a new one.');
        }
      } else {
        // No token parameters, check for active session
        if (supabase) {
          const { data } = await supabase.auth.getSession();
          if (data.session?.user) {
            setHasSession(true);
            if (data.session.user.email && !email) {
              console.log('Setting email from Supabase session:', data.session.user.email);
              setEmail(data.session.user.email);
            }
            setTokenStatus('valid'); // User has session, so they can update password
          } else {
            // No session and no token - invalid state
            if (!tokenHash) {
              setTokenStatus('expired');
              setErrorMsg('Invalid password reset request. Please request a new password reset link.');
            }
          }
        }
      }
    };

    checkSessionAndEmail();
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMsg('');
    setSuccessMsg('');
    setLoading(true);

    try {
      if (password !== confirmPassword) {
        throw new Error('Passwords do not match');
      }

      const passwordError = validatePassword(password);
      if (passwordError) {
        throw new Error(passwordError);
      }

      if (!email) {
        throw new Error('Email is required');
      }

      console.log('Attempting to update password via API...');

      // Always use the admin API endpoint for consistent behavior
      const response = await fetch('/api/auth/update-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Password update failed');
      }

      console.log('Password updated successfully via admin API');

      // Determine where to redirect based on session state
      if (hasSession) {
        // User has an active session, redirect to dashboard
        setSuccessMsg('Password updated successfully! Redirecting to dashboard...');
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);
      } else {
        // No active session, redirect to login
        setSuccessMsg('Password updated successfully! Redirecting to login...');
        setTimeout(async () => {
          if (supabase) {
            await supabase.auth.signOut();
          }
          router.push('/login');
        }, 2000);
      }

    } catch (error: any) {
      console.error('Password update failed:', error);
      setErrorMsg(error.message || 'Password update failed');
    } finally {
      setLoading(false);
    }
  };

  // If token is expired, show a message and option to request a new reset link
  if (tokenStatus === 'expired') {
    return (
      <div style={styles.container}>
        <div style={styles.formBox}>
          <h2 style={styles.heading}>Password Reset Link Expired</h2>
          
          <div style={{ 
            marginBottom: '20px', 
            padding: '15px', 
            backgroundColor: '#f8d7da', 
            borderRadius: '4px', 
            border: '1px solid #f5c6cb',
            color: '#721c24',
            textAlign: 'center' as const
          }}>
            <p style={{ margin: '0 0 10px 0', fontSize: '14px' }}>
              Your password reset link has expired or is invalid.
            </p>
            <p style={{ margin: '0', fontSize: '14px' }}>
              Please request a new password reset link.
            </p>
          </div>
          
          <Link href="/reset-password" style={styles.linkButton}>
            Request New Reset Link
          </Link>
        </div>
      </div>
    );
  }

  // If still checking token status, show loading
  if (tokenStatus === 'checking') {
    return (
      <div style={styles.container}>
        <div style={styles.formBox}>
          <h2 style={styles.heading}>Verifying Reset Link...</h2>
          <div style={{ textAlign: 'center' as const }}>
            <p>Please wait while we verify your password reset link.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={styles.container}>
      <div style={styles.formBox}>
        <h2 style={styles.heading}>Update Password</h2>
        
        {errorMsg && <p style={styles.error}>{errorMsg}</p>}
        {successMsg && <p style={styles.success}>{successMsg}</p>}
        
        <form onSubmit={handleSubmit} style={styles.form}>
          {!hasSession && !email && (
            <div style={styles.field}>
              <label style={styles.label}>
                Email Address
                <input
                  type="email"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  style={styles.input}
                  placeholder="Enter your email"
                  required
                />
              </label>
            </div>
          )}
          
          <div style={styles.field}>
            <label style={styles.label}>
              New Password
              <input
                type="password"
                value={password}
                onChange={e => setPassword(e.target.value)}
                style={styles.input}
                placeholder="Enter new password"
                required
              />
            </label>
            <p style={styles.hint}>
              Password must be at least 8 characters with uppercase, lowercase, and numbers.
            </p>
          </div>
          
          <div style={styles.field}>
            <label style={styles.label}>
              Confirm Password
              <input
                type="password"
                value={confirmPassword}
                onChange={e => setConfirmPassword(e.target.value)}
                style={styles.input}
                placeholder="Confirm new password"
                required
              />
            </label>
          </div>
          
          <button
            type="submit"
            style={styles.button}
            disabled={loading}
          >
            {loading ? 'Updating...' : 'Update Password'}
          </button>
        </form>
        
        <div style={styles.links}>
          <Link href="/login" style={styles.link}>
            Back to Login
          </Link>
        </div>
      </div>
    </div>
  );
}

const styles = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    padding: '20px',
    backgroundColor: '#f9fafb',
  },
  formBox: {
    backgroundColor: 'white',
    padding: '40px',
    borderRadius: '8px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    width: '100%',
    maxWidth: '400px',
  },
  heading: {
    textAlign: 'center' as const,
    marginBottom: '30px',
    color: '#333',
    fontSize: '24px',
    fontWeight: 'bold',
  },
  form: {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '20px',
  },
  field: {
    display: 'flex',
    flexDirection: 'column' as const,
  },
  label: {
    marginBottom: '8px',
    color: '#555',
    fontSize: '14px',
    fontWeight: '500',
  },
  input: {
    padding: '12px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '16px',
    transition: 'border-color 0.2s',
    outline: 'none',
  },
  button: {
    padding: '12px',
    backgroundColor: loading ? '#ccc' : '#007bff',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    fontSize: '16px',
    fontWeight: '500',
    cursor: loading ? 'not-allowed' : 'pointer',
    transition: 'background-color 0.2s',
  },
  error: {
    color: '#dc3545',
    fontSize: '14px',
    marginBottom: '20px',
    textAlign: 'center' as const,
  },
  success: {
    color: '#28a745',
    fontSize: '14px',
    marginBottom: '20px',
    textAlign: 'center' as const,
  },
  hint: {
    fontSize: '12px',
    color: '#6c757d',
    marginBottom: '10px',
  },
  links: {
    display: 'flex',
    justifyContent: 'space-between',
    marginTop: '20px',
  },
  link: {
    color: '#007bff',
    textDecoration: 'none',
    fontSize: '14px',
  },
  linkButton: {
    padding: '10px 15px',
    backgroundColor: '#007bff',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    fontSize: '14px',
    fontWeight: '500',
    cursor: 'pointer',
    transition: 'background-color 0.2s',
    textDecoration: 'none',
  },
};
