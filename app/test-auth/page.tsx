import { getServerAuth } from '@/lib/auth-server'
import { AuthTest } from '@/components/auth-test'

export default async function TestAuthPage() {
  const auth = await getServerAuth()

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">Authentication Test</h1>

      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">Server-side Auth Status:</h2>
        <pre className="text-sm">
          {JSON.stringify(auth, null, 2)}
        </pre>
      </div>

      <div className="mb-6">
        {auth.isAuthenticated ? (
          <div className="text-green-600">
            ✅ User is authenticated as {auth.user?.email}
          </div>
        ) : (
          <div className="text-red-600">
            ❌ User is not authenticated
          </div>
        )}
      </div>

      <AuthTest />
    </div>
  )
}
