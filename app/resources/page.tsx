"use client"

import { <PERSON>Header } from "@/components/page-header"
import { CtaSection } from "@/components/cta-section"
import { useState } from "react"
import { ChevronDown, Building2, Users, TrendingUp, Star, Handshake, Target, Zap, Coins, Calendar } from "lucide-react"
import { AnimatedSection } from "@/components/animated-section"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import Link from "next/link"

export default function ResourcesPage() {
  const [showSustainability, setShowSustainability] = useState(false)
  const [showAutomation, setShowAutomation] = useState(false)

  return (
    <>
      <PageHeader
        title="Resources"
        subtitle="BUSINESS NETWORK HUB"
        description="Join our growing network of businesses and access valuable insights, guides, and tools to transform your customer relationships."
      />

      {/* Business Network CTA Hero Section */}
      <section className="py-20 bg-gradient-to-br from-[#000814] via-[#001122] to-black relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-1/2 h-1/2 bg-[#3A56FF] opacity-20 blur-[100px] rounded-full" />
          <div className="absolute bottom-0 right-1/4 w-1/2 h-1/2 bg-[#FF914D] opacity-20 blur-[100px] rounded-full" />
        </div>

        {/* Hero Image Background */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-br from-[#000814]/90 via-[#001122]/85 to-black/90 z-10" />
          <img
            src="/images/cyberpunk-cityscape.jpeg"
            alt="Business Network"
            className="w-full h-full object-cover opacity-30"
          />
        </div>

        <div className="container mx-auto px-4 relative z-20">
          <AnimatedSection>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
              {/* Left side - Text content */}
              <div className="text-left lg:text-left">
                <Badge className="bg-gradient-to-r from-[#3A56FF] to-[#6366f1] text-white mb-6 text-lg px-6 py-3">
                  🚀 Join The Network
                </Badge>
                <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-[#FF914D] to-white bg-clip-text text-transparent">
                  Fuse Your Business
                  <br />
                  <span className="text-[#3A56FF]">With Fuse.Vip</span>
                </h2>
                <p className="text-xl text-white/80 max-w-2xl mb-8">
                  Join over 300+ VIPS already leveraging their relationships with our loyalty platform.
                  Increase retention, leverage your businesses hard work and build lasting connections with your customers.
                </p>

                {/* CTA Buttons - Moved here for better visual hierarchy */}
                <div className="flex flex-col sm:flex-row gap-6 mb-8">
                  <Link href="/register">
                    <Button
                      size="lg"
                      className="bg-gradient-to-r from-[#FF914D] to-[#FF6B35] hover:from-[#FF6B35] hover:to-[#FF914D] text-black font-bold text-lg px-10 py-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300 w-full sm:w-auto"
                    >
                      <Building2 className="mr-2 h-6 w-6" />
                      Register Your Business
                    </Button>
                  </Link>

                  <Link href="/book-call">
                    <Button
                      size="lg"
                      className="bg-gradient-to-r from-[#FF914D] to-[#FF6B35] hover:from-[#FF6B35] hover:to-[#FF914D] text-black font-bold text-lg px-10 py-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300 w-full sm:w-auto"
                    >
                      <Handshake className="mr-2 h-6 w-6" />
                      Schedule Demo
                    </Button>
                  </Link>
                </div>
              </div>

              {/* Right side - Featured image */}
              <div className="relative">
                <motion.div
                  className="relative rounded-2xl overflow-hidden shadow-2xl"
                  whileHover={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <img
                    src="/images/landing-page-background.png"
                    alt="Business Resources"
                    className="w-full h-[400px] object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                  <div className="absolute bottom-6 left-6 right-6">
                    <h3 className="text-2xl font-bold text-white mb-2">Transform Your Business</h3>
                    <p className="text-white/80">Access powerful tools and insights to grow your customer base</p>
                  </div>
                </motion.div>
              </div>
            </div>
          </AnimatedSection>

          {/* Stats Section */}
          <AnimatedSection delay={0.2}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <motion.div
                className="text-center p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#3A56FF] to-[#6366f1] rounded-full flex items-center justify-center mx-auto mb-4">
                  <Building2 className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-white mb-2">300+</h3>
                <p className="text-white/70">Active VIPS</p>
              </motion.div>

              <motion.div
                className="text-center p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#FF914D] to-[#FF6B35] rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-white mb-2">Support Social Medias</h3>
                <p className="text-white/70">Happy Fuse Partners follow the socials and spread the word</p>
              </motion.div>

              <motion.div
                className="text-center p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#9747FF] to-[#6366f1] rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-white mb-2">40%</h3>
                <p className="text-white/70">Avg. Retention Boost</p>
              </motion.div>
            </div>
          </AnimatedSection>

          {/* Benefits Grid */}
          <AnimatedSection delay={0.4}>
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold text-white mb-4">Why Choose Fuse.Vip?</h3>
              <p className="text-white/70 text-lg max-w-2xl mx-auto">
                Everything you need to build lasting customer relationships and grow your business
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              <motion.div
                className="p-8 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl"
                whileHover={{ y: -10, scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#3A56FF] to-[#6366f1] rounded-2xl flex items-center justify-center mb-6 shadow-lg">
                  <Target className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-white mb-4">Targeted Marketing</h4>
                <p className="text-white/80">Reach the right customers with precision-targeted campaigns and loyalty rewards that actually convert.</p>
              </motion.div>

              <motion.div
                className="p-8 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl"
                whileHover={{ y: -10, scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#FF914D] to-[#FF6B35] rounded-2xl flex items-center justify-center mb-6 shadow-lg">
                  <Zap className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-white mb-4">Instant Setup</h4>
                <p className="text-white/80">Get your loyalty program running in minutes, not months. No technical expertise required - we handle everything.</p>
              </motion.div>

              <motion.div
                className="p-8 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl"
                whileHover={{ y: -10, scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#9747FF] to-[#6366f1] rounded-2xl flex items-center justify-center mb-6 shadow-lg">
                  <Star className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-white mb-4">Premium Support</h4>
                <p className="text-white/80">24/7 dedicated support to help you maximize your business growth and customer satisfaction every step of the way.</p>
              </motion.div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Crypto Rewards & Frequency Section */}
      <section className="py-20 bg-gradient-to-br from-[#001122] via-[#000814] to-black relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 right-1/4 w-1/3 h-1/3 bg-[#FFD700] opacity-15 blur-[80px] rounded-full" />
          <div className="absolute bottom-1/4 left-1/4 w-1/3 h-1/3 bg-[#3A56FF] opacity-15 blur-[80px] rounded-full" />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <AnimatedSection>
            <div className="text-center mb-16">
              <Badge className="bg-gradient-to-r from-[#FFD700] to-[#FF914D] text-black mb-6 text-lg px-6 py-3">
                💰 Flexible Rewards
              </Badge>
              <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white via-[#FFD700] to-white bg-clip-text text-transparent">
                Get Rewarded Your Way
              </h2>
              <p className="text-xl text-white/80 max-w-3xl mx-auto">
                Choose your preferred cryptocurrency and reward frequency. We believe in giving you complete control over how and when you receive your business rewards.
              </p>
            </div>
          </AnimatedSection>

          <AnimatedSection delay={0.2}>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
              {/* Crypto Options Card */}
              <motion.div
                className="p-8 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl"
                whileHover={{ y: -10, scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="w-20 h-20 bg-gradient-to-r from-[#FFD700] to-[#FF914D] rounded-2xl flex items-center justify-center mb-6 shadow-lg">
                  <Coins className="h-10 w-10 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Any Cryptocurrency</h3>
                <p className="text-white/80 mb-6">
                  Don't limit yourself to just FUSE tokens. Choose to receive your business rewards in any cryptocurrency you prefer - Bitcoin, Ethereum, XRP, USDC, or any other digital asset.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center text-white/70">
                    <div className="w-2 h-2 bg-[#FFD700] rounded-full mr-3"></div>
                    <span>Bitcoin (BTC)</span>
                  </div>
                  <div className="flex items-center text-white/70">
                    <div className="w-2 h-2 bg-[#FFD700] rounded-full mr-3"></div>
                    <span>Ethereum (ETH)</span>
                  </div>
                  <div className="flex items-center text-white/70">
                    <div className="w-2 h-2 bg-[#FFD700] rounded-full mr-3"></div>
                    <span>XRP Ledger (XRP)</span>
                  </div>
                  <div className="flex items-center text-white/70">
                    <div className="w-2 h-2 bg-[#FFD700] rounded-full mr-3"></div>
                    <span>USD Coin (USDC)</span>
                  </div>
                  <div className="flex items-center text-white/70">
                    <div className="w-2 h-2 bg-[#FFD700] rounded-full mr-3"></div>
                    <span>FUSE Tokens</span>
                  </div>
                  <div className="flex items-center text-white/70">
                    <div className="w-2 h-2 bg-[#FFD700] rounded-full mr-3"></div>
                    <span>And many more...</span>
                  </div>
                </div>
              </motion.div>

              {/* Frequency Options Card */}
              <motion.div
                className="p-8 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl"
                whileHover={{ y: -10, scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="w-20 h-20 bg-gradient-to-r from-[#3A56FF] to-[#6366f1] rounded-2xl flex items-center justify-center mb-6 shadow-lg">
                  <Calendar className="h-10 w-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Flexible Timing</h3>
                <p className="text-white/80 mb-6">
                  Set your reward distribution schedule to match your business needs. Whether you prefer regular monthly payments or larger quarterly/annual distributions, the choice is yours.
                </p>
                <div className="space-y-4">
                  <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-semibold text-white">Monthly</span>
                      <span className="text-[#FFD700] text-sm">Most Popular</span>
                    </div>
                    <p className="text-white/70 text-sm">Steady monthly rewards for consistent cash flow</p>
                  </div>
                  <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-semibold text-white">Quarterly</span>
                      <span className="text-[#3A56FF] text-sm">Balanced</span>
                    </div>
                    <p className="text-white/70 text-sm">Larger rewards every 3 months for better planning</p>
                  </div>
                  <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-semibold text-white">Annually</span>
                      <span className="text-[#FF914D] text-sm">Maximum</span>
                    </div>
                    <p className="text-white/70 text-sm">Biggest annual payout for long-term growth</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </AnimatedSection>

          {/* Call to Action */}
          <AnimatedSection delay={0.4}>
            <div className="text-center mt-16">
              <p className="text-lg text-white/80 mb-8 max-w-2xl mx-auto">
                Ready to start earning rewards in your preferred cryptocurrency? Join our network and customize your reward preferences during registration.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/register">
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-[#FF914D] to-[#FF6B35] hover:from-[#FF6B35] hover:to-[#FF914D] text-black font-bold text-lg px-10 py-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300"
                  >
                    <Building2 className="mr-2 h-6 w-6" />
                    Start Earning Today
                  </Button>
                </Link>
                <Link href="/book-call">
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-[#FF914D] to-[#FF6B35] hover:from-[#FF6B35] hover:to-[#FF914D] text-black font-bold text-lg px-10 py-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300"
                  >
                    <Handshake className="mr-2 h-6 w-6" />
                    Learn More
                  </Button>
                </Link>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Cross-Border Payments Coming Soon Section */}
      <section className="py-16 bg-gradient-to-r from-[#000814] to-[#001122] relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-[#3A56FF]/10 via-transparent to-[#FF914D]/10" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-[#FFD700] opacity-5 blur-[120px] rounded-full" />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <AnimatedSection>
            <div className="max-w-4xl mx-auto text-center">
              <Badge className="bg-gradient-to-r from-[#3A56FF] to-[#6366f1] text-white mb-6 text-lg px-6 py-3">
                🚀 Coming Soon
              </Badge>

              <h2 className="text-3xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white via-[#3A56FF] to-white bg-clip-text text-transparent">
                Cross-Border Crypto Payments
              </h2>

              <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                We're partnering with leading payment processors to help your business accept cryptocurrency payments from customers worldwide, breaking down financial borders and expanding your market reach.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <motion.div
                  className="p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="w-16 h-16 bg-gradient-to-r from-[#FFD700] to-[#FF914D] rounded-full flex items-center justify-center mx-auto mb-4">
                    <Coins className="h-8 w-8 text-black" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">Accept Any Crypto</h3>
                  <p className="text-white/70 text-sm">Bitcoin, Ethereum, XRP, USDC, and 100+ cryptocurrencies accepted seamlessly</p>
                </motion.div>

                <motion.div
                  className="p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="w-16 h-16 bg-gradient-to-r from-[#3A56FF] to-[#6366f1] rounded-full flex items-center justify-center mx-auto mb-4">
                    <TrendingUp className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">Global Reach</h3>
                  <p className="text-white/70 text-sm">Serve customers from any country without traditional banking limitations</p>
                </motion.div>

                <motion.div
                  className="p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="w-16 h-16 bg-gradient-to-r from-[#9747FF] to-[#6366f1] rounded-full flex items-center justify-center mx-auto mb-4">
                    <Zap className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">Instant Settlement</h3>
                  <p className="text-white/70 text-sm">Receive payments instantly with lower fees than traditional processors</p>
                </motion.div>
              </div>

              <div className="bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm rounded-2xl border border-white/20 p-8">
                <h3 className="text-2xl font-bold text-white mb-4">Transform Your Business Payment System</h3>
                <p className="text-white/80 mb-6 max-w-2xl mx-auto">
                  Soon, your business will be able to leverage cryptocurrency for all transactions, opening doors to international customers and reducing payment processing costs by up to 70%.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/register">
                    <Button
                      size="lg"
                      className="bg-gradient-to-r from-[#FF914D] to-[#FF6B35] hover:from-[#FF6B35] hover:to-[#FF914D] text-black font-bold text-lg px-10 py-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300"
                    >
                      <Building2 className="mr-2 h-6 w-6" />
                      Join Early Access
                    </Button>
                  </Link>
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-2 border-white/30 text-white hover:bg-white/10 font-bold text-lg px-10 py-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300"
                    disabled
                  >
                    <Star className="mr-2 h-6 w-6" />
                    Coming Q2 2025
                  </Button>
                </div>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">

          {/* External link to Notion page */}
          <div className="max-w-3xl mx-auto space-y-4">
            <AnimatedSection>
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <a
                  href="https://warm-libra-173.notion.site/Fuse-Vip-1de5aebd6632807fab05d1b0378b7cc5"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-full flex justify-between items-center p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
                >
                  <span className="font-medium text-lg">View Privacy Policy and Terms of Service</span>
                </a>
              </div>
            </AnimatedSection>

            {/* Sustainability Section */}
            <AnimatedSection delay={0.2}>
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => setShowSustainability(!showSustainability)}
                  className="w-full flex justify-between items-center p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
                  aria-expanded={showSustainability}
                >
                  <span className="font-medium text-lg">Sustainability</span>
                  <ChevronDown
                    className={`h-5 w-5 text-gray-500 transition-transform duration-200 ${
                      showSustainability ? "transform rotate-180" : ""
                    }`}
                  />
                </button>

                {showSustainability && (
                  <div className="p-4 bg-white">
                    <p className="text-[#4a4a4a]">
                      <strong>Why Fuse Is A Sustainable Platform:</strong>
                      <br />
                      <br />
                      By Raul Salazar
                      <br />
                      <br />
                      Large Organizations (10,000+ Employees) Utilize An Average Of 410 SaaS Applications, Highlighting
                      The Integral Role of SaaS in Enterprise Operations. Fuse.Vip Will Be Creating An EcoSystem Where
                      Every Party Involved Will Be Relevant, Business Owners - Customers and The Fuse.Vip Team. Our Team
                      Is The Bridge For The Digital Age To Small Businesses!
                      <br />
                      <br />
                      Fuse.Vip Is Built On Timeless Sustainability Models That Ensure Long-Term Stability, Growth, And
                      User Empowerment.
                      <br />
                      <br />
                      Here's How:
                      <br />
                      <br />
                      <strong>1. Simple And Predictable Revenue Model</strong>
                      <br />
                      <br />
                      Fuse.vip offers free accounts to everyday users and free onboarding for businesses to local
                      entrepreneurs. Our Revenue Comes From Real Value Delivered — Not Ads, Data Sales, Or Speculative
                      Promises.
                      <br />
                      <br />• Subscription Fees From VIP Card Owners: Affordable One Time Purchase.
                      <br />• Optional Upgrades: Business Owners Are Incentivized To Join And Spread The VIP Cards To
                      Their Customers. Earning Them $FUSE Which Will Eventually Allow Them To Trade In Their Tokens To
                      Unlock SaaS
                      <br />• No Hidden Costs: Transparency builds user trust and predictable operational scaling. We
                      Want to Gamify The Entire EcoSystem. We Have Skills Ready To Help You.
                      <br />
                      <br />
                      <strong>2. Minimal Data Storage = Minimal Risk</strong>
                      <br />
                      <br />
                      Fuse.Vip Collects Only Essential User Information (Emails And Business Verification If
                      Applicable). We Respect User Privacy, Which Lowers Regulatory Risks And Data Breach
                      Vulnerabilities.
                      <br />• No Selling Of User Data
                      <br />• No Third-Party Tracking Or Behavioral Analytics
                      <br />• Focus On User Value, Not Surveillance Capitalism
                      <br />
                      <br />
                      <strong>3. Built On Lean, Scalable Infrastructure</strong>
                      <br />
                      <br />
                      Our Platform Is Hosted On Vercel, One Of The Most Reliable Cloud Services In The World.
                      <br />
                      Lean Codebases, Modern Frameworks, and Serverless Technology Allow Us To Scale Without Bloated
                      Costs.
                      <br />• Low Fixed Costs Even As User Numbers Grow
                      <br />• Auto-Scaling For Spikes In Referral Activity. Our Automation Skills Allow Us To Scale -
                      And Make $FUSE Token Have More Than Implied Value.
                      <br />• No Heavy Legacy Systems Holding Us Back
                      <br />
                      <br />
                      <strong>Fuse.Vip: Empowering America's Local Businesses For The Next 100 Years</strong>
                      <br />
                      <br />
                      We Are Building A Platform That Supports Entrepreneurship, Rewards Trust, Protects Privacy, And
                      Stays Financially Sustainable — Not Just For Today, But For Tomorrow.
                    </p>
                  </div>
                )}
              </div>
            </AnimatedSection>

            {/* Automation Section */}
            <AnimatedSection delay={0.3}>
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => setShowAutomation(!showAutomation)}
                  className="w-full flex justify-between items-center p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
                  aria-expanded={showAutomation}
                >
                  <span className="font-medium text-lg">Automation For Your Business</span>
                  <ChevronDown
                    className={`h-5 w-5 text-gray-500 transition-transform duration-200 ${
                      showAutomation ? "transform rotate-180" : ""
                    }`}
                  />
                </button>

                {showAutomation && (
                  <div className="p-4 bg-white">
                    <p className="text-[#4a4a4a]">
                      The future of commerce is automated & decentralized. Charlie, Dylan and Raul create tools that
                      empower local entrepreneurs, enable neighbor-to-neighbor rewards, and foster deep, lasting
                      connections between people and the places they love.
                      <br />
                      <br />
                      Join us in building a future where every business owner has the tools they need to succeed, and
                      every customer has the power to support the businesses they love. Email <NAME_EMAIL> for
                      help getting started. Or if you would like to get started automating your business now, dm us or
                      email us! 24/7 support is available.
                    </p>
                  </div>
                )}
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

    

      <CtaSection />
    </>
  )
}
