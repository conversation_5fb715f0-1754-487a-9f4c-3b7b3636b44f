"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { AnimatedSection } from "@/components/animated-section";
import { useAuth } from "@/contexts/auth-context";
import { OnboardingPanel } from "@/components/dashboard/onboarding-panel";
import { supabase } from "@/lib/supabase";
import { RedeemCodeModal } from "@/components/redeem-code-modal";
import { WalletConnectButton } from "@/components/wallet/wallet-connect-button";
import { UserQRCode } from "@/components/user-qr-code";
import { QRInteractions } from "@/components/qr-interactions";

import { Building2, CreditCard, Loader2, Gift, LogOut, QrCode, Users } from "lucide-react";
import { LiveGamingNotifications, useGamingNotifications } from "@/components/gaming/live-gaming-notifications"
import { useLivePrizeData } from "@/components/gaming/live-prize-tracker"
import { GamingProvider } from "@/contexts/gaming-context";
import { FuseGamingProvider } from "@/contexts/fuse-gaming-context";
import Image from "next/image";
import Link from "next/link";
import { ProtectedRoute } from "@/components/protected-route";
import { Button } from "@/components/ui/button";


export default function DashboardPage() {
  const { user, isLoading, refreshSession, signOut } = useAuth();
  const router = useRouter();
  const [isRecoveringSession, setIsRecoveringSession] = useState(false);
  const [isRedeemModalOpen, setIsRedeemModalOpen] = useState(false);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [profileLoading, setProfileLoading] = useState(true);
  const [userBusiness, setUserBusiness] = useState<any>(null);
  const [businessLoading, setBusinessLoading] = useState(true);

  // Show gaming notifications for dashboard users
  const showNotifications = useGamingNotifications(true);

  // Get live prize pool data
  const { totalPrizePool, totalPlayers, isLoading: prizeLoading } = useLivePrizeData();

  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Card tiers data (matching upgrade page)
  const cardTiers = [
    {
      name: "Premium Card",
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/premium-card-jvnxFn2cckvmHmXc3LEiYxIXgsVH9k.png",
      rewards: { referral: "20%", affiliate: "1%" },
      color: "from-blue-600 via-purple-600 to-indigo-700"
    },
    {
      name: "Gold Card",
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/gold-card-bflkHLVolhMSi3L5RexoL9fh7wsqBq.png",
      rewards: { referral: "25%", affiliate: "2%" },
      color: "from-yellow-600 via-yellow-500 to-orange-600"
    },
    {
      name: "Platinum Card",
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/platinum-card-IQekSEB3CMuXes5qUTxZc55ZeKUVOy.png",
      rewards: { referral: "30%", affiliate: "3%" },
      color: "from-gray-400 via-gray-300 to-gray-500"
    },
    {
      name: "Diamond Card",
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/diamond-card-I9uaKzc6KKTRSgl9mrz4SP2Y2myuiF.png",
      rewards: { referral: "35%", affiliate: "4%" },
      color: "from-cyan-400 via-blue-300 to-indigo-400"
    },
    {
      name: "Obsidian Card",
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/obsidian-card-sZV0uG2g9pJ0BiQRLvn14MJIFGvzDn.png",
      rewards: { referral: "40%", affiliate: "5%" },
      color: "from-black via-gray-800 to-gray-900"
    }
  ];

  // Helper function to get card tier data
  const getUserCardTier = () => {
    if (!userProfile?.card_tier) return null;
    return cardTiers.find(card =>
      card.name.toLowerCase().includes(userProfile.card_tier.toLowerCase())
    );
  };

  // Helper function to get business category colors and info
  const getBusinessCategoryInfo = (category: string) => {
    const categoryMap: { [key: string]: { color: string; icon: string; description: string } } = {
      'restaurant': {
        color: 'from-orange-600 via-red-600 to-pink-700',
        icon: '🍽️',
        description: 'Food & Dining'
      },
      'retail': {
        color: 'from-purple-600 via-blue-600 to-indigo-700',
        icon: '🛍️',
        description: 'Retail & Shopping'
      },
      'health': {
        color: 'from-green-600 via-emerald-600 to-teal-700',
        icon: '🏥',
        description: 'Health & Wellness'
      },
      'beauty': {
        color: 'from-pink-600 via-rose-600 to-red-700',
        icon: '💄',
        description: 'Beauty & Spa'
      },
      'automotive': {
        color: 'from-gray-600 via-slate-600 to-zinc-700',
        icon: '🚗',
        description: 'Automotive'
      },
      'entertainment': {
        color: 'from-yellow-600 via-orange-600 to-red-700',
        icon: '🎭',
        description: 'Entertainment'
      },
      'fitness': {
        color: 'from-blue-600 via-cyan-600 to-teal-700',
        icon: '💪',
        description: 'Fitness & Sports'
      },
      'technology': {
        color: 'from-indigo-600 via-purple-600 to-pink-700',
        icon: '💻',
        description: 'Technology'
      },
      'education': {
        color: 'from-emerald-600 via-green-600 to-lime-700',
        icon: '📚',
        description: 'Education'
      },
      'professional': {
        color: 'from-slate-600 via-gray-600 to-stone-700',
        icon: '💼',
        description: 'Professional Services'
      }
    };

    const categoryKey = category?.toLowerCase() || 'professional';
    return categoryMap[categoryKey] || categoryMap['professional'];
  };

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await signOut();
      // Clear any stored user data
      localStorage.removeItem('user_id');
      localStorage.removeItem('user_email');
      // Redirect to home page
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  useEffect(() => {
    // Check if we need to recover a session
    const recoverSession = async () => {
      const storedUserId = localStorage.getItem('user_id');
      const storedEmail = localStorage.getItem('user_email');

      if (!user && storedUserId && storedEmail) {
        setIsRecoveringSession(true);

        try {
          // First try to refresh the auth context
          await refreshSession();

          // Give a moment for the context to update
          setTimeout(async () => {
            // If we still don't have a user after context refresh, try Supabase
            if (!user && supabase) {
              const { data, error } = await supabase.auth.refreshSession();
              if (error || !data.session) {
                // If refresh fails, redirect to login with stored email
                console.log("Session refresh failed, redirecting to login");
                router.push(`/login?email=${encodeURIComponent(storedEmail)}`);
              } else {
                // Session refreshed successfully, refresh context again
                await refreshSession();
              }
            }
            setIsRecoveringSession(false);
          }, 1000);
        } catch (err) {
          console.error("Session recovery error:", err);
          setIsRecoveringSession(false);
          // Don't redirect immediately on error - give the user a chance to see the dashboard
          // The ProtectedRoute component will handle the redirect if needed
        }
      }
    };

    recoverSession();
  }, [user, router, refreshSession]);

  // Fetch user profile and business data
  useEffect(() => {
    const fetchUserData = async () => {
      if (user && supabase) {
        setProfileLoading(true);
        setBusinessLoading(true);

        try {
          // Fetch user profile
          const { data: profileData, error: profileError } = await supabase
            .from("profiles")
            .select("*")
            .eq("id", user.id)
            .single();

          if (profileError) {
            console.error("Error fetching user profile:", profileError);
          } else {
            setUserProfile(profileData);
          }

          // Fetch user business data
          const { data: businessData, error: businessError } = await supabase
            .from("businesses")
            .select("*")
            .eq("user_id", user.id)
            .single();

          if (businessError) {
            if (businessError.code !== "PGRST116") { // Not a "no rows found" error
              console.error("Error fetching user business:", businessError);
            }
            setUserBusiness(null);
          } else {
            setUserBusiness(businessData);
          }
        } catch (err) {
          console.error("Exception fetching user data:", err);
        } finally {
          setProfileLoading(false);
          setBusinessLoading(false);
        }
      } else {
        setProfileLoading(false);
        setBusinessLoading(false);
      }
    };

    fetchUserData();
  }, [user]);

  if (isLoading || isRecoveringSession) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[#3A56FF]" />
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <GamingProvider>
        <FuseGamingProvider>
        {/* Header with user controls */}
      <div className="relative bg-gradient-to-br from-[#000814] via-[#001122] to-black">
        <div className="container mx-auto px-4 pt-8 pb-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Image
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png"
              width={40}
              height={40}
              alt="Fuse.vip Logo"
              className="w-10 h-10"
            />
            <div>
              <h1 className="text-white text-xl font-bold">Welcome back!</h1>
              <p className="text-white/70 text-sm">Ready to grow your business or save money?</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <WalletConnectButton />
            <Button
              onClick={handleLogout}
              disabled={isLoggingOut}
              variant="outline"
              className="text-red-400 border-red-400 hover:bg-red-400/10 flex items-center"
            >
              {isLoggingOut ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Signing out...
                </>
              ) : (
                <>
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Hero Section */}
        <div className="container mx-auto px-4 py-16 text-center">
          <AnimatedSection>
            <div className="max-w-4xl mx-auto">
              <h2 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
                Choose Your <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">Fuse</span> Journey
              </h2>
              <p className="text-xl md:text-2xl text-white/80 mb-8 max-w-3xl mx-auto">
                Whether you're a business owner looking to attract more customers or a savvy shopper wanting exclusive discounts, we've got you covered.
              </p>

              {/* Quick Actions Pyramid */}
              <div className="mb-8">
                <AnimatedSection delay={0.2}>
                  <div className="flex flex-col items-center gap-3">
                    {/* Top row - 1 button */}
                    <div className="flex justify-center">
                      <Button
                        onClick={() => router.push('/scan')}
                        className="bg-blue-600/20 hover:bg-blue-600/30 border border-blue-400/30 text-blue-300 hover:text-blue-200 backdrop-blur-sm px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2"
                      >
                        <CreditCard className="h-4 w-4" />
                        Scan QR
                      </Button>
                    </div>

                    {/* Middle row - 2 buttons */}
                    <div className="flex gap-3">
                      <Button
                        onClick={() => setIsRedeemModalOpen(true)}
                        className="bg-green-600/20 hover:bg-green-600/30 border border-green-400/30 text-green-300 hover:text-green-200 backdrop-blur-sm px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2"
                      >
                        <Gift className="h-4 w-4" />
                        Redeem Code
                      </Button>
                      <Link href="/industry">
                        <Button className="bg-purple-600/20 hover:bg-purple-600/30 border border-purple-400/30 text-purple-300 hover:text-purple-200 backdrop-blur-sm px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2">
                          <Building2 className="h-4 w-4" />
                          Find Businesses
                        </Button>
                      </Link>
                    </div>

                    {/* Bottom row - 1 button (Gaming) */}
                    <div className="flex justify-center">
                      <Button className="bg-gradient-to-r from-yellow-600/20 to-orange-600/20 hover:from-yellow-600/30 hover:to-orange-600/30 border border-yellow-400/30 text-yellow-300 hover:text-yellow-200 backdrop-blur-sm px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 relative overflow-hidden">
                        <div className="absolute top-1 right-1 w-2 h-2 bg-yellow-400/40 rounded-full animate-ping"></div>
                        <span className="text-lg">🎮</span>
                        Gaming Hub
                        <span className="text-xs bg-yellow-400/20 px-1 rounded">
                          {prizeLoading ? '...' : `${totalPrizePool.toFixed(1)} XRP`}
                        </span>
                      </Button>
                    </div>
                  </div>
                </AnimatedSection>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <div className="flex items-center text-white/70">
                  <span className="text-green-400 mr-2">✓</span>
                  <span>Trusted by 70+ businesses</span>
                </div>
                <div className="flex items-center text-white/70">
                  <span className="text-green-400 mr-2">✓</span>
                  <span>Increase in customer savings</span>
                </div>
                <div className="flex items-center text-white/70">
                  <span className="text-green-400 mr-2">✓</span>
                  <span>50+ cities</span>
                </div>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </div>

      {/* Main CTA Section */}
      <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4">
          {/* Onboarding Panel - will only show if onboarding is not complete */}
          <OnboardingPanel />

          {/* Dual CTA Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-7xl mx-auto">

            {/* Business Owner CTA or Business Display */}
            <AnimatedSection delay={0}>
              {!businessLoading && userBusiness ? (
                // Show user's business in a striking way
                <div className="relative group">
                  {/* Dynamic background gradient based on business category */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${getBusinessCategoryInfo(userBusiness.category)?.color || 'from-blue-600 via-purple-600 to-indigo-700'} rounded-3xl opacity-90 group-hover:opacity-100 transition-opacity duration-300`}></div>

                  {/* Content */}
                  <div className="relative bg-black/10 backdrop-blur-sm rounded-3xl p-8 lg:p-12 border border-white/20 h-full">
                    <div className="text-center text-white space-y-6">
                      {/* Badge */}
                      <div className="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-full text-sm font-semibold">
                        ✅ Business Owner
                      </div>

                      {/* Title */}
                      <h3 className="text-4xl lg:text-5xl font-bold leading-tight">
                        <span className="text-yellow-400">{userBusiness.name || userBusiness.business_name}</span>
                      </h3>

                      {/* Business Logo or Category Icon */}
                      <div className="flex justify-center mb-6">
                        <div className="relative">
                          {userBusiness.logo_url ? (
                            <Image
                              src={userBusiness.logo_url}
                              width={200}
                              height={200}
                              alt={`${userBusiness.name || userBusiness.business_name} Logo`}
                              className="rounded-xl shadow-2xl transform hover:scale-105 transition-transform duration-300 bg-white/10 p-4"
                            />
                          ) : (
                            <div className="w-32 h-32 bg-white/20 rounded-xl flex items-center justify-center shadow-2xl transform hover:scale-105 transition-transform duration-300">
                              <span className="text-6xl">{getBusinessCategoryInfo(userBusiness.category)?.icon || '🏢'}</span>
                            </div>
                          )}
                          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl"></div>
                        </div>
                      </div>

                      {/* Business Info */}
                      <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 max-w-sm mx-auto border border-white/20">
                        <div className="space-y-3 text-center">
                          <div>
                            <div className="text-lg font-bold text-yellow-400">{getBusinessCategoryInfo(userBusiness.category)?.description || 'Business'}</div>
                            <div className="text-white/80 text-sm">Category</div>
                          </div>
                          {userBusiness.premium_discount && (
                            <div>
                              <div className="text-2xl font-bold text-green-400">{userBusiness.premium_discount}%</div>
                              <div className="text-white/80 text-sm">Premium Discount</div>
                            </div>
                          )}
                          <div>
                            <div className="text-lg font-bold text-blue-400">{userBusiness.status || 'Active'}</div>
                            <div className="text-white/80 text-sm">Status</div>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="space-y-4">
                        <Link href="/dashboard/business">
                          <Button className="bg-white/20 border-white/40 text-white hover:bg-white/30 font-semibold text-lg px-8 py-4 rounded-xl w-full border shadow-lg transform hover:scale-105 transition-all duration-200">
                            Manage Business Dashboard
                          </Button>
                        </Link>
                        <div className="flex flex-col sm:flex-row gap-2">
                          <Link href="/industry" className="flex-1">
                            <Button className="bg-yellow-500 hover:bg-yellow-400 text-black font-bold px-4 py-3 rounded-xl w-full">
                              View Directory
                            </Button>
                          </Link>
                          <Link href="/register-business" className="flex-1">
                            <Button className="bg-white/20 border-white/40 text-white hover:bg-white/30 font-semibold px-4 py-3 rounded-xl border">
                              Add 2nd Business
                            </Button>
                          </Link>
                        </div>
                      </div>

                      <p className="text-white/60 text-sm">
                        🏢 Business Owner • 📊 Analytics Available • 🎯 Growing Network
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                // Show Business Registration CTA for non-business owners
                <div className="relative group">
                  {/* Background gradient */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-3xl opacity-90 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Content */}
                  <div className="relative bg-black/10 backdrop-blur-sm rounded-3xl p-8 lg:p-12 border border-white/20 h-full">
                    <div className="text-center text-white space-y-6">
                      {/* Badge */}
                      <div className="inline-flex items-center px-4 py-2 bg-yellow-500 text-black rounded-full text-sm font-semibold">
                        🏢 For Business Owners
                      </div>

                      {/* Title */}
                      <h3 className="text-4xl lg:text-5xl font-bold leading-tight">
                        Grow Your <span className="text-yellow-400">Business</span>
                      </h3>

                      {/* Description */}
                      <p className="text-xl text-white/90 max-w-md mx-auto">
                        Join our network and attract more customers with exclusive loyalty rewards and referral programs.
                      </p>

                      {/* Benefits */}
                      <div className="space-y-4 text-left max-w-sm mx-auto">
                        <div className="flex items-center">
                          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                            <span className="text-white text-xs">✓</span>
                          </div>
                          <span className="text-white/90">Increase customer retention by 40%</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                            <span className="text-white text-xs">✓</span>
                          </div>
                          <span className="text-white/90">Get featured in our business directory</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                            <span className="text-white text-xs">✓</span>
                          </div>
                          <span className="text-white/90">Access to analytics and insights</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                            <span className="text-white text-xs">✓</span>
                          </div>
                          <span className="text-white/90">Free setup and onboarding</span>
                        </div>
                      </div>

                      {/* CTA Buttons */}
                      <div className="space-y-4">
                        <Link href="/register-business">
                          <Button className="bg-yellow-500 hover:bg-yellow-400 text-black font-bold text-lg px-8 py-4 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200 w-full">
                            🚀 Register Your Business
                          </Button>
                        </Link>
                        <Link href="/dashboard/business">
                          <Button className="bg-white/20 border-white/40 text-white hover:bg-white/30 font-semibold px-6 py-3 rounded-xl w-full border">
                            View Business Dashboard
                          </Button>
                        </Link>
                      </div>

                      <p className="text-white/60 text-sm">
                        ⚡ Quick approval • 📊 Real-time analytics • 🎯 Targeted marketing
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </AnimatedSection>

            {/* VIP Card CTA or Card Display */}
            <AnimatedSection delay={0.2}>
              {!profileLoading && userProfile?.is_card_holder && getUserCardTier() ? (
                // Show user's card tier in a striking way
                <div className="relative group">
                  {/* Dynamic background gradient based on card tier */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${getUserCardTier()?.color || 'from-emerald-600 via-teal-600 to-cyan-700'} rounded-3xl opacity-90 group-hover:opacity-100 transition-opacity duration-300`}></div>

                  {/* Content */}
                  <div className="relative bg-black/10 backdrop-blur-sm rounded-3xl p-8 lg:p-12 border border-white/20 h-full">
                    <div className="text-center text-white space-y-6">
                      {/* Badge */}
                      <div className="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-full text-sm font-semibold">
                        ✅ VIP Member
                      </div>

                      {/* Title */}
                      <h3 className="text-4xl lg:text-5xl font-bold leading-tight">
                        Your <span className="text-yellow-400">{getUserCardTier()?.name}</span>
                      </h3>

                      {/* Card Image */}
                      <div className="flex justify-center mb-6">
                        <div className="relative">
                          <Image
                            src={getUserCardTier()?.cardImage || "/placeholder.svg"}
                            width={300}
                            height={200}
                            alt={`${getUserCardTier()?.name} VIP Card`}
                            className="rounded-xl shadow-2xl transform hover:scale-105 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl"></div>
                        </div>
                      </div>

                      {/* Rewards Info */}
                      <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 max-w-sm mx-auto border border-white/20">
                        <div className="grid grid-cols-2 gap-4 text-center">
                          <div>
                            <div className="text-2xl font-bold text-yellow-400">{getUserCardTier()?.rewards.referral}</div>
                            <div className="text-white/80 text-sm">Referral Rewards</div>
                          </div>
                          <div>
                            <div className="text-2xl font-bold text-green-400">{getUserCardTier()?.rewards.affiliate}</div>
                            <div className="text-white/80 text-sm">Affiliate Rewards</div>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="space-y-4">
                        <Link href="/dashboard/cards">
                          <Button className="bg-white/20 border-white/40 text-white hover:bg-white/30 font-semibold text-lg px-8 py-4 rounded-xl w-full border shadow-lg transform hover:scale-105 transition-all duration-200">
                            View Card Dashboard
                          </Button>
                        </Link>
                        <div className="flex flex-col sm:flex-row gap-2">
                          <Link href="/industry" className="flex-1">
                            <Button className="bg-yellow-500 hover:bg-yellow-400 text-black font-bold px-4 py-3 rounded-xl w-full">
                              Find Discounts
                            </Button>
                          </Link>
                          <Button
                            onClick={() => setIsRedeemModalOpen(true)}
                            className="bg-white/20 border-white/40 text-white hover:bg-white/30 font-semibold px-4 py-3 rounded-xl flex-1 border"
                          >
                            Redeem Code
                          </Button>
                        </div>
                      </div>

                      <p className="text-white/60 text-sm">
                        🎉 VIP Member • 💰 Earning rewards • 🔥 Exclusive access
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                // Show VIP Card CTA for non-card holders
                <div className="relative group">
                  {/* Background gradient */}
                  <div className="absolute inset-0 bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-700 rounded-3xl opacity-90 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Content */}
                  <div className="relative bg-black/10 backdrop-blur-sm rounded-3xl p-8 lg:p-12 border border-white/20 h-full">
                    <div className="text-center text-white space-y-6">
                      {/* Badge */}
                      <div className="inline-flex items-center px-4 py-2 bg-orange-500 text-white rounded-full text-sm font-semibold">
                        💎 For Smart Shoppers
                      </div>

                      {/* Title */}
                      <h3 className="text-4xl lg:text-5xl font-bold leading-tight">
                        Get Your <span className="text-orange-400">VIP Card</span>
                      </h3>

                      {/* Description */}
                      <p className="text-xl text-white/90 max-w-md mx-auto">
                        Unlock exclusive discounts and rewards at hundreds of businesses nationwide for just $100/year.
                      </p>

                      {/* Pricing */}
                      <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 max-w-sm mx-auto border border-white/20">
                        <div className="text-center">
                          <div className="text-white/70 text-sm line-through">$200/year</div>
                          <div className="text-4xl font-bold text-orange-400">$100</div>
                          <div className="text-white/80 text-sm">Limited time • Save $100</div>
                        </div>
                      </div>

                      {/* Benefits */}
                      <div className="space-y-4 text-left max-w-sm mx-auto">
                        <div className="flex items-center">
                          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                            <span className="text-white text-xs">✓</span>
                          </div>
                          <span className="text-white/90">Save 10-50% at partner businesses</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                            <span className="text-white text-xs">✓</span>
                          </div>
                          <span className="text-white/90">Earn cashback and rewards</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                            <span className="text-white text-xs">✓</span>
                          </div>
                          <span className="text-white/90">Access to exclusive events</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                            <span className="text-white text-xs">✓</span>
                          </div>
                          <span className="text-white/90">Priority customer support</span>
                        </div>
                      </div>

                      {/* CTA Buttons */}
                      <div className="space-y-4">
                        <Link href="/upgrade">
                          <Button className="bg-orange-500 hover:bg-orange-400 text-white font-bold text-lg px-8 py-4 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200 w-full">
                            💳 Get VIP Card - $100
                          </Button>
                        </Link>
                        <div className="flex flex-col sm:flex-row gap-2">
                          <Link href="/dashboard/cards" className="flex-1">
                            <Button className="bg-white/20 border-white/40 text-white hover:bg-white/30 font-semibold px-4 py-3 rounded-xl w-full border">
                              View Card Dashboard
                            </Button>
                          </Link>
                          <Button
                            onClick={() => setIsRedeemModalOpen(true)}
                            className="bg-white/20 border-white/40 text-white hover:bg-white/30 font-semibold px-4 py-3 rounded-xl flex-1 border"
                          >
                            Redeem Code
                          </Button>
                        </div>
                      </div>

                      <p className="text-white/60 text-sm">
                        💳 Instant activation • 🔒 30-day guarantee • 📱 Mobile app access
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Compact Gaming Section */}
      <section className="py-12 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
        <div className="container mx-auto px-4">
          <AnimatedSection>
            <div className="max-w-4xl mx-auto">
              {/* Header */}
              <div className="text-center mb-8">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8 }}
                  className="inline-flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-4 py-2 rounded-full font-bold text-sm mb-4"
                >
                  🎮 Competitive Gaming
                  <motion.span
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    🏆
                  </motion.span>
                </motion.div>
                <h2 className="text-2xl md:text-3xl font-bold mb-2">
                  Play Games, Win <span className="text-yellow-400">XRP Prizes</span>
                </h2>
                <p className="text-white/80 text-sm">Free to play, pay to compete!</p>
              </div>

              {/* Compact Stats & Games Grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                {/* Prize Pool */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 text-center"
                >
                  <div className="text-lg font-bold text-yellow-400">
                    {prizeLoading ? '...' : totalPrizePool.toFixed(1)} XRP
                  </div>
                  <div className="text-white/80 text-xs">Prize Pool</div>
                </motion.div>

                {/* Active Players */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 text-center"
                >
                  <div className="text-lg font-bold text-blue-400">
                    {prizeLoading ? '...' : totalPlayers}
                  </div>
                  <div className="text-white/80 text-xs">Players</div>
                </motion.div>

                {/* Games Available */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 text-center"
                >
                  <div className="text-lg font-bold text-green-400">3</div>
                  <div className="text-white/80 text-xs">Games</div>
                </motion.div>

                {/* Free to Play */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 text-center"
                >
                  <div className="text-lg font-bold text-pink-400">Free</div>
                  <div className="text-white/80 text-xs">To Play</div>
                </motion.div>
              </div>

              {/* Compact Game Selection */}
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/20 mb-6">
                <h3 className="text-lg font-bold mb-4 text-center">Choose Your Game</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[
                    { name: "2048", icon: "🎯", difficulty: "Medium" },
                    { name: "Fuse Bird", icon: "🐦", difficulty: "Hard" },
                    { name: "Rock Paper Scissors", icon: "✂️", difficulty: "Easy" }
                  ].map((game) => (
                    <motion.div
                      key={game.name}
                      whileHover={{ scale: 1.02 }}
                      className="bg-white/10 rounded-lg p-4 border border-white/20 cursor-pointer group text-center"
                    >
                      <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">
                        {game.icon}
                      </div>
                      <h4 className="font-bold text-sm mb-1">{game.name}</h4>
                      <div className={`text-xs px-2 py-1 rounded-full inline-block ${
                        game.difficulty === 'Easy' ? 'bg-green-500/20 text-green-400' :
                        game.difficulty === 'Medium' ? 'bg-yellow-500/20 text-yellow-400' :
                        'bg-red-500/20 text-red-400'
                      }`}>
                        {game.difficulty}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Compact CTA */}
              <div className="text-center">
                <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
                  <Button
                    size="sm"
                    className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-400 hover:to-orange-400 text-black font-bold px-6 py-3 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200"
                  >
                    🎮 Start Playing
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="border border-white/40 bg-white/10 text-white hover:bg-white/20 font-semibold px-6 py-3 rounded-xl transition-all duration-200"
                  >
                    View Leaderboards
                  </Button>
                </div>
                <p className="text-white/60 text-xs mt-3">
                  🎯 Free to play • 💰 Real XRP prizes • 🔒 Secure with Xaman wallet
                </p>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>



      {/* QR Code Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <AnimatedSection>
            <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Your QR Code & Connections
            </h3>
          </AnimatedSection>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
            {/* User QR Code */}
            <AnimatedSection delay={0}>
              <div className="flex justify-center">
                <UserQRCode size={280} />
              </div>
            </AnimatedSection>

            {/* QR Interactions */}
            <AnimatedSection delay={0.2}>
              <QRInteractions limit={5} />
            </AnimatedSection>
          </div>

          {/* QR Actions */}
          <AnimatedSection delay={0.4}>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-12">
              <Button
                onClick={() => router.push('/scan')}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold text-lg px-8 py-4 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center gap-2"
              >
                <QrCode className="h-5 w-5" />
                Scan QR Code
              </Button>
              <Button
                onClick={() => router.push('/qr-connections')}
                variant="outline"
                className="border-gray-300 text-gray-700 hover:bg-gray-50 font-semibold text-lg px-8 py-4 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center gap-2"
              >
                <Users className="h-5 w-5" />
                View All Connections
              </Button>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Enhanced CTA Section */}
      <section className="bg-gradient-to-r from-blue-900 via-purple-900 to-indigo-900 py-16">
        <div className="container mx-auto px-4 text-center">
          <AnimatedSection>
            <h2 className="text-white text-4xl md:text-5xl font-bold mb-6">
              Ready to unlock the power of
              <span className="inline-flex items-center ml-3">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png"
                  width={32}
                  height={32}
                  alt="Fuse.vip Logo"
                  className="w-8 h-8 mr-2"
                />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">Fuse.Vip</span>
              </span>
              ?
            </h2>
          </AnimatedSection>

          <AnimatedSection delay={0.2}>
            <p className="text-white/80 mb-8 max-w-2xl mx-auto text-xl">
              Join thousands of businesses and customers who are already saving money and growing their revenue with our revolutionary loyalty platform.
            </p>
          </AnimatedSection>

          <AnimatedSection delay={0.4}>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <Link href="/register-business">
                <Button className="bg-yellow-500 hover:bg-yellow-400 text-black font-bold text-lg px-8 py-4 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200">
                  🏢 Register Your Business
                </Button>
              </Link>
              <Link href="/upgrade">
                <Button className="bg-orange-500 hover:bg-orange-400 text-white font-bold text-lg px-8 py-4 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200">
                  💳 Get VIP Card - $100
                </Button>
              </Link>
            </div>
          </AnimatedSection>

          <AnimatedSection delay={0.6}>
            <p className="text-white/60 text-sm">
              🚀 Quick setup • 💰 Instant savings • 📈 Proven results • 🔒 Secure platform
            </p>
          </AnimatedSection>
        </div>
      </section>

      {/* Add the modal at the end */}
      <RedeemCodeModal
        isOpen={isRedeemModalOpen}
        onClose={() => setIsRedeemModalOpen(false)}
      />

        {/* Live Gaming Notifications */}
        {showNotifications && <LiveGamingNotifications />}
        </FuseGamingProvider>
      </GamingProvider>
    </ProtectedRoute>
  );
}
