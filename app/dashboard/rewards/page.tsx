"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { supabase } from "@/lib/supabase";
import { ProtectedRoute } from "@/components/protected-route";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Coins, Calendar, MapPin, TrendingUp } from "lucide-react";

interface BusinessInteraction {
  id: string;
  scanned_business_id: string;
  created_at: string;
  interaction_metadata?: {
    business_name?: string;
    fuse_tokens_awarded?: number;
  };
  business?: {
    name: string;
    category: string;
    logo_url?: string;
  };
}

export default function RewardsPage() {
  const { user, profile } = useAuth();
  const router = useRouter();
  const [visits, setVisits] = useState<BusinessInteraction[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalFuseEarned, setTotalFuseEarned] = useState(0);

  useEffect(() => {
    if (!user) return;

    const fetchVisits = async () => {
      try {
        const { data, error } = await supabase
          .from('qr_interactions')
          .select(`
            id,
            scanned_business_id,
            created_at,
            interaction_metadata,
            businesses:scanned_business_id (
              name,
              category,
              logo_url
            )
          `)
          .eq('scanner_user_id', user.id)
          .eq('interaction_type', 'business_scan')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching interactions:', error);
        } else {
          setVisits(data || []);
          // Calculate total FUSE earned from interaction metadata
          const totalEarned = data?.reduce((total, interaction) => {
            const tokensAwarded = interaction.interaction_metadata?.fuse_tokens_awarded || 100;
            return total + tokensAwarded;
          }, 0) || 0;
          setTotalFuseEarned(totalEarned);
        }
      } catch (error) {
        console.error('Error:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchVisits();
  }, [user]);

  const handleGoBack = () => {
    router.push('/dashboard');
  };

  const handleScanMore = () => {
    router.push('/scan');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getVisitsThisWeek = () => {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    
    return visits.filter(visit =>
      new Date(visit.created_at) >= oneWeekAgo
    ).length;
  };

  const getVisitsThisMonth = () => {
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    
    return visits.filter(visit =>
      new Date(visit.created_at) >= oneMonthAgo
    ).length;
  };

  if (!profile?.is_card_holder) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center mb-8">
              <Button
                onClick={handleGoBack}
                variant="ghost"
                className="text-white hover:bg-white/10 mr-4"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Dashboard
              </Button>
            </div>

            <div className="max-w-2xl mx-auto text-center">
              <div className="bg-white rounded-xl p-8 shadow-xl">
                <Coins className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-gray-900 mb-4">
                  VIP Membership Required
                </h1>
                <p className="text-gray-600 mb-6">
                  Only VIP cardholders can earn FUSE rewards by scanning business QR codes. 
                  Upgrade to VIP to start earning 100 FUSE tokens per business visit!
                </p>
                <Button 
                  onClick={() => router.push('/membership')}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3"
                >
                  Upgrade to VIP
                </Button>
              </div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <Button
              onClick={handleGoBack}
              variant="ghost"
              className="text-white hover:bg-white/10 mr-4"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Dashboard
            </Button>
          </div>

          <div className="max-w-4xl mx-auto">
            {/* Page Title */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-white mb-2">
                Your FUSE Rewards
              </h1>
              <p className="text-blue-200">
                Track your earnings from business visits
              </p>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white/10 rounded-xl p-6 text-center">
                <Coins className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
                <div className="text-3xl font-bold text-white">{totalFuseEarned}</div>
                <div className="text-blue-200 text-sm">Total FUSE Earned</div>
              </div>
              
              <div className="bg-white/10 rounded-xl p-6 text-center">
                <MapPin className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <div className="text-3xl font-bold text-white">{visits.length}</div>
                <div className="text-blue-200 text-sm">Total Visits</div>
              </div>
              
              <div className="bg-white/10 rounded-xl p-6 text-center">
                <Calendar className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                <div className="text-3xl font-bold text-white">{getVisitsThisWeek()}</div>
                <div className="text-blue-200 text-sm">This Week</div>
              </div>
              
              <div className="bg-white/10 rounded-xl p-6 text-center">
                <TrendingUp className="h-8 w-8 text-purple-400 mx-auto mb-2" />
                <div className="text-3xl font-bold text-white">{getVisitsThisMonth()}</div>
                <div className="text-blue-200 text-sm">This Month</div>
              </div>
            </div>

            {/* Visit History */}
            <div className="bg-white rounded-xl shadow-xl overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900">Visit History</h2>
                  <Button 
                    onClick={handleScanMore}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    Scan More QR Codes
                  </Button>
                </div>
              </div>

              <div className="max-h-96 overflow-y-auto">
                {loading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
                  </div>
                ) : visits.length > 0 ? (
                  <div className="divide-y divide-gray-200">
                    {visits.map((visit) => (
                      <div key={visit.id} className="p-6 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            {visit.business?.logo_url ? (
                              <img 
                                src={visit.business.logo_url} 
                                alt={visit.business.name}
                                className="w-12 h-12 rounded-lg object-cover"
                              />
                            ) : (
                              <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                <MapPin className="h-6 w-6 text-gray-400" />
                              </div>
                            )}
                            <div>
                              <h3 className="font-semibold text-gray-900">
                                {visit.business?.name || 'Unknown Business'}
                              </h3>
                              <p className="text-gray-600 text-sm">
                                {visit.business?.category || 'Business'}
                              </p>
                              <p className="text-gray-500 text-xs">
                                {formatDate(visit.created_at)}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center text-green-600 font-bold">
                              <Coins className="h-4 w-4 mr-1" />
                              +{visit.interaction_metadata?.fuse_tokens_awarded || 100} FUSE
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Coins className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      No visits yet
                    </h3>
                    <p className="text-gray-600 mb-6">
                      Start scanning business QR codes to earn FUSE rewards!
                    </p>
                    <Button 
                      onClick={handleScanMore}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      Scan Your First QR Code
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Info Card */}
            <div className="mt-8 bg-white/5 rounded-xl p-6">
              <h3 className="text-white font-semibold mb-2">How FUSE Rewards Work</h3>
              <ul className="text-gray-300 text-sm space-y-1">
                <li>• Earn 100 FUSE tokens for each business visit</li>
                <li>• Each business can be scanned once per day</li>
                <li>• Only VIP cardholders are eligible for rewards</li>
                <li>• Future: Automatic FUSE airdrops to your XRPL wallet</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
