"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { AnimatedSection } from "@/components/animated-section";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useAuth } from "@/contexts/auth-context";
import { RedeemCodeModal } from "@/components/redeem-code-modal";
import { PhysicalCardRedeemModal } from "@/components/physical-card-redeem-modal";
import { BusinessCombobox } from "@/components/business/business-combobox";

interface Business {
  name: string;
  premium_discount: string;
  logo_url: string;
  website: string;
}

interface ReferringBusiness {
  id: string;
  name: string;
}

export default function CardDashboard() {
  const router = useRouter();
  const { user, profile, isLoading: profileLoading } = useAuth();
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentBusiness, setCurrentBusiness] = useState<Business | null>(null);
  const [isRedeemModalOpen, setIsRedeemModalOpen] = useState(false);
  const [isPhysicalCardModalOpen, setIsPhysicalCardModalOpen] = useState(false);
  const [referringBusiness, setReferringBusiness] = useState<ReferringBusiness | null>(null);
  const [isEditingReferringBusiness, setIsEditingReferringBusiness] = useState(false);
  const [selectedReferringBusinessId, setSelectedReferringBusinessId] = useState("");
  const [timeRemaining, setTimeRemaining] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  } | null>(null);

  useEffect(() => {
    fetchBusinesses();
  }, []);

  useEffect(() => {
    if (profile) {
      fetchReferringBusiness();
      calculateTimeRemaining();
    }
  }, [profile]);

  // Timer effect for live countdown
  useEffect(() => {
    if (isPremiumOrHigher() && profile?.membership_end_date) {
      const timer = setInterval(() => {
        calculateTimeRemaining();
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [profile?.membership_end_date, profile?.card_tier]);

  useEffect(() => {
    if (businesses.length > 0) {
      setCurrentBusiness(businesses[currentIndex]);
    }
  }, [businesses, currentIndex]);

  // Helper function to check if user has premium or higher tier
  const isPremiumOrHigher = () => {
    const tier = profile?.card_tier?.toLowerCase();
    return tier && ['premium', 'gold', 'platinum', 'diamond', 'obsidian'].includes(tier);
  };

  // Calculate time remaining until membership expires
  const calculateTimeRemaining = () => {
    if (!profile?.membership_end_date) {
      setTimeRemaining(null);
      return;
    }

    const now = new Date().getTime();
    const endDate = new Date(profile.membership_end_date).getTime();
    const difference = endDate - now;

    if (difference > 0) {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      setTimeRemaining({ days, hours, minutes, seconds });
    } else {
      setTimeRemaining(null);
    }
  };

  const fetchBusinesses = async () => {
    if (!supabase) return;

    const { data, error } = await supabase
      .from("businesses")
      .select("name, premium_discount, logo_url, website")
      .not("premium_discount", "is", null)
      .order("name");

    if (error) {
      console.error("Error fetching businesses:", error);
    } else {
      const processedData: Business[] =
        data?.map((business: any) => ({
          name: business.name,
          premium_discount: business.premium_discount,
          logo_url: business.logo_url || "/placeholder-logo.png",
          website: business.website,
        })) || [];
      setBusinesses(processedData);
      setCurrentIndex(Math.floor(Math.random() * processedData.length));
    }
  };

  const fetchReferringBusiness = async () => {
    if (!supabase || !profile?.referring_business_id) return;

    const { data, error } = await supabase
      .from("businesses")
      .select("id, name")
      .eq("id", profile.referring_business_id)
      .single();

    if (error) {
      console.error("Error fetching referring business:", error);
    } else if (data) {
      setReferringBusiness({
        id: data.id as string,
        name: data.name as string,
      });
    }
  };

  const updateReferringBusiness = async () => {
    if (!supabase || !user || !selectedReferringBusinessId) return;

    const { error } = await supabase
      .from("profiles")
      .update({ referring_business_id: selectedReferringBusinessId })
      .eq("id", user.id);

    if (error) {
      console.error("Error updating referring business:", error);
      alert("Error updating referring business. Please try again.");
    } else {
      // Refresh the referring business data
      fetchReferringBusiness();
      setIsEditingReferringBusiness(false);
      setSelectedReferringBusinessId("");
      alert("Referring business updated successfully!");
    }
  };

  if (profileLoading || !profile) {
    return <div className="text-center py-16 text-white">Loading your profile...</div>;
  }

  return (
    <div className="relative w-full min-h-screen bg-gradient-to-b from-[#000814] via-[#000d1a] to-black overflow-hidden text-white px-4 sm:px-8 py-20">
      {/* Background Image Overlay */}
      <div className="absolute inset-0 z-0 after:content-[''] after:absolute after:inset-0 after:bg-radial-gradient after:from-transparent after:to-black/70">
        <Image
          src="/images/landing-bckgrnd-2.png"
          alt="Fuse.vip Background"
          width={1600}
          height={900}
          className="object-cover w-full h-full opacity-30"
          priority
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-6xl mx-auto space-y-16">

        {/* Custom Header */}
        <div className="text-center max-w-3xl mx-auto space-y-4 pt-4 pb-10">
          <p className="text-sm font-semibold text-blue-500 uppercase tracking-wider">
            Welcome to Your Fuse Portal
          </p>
          <h1 className="text-5xl font-bold text-white">
            {profile.is_card_holder
              ? (profile.card_tier ? `${profile.card_tier.charAt(0).toUpperCase() + profile.card_tier.slice(1).toLowerCase()} Card` : "Your VIP Card")
              : "Get Your VIP Card"
            }
          </h1>

          {profile.is_card_holder ? (
            // Timer for card holders
            isPremiumOrHigher() && timeRemaining && (
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-4 mx-auto max-w-md">
                <p className="text-sm font-medium text-white mb-2">Membership Expires In:</p>
                <div className="grid grid-cols-4 gap-2 text-center">
                  <div className="bg-white/20 rounded-md p-2">
                    <div className="text-2xl font-bold text-white">{timeRemaining.days}</div>
                    <div className="text-xs text-white/80">Days</div>
                  </div>
                  <div className="bg-white/20 rounded-md p-2">
                    <div className="text-2xl font-bold text-white">{timeRemaining.hours}</div>
                    <div className="text-xs text-white/80">Hours</div>
                  </div>
                  <div className="bg-white/20 rounded-md p-2">
                    <div className="text-2xl font-bold text-white">{timeRemaining.minutes}</div>
                    <div className="text-xs text-white/80">Minutes</div>
                  </div>
                  <div className="bg-white/20 rounded-md p-2">
                    <div className="text-2xl font-bold text-white">{timeRemaining.seconds}</div>
                    <div className="text-xs text-white/80">Seconds</div>
                  </div>
                </div>
              </div>
            )
          ) : (
            // CTA for non-card holders
            <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg p-4 mx-auto max-w-md">
              <div className="flex items-center justify-center gap-2 text-yellow-400 mb-2">
                <span className="text-lg">⚡</span>
                <span className="font-semibold">Limited Time: 50% Off</span>
              </div>
              <div className="text-center">
                <div className="text-white/70 text-sm line-through">$200</div>
                <div className="text-3xl font-bold text-yellow-400">$100</div>
                <div className="text-white/80 text-sm">Save $100 today!</div>
              </div>
            </div>
          )}

          <p className="text-lg text-white/80">
            {profile.is_card_holder
              ? "Manage your card, view discounts and soon, log the businesses you've visited to earn rewards!"
              : "Join thousands of members saving money at local businesses with exclusive VIP discounts and rewards!"
            }
          </p>
        </div>

        {/* Membership Card */}
        <AnimatedSection>
          <div className="relative bg-[#1c1f2a]/80 backdrop-blur-sm border border-white/10 rounded-2xl px-8 py-10 shadow-2xl w-full max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row items-center justify-center gap-8">
              {/* VIP Card Image */}
              <div className="flex-shrink-0">
                <div className="relative">
                  <Image
                    src="/images/premium-card.png"
                    alt="Premium Fuse Card"
                    width={280}
                    height={180}
                    className={`rounded-md shadow-lg transition-all duration-300 ${
                      !profile.is_card_holder ? "opacity-50 grayscale" : ""
                    }`}
                  />
                  {!profile.is_card_holder && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/60 rounded-md">
                      <div className="text-center">
                        <div className="text-2xl mb-2">🔒</div>
                        <div className="text-white font-semibold text-sm">Get Your VIP Card</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Profile Info */}
              <div className="text-center md:text-left space-y-3">
                <h2 className="text-2xl font-semibold text-white">
                  Welcome, {profile.first_name} {profile.last_name}!
                </h2>

                {profile.is_card_holder ? (
                  // Card holder information
                  <>
                    <p className="text-sm text-white/70">
                      <span className="font-medium text-white">Member Since:</span>{" "}
                      {new Date(profile.created_at).toLocaleDateString()}
                    </p>
                    <div className="text-sm text-white/70">
                      <span className="font-medium text-white">Referring Business:</span>{" "}
                      {!isEditingReferringBusiness ? (
                        <div className="flex items-center gap-2 mt-1">
                          <span>{referringBusiness ? referringBusiness.name : "None selected"}</span>
                          <Button
                            onClick={() => {
                              setIsEditingReferringBusiness(true);
                              setSelectedReferringBusinessId(referringBusiness?.id || "");
                            }}
                            className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1 rounded-md"
                          >
                            {referringBusiness ? "Change" : "Select"}
                          </Button>
                        </div>
                      ) : (
                        <div className="mt-2 space-y-2">
                          <BusinessCombobox
                            value={selectedReferringBusinessId}
                            onChange={setSelectedReferringBusinessId}
                          />
                          <div className="flex gap-2">
                            <Button
                              onClick={updateReferringBusiness}
                              className="bg-green-600 hover:bg-green-700 text-white text-xs px-2 py-1 rounded-md"
                            >
                              Save
                            </Button>
                            <Button
                              onClick={() => {
                                setIsEditingReferringBusiness(false);
                                setSelectedReferringBusinessId("");
                              }}
                              className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1 rounded-md"
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                    <p className="text-sm text-white/70 break-all">
                      <span className="font-medium text-white">User ID:</span> {profile.id}
                    </p>
                  </>
                ) : (
                  // Non-card holder information
                  <>
                    <p className="text-sm text-white/70">
                      <span className="font-medium text-white">Account Created:</span>{" "}
                      {new Date(profile.created_at).toLocaleDateString()}
                    </p>
                    <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4 my-4">
                      <div className="flex items-center gap-2 text-yellow-400 mb-2">
                        <span className="text-lg">⚠️</span>
                        <span className="font-semibold">VIP Card Required</span>
                      </div>
                      <p className="text-white/80 text-sm">
                        You need a VIP card to access premium discounts and rewards. Get yours today and start saving immediately!
                      </p>
                    </div>
                    <div className="text-sm text-white/70">
                      <span className="font-medium text-white">Referring Business:</span>{" "}
                      {!isEditingReferringBusiness ? (
                        <div className="flex items-center gap-2 mt-1">
                          <span>{referringBusiness ? referringBusiness.name : "None selected"}</span>
                          <Button
                            onClick={() => {
                              setIsEditingReferringBusiness(true);
                              setSelectedReferringBusinessId(referringBusiness?.id || "");
                            }}
                            className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1 rounded-md"
                          >
                            {referringBusiness ? "Change" : "Select"}
                          </Button>
                        </div>
                      ) : (
                        <div className="mt-2 space-y-2">
                          <BusinessCombobox
                            value={selectedReferringBusinessId}
                            onChange={setSelectedReferringBusinessId}
                          />
                          <div className="flex gap-2">
                            <Button
                              onClick={updateReferringBusiness}
                              className="bg-green-600 hover:bg-green-700 text-white text-xs px-2 py-1 rounded-md"
                            >
                              Save
                            </Button>
                            <Button
                              onClick={() => {
                                setIsEditingReferringBusiness(false);
                                setSelectedReferringBusinessId("");
                              }}
                              className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1 rounded-md"
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </>
                )}

                <div className="pt-4 space-y-2">
                  {profile.is_card_holder ? (
                    <div className="flex flex-col sm:flex-row gap-2">
                      <Button
                        onClick={() => setIsRedeemModalOpen(true)}
                        className="bg-blue-600 hover:bg-blue-700 text-white text-sm px-6 py-2 rounded-md shadow-md"
                      >
                        Redeem VIP Code
                      </Button>
                      <Button
                        onClick={() => setIsPhysicalCardModalOpen(true)}
                        variant="outline"
                        className="border-blue-600 text-blue-600 hover:bg-blue-50 text-sm px-6 py-2 rounded-md"
                      >
                        Redeem Physical Card
                      </Button>
                    </div>
                  ) : (
                    <div className="flex flex-col sm:flex-row gap-2">
                      <Button
                        onClick={() => router.push("/upgrade")}
                        className="bg-yellow-500 hover:bg-yellow-400 text-black font-semibold text-sm px-6 py-2 rounded-md shadow-md"
                      >
                        🚀 Get VIP Card - $100
                      </Button>
                      <Button
                        onClick={() => setIsRedeemModalOpen(true)}
                        variant="outline"
                        className="border-white/30 text-white hover:bg-white/10 text-sm px-6 py-2 rounded-md"
                      >
                        Redeem Code
                      </Button>
                      <Button
                        onClick={() => setIsPhysicalCardModalOpen(true)}
                        variant="outline"
                        className="border-white/30 text-white hover:bg-white/10 text-sm px-6 py-2 rounded-md"
                      >
                        Redeem Physical Card
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* Upgrade CTA for Non-Card Holders or How It Works for Card Holders */}
        <AnimatedSection delay={0.2}>
          {!profile.is_card_holder ? (
            // Big upgrade CTA for users without VIP cards
            <div className="relative">
              {/* Background gradient */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-3xl opacity-90"></div>
              <div className="relative bg-black/20 backdrop-blur-sm rounded-3xl p-8 md:p-12 border border-white/10">
                <div className="text-center space-y-6">
                  <div className="inline-flex items-center px-4 py-2 bg-yellow-500 text-black rounded-full text-sm font-semibold">
                    🚀 Limited Time Offer
                  </div>

                  <h2 className="text-4xl md:text-6xl font-bold text-white leading-tight">
                    Get Your <span className="text-yellow-400">VIP Card</span> Now!
                  </h2>

                  <p className="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto">
                    Unlock exclusive discounts, rewards, and premium benefits at hundreds of businesses in our network
                  </p>

                  {/* Benefits Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-8">
                    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                      <div className="text-4xl mb-3">💎</div>
                      <h3 className="text-lg font-semibold text-white mb-2">Premium Discounts</h3>
                      <p className="text-white/80 text-sm">Save 10-50% at partner businesses nationwide</p>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                      <div className="text-4xl mb-3">🎯</div>
                      <h3 className="text-lg font-semibold text-white mb-2">Instant Rewards</h3>
                      <p className="text-white/80 text-sm">Earn tokens and cashback on every purchase</p>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                      <div className="text-4xl mb-3">🌟</div>
                      <h3 className="text-lg font-semibold text-white mb-2">VIP Access</h3>
                      <p className="text-white/80 text-sm">Exclusive events, early access, and priority support</p>
                    </div>
                  </div>

                  {/* Pricing */}
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 max-w-md mx-auto border border-white/20">
                    <div className="text-center">
                      <div className="text-white/70 text-sm line-through">$200/year</div>
                      <div className="text-4xl font-bold text-yellow-400">$100</div>
                      <div className="text-white/80 text-sm">One-time payment • Valid for 1 year</div>
                    </div>
                  </div>

                  {/* CTA Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <Button
                      onClick={() => router.push("/upgrade")}
                      className="bg-yellow-500 hover:bg-yellow-400 text-black font-bold text-lg px-8 py-4 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200"
                    >
                      🚀 Get Your VIP Card Now
                    </Button>
                    <div className="flex flex-col sm:flex-row gap-2">
                      <Button
                        onClick={() => setIsRedeemModalOpen(true)}
                        variant="outline"
                        className="border-white/30 text-white hover:bg-white/10 font-semibold px-6 py-4 rounded-xl"
                      >
                        Have a Code? Redeem Here
                      </Button>
                      <Button
                        onClick={() => setIsPhysicalCardModalOpen(true)}
                        variant="outline"
                        className="border-white/30 text-white hover:bg-white/10 font-semibold px-6 py-4 rounded-xl"
                      >
                        Have a Physical Card?
                      </Button>
                    </div>
                  </div>

                  <p className="text-white/60 text-sm">
                    ⚡ Instant activation • 💳 Secure payment • 🔒 30-day money-back guarantee
                  </p>
                </div>
              </div>
            </div>
          ) : (
            // Simplified "How It Works" for existing card holders
            <div>
              <h2 className="text-2xl font-bold text-center mb-8">How to Maximize Your Benefits</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {[
                  {
                    icon: "🏪",
                    title: "Visit Partner Businesses",
                    description: "Show your VIP card at any partner location to unlock exclusive discounts"
                  },
                  {
                    icon: "📱",
                    title: "Scan QR Codes",
                    description: "QR Code rewards coming soon - earn tokens for every visit and purchase"
                  },
                  {
                    icon: "🎁",
                    title: "Enjoy Premium Rewards",
                    description: "Access exclusive deals, early access to sales, and VIP customer support"
                  }
                ].map((step, index) => (
                  <div
                    key={index}
                    className="p-6 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg shadow-md text-center border border-gray-700"
                  >
                    <div className="text-4xl mb-4">{step.icon}</div>
                    <h3 className="text-lg font-semibold text-white mb-2">{step.title}</h3>
                    <p className="text-gray-300 text-sm">{step.description}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </AnimatedSection>

        {/* Featured Businesses */}
        <AnimatedSection delay={0.4}>
          <div>
            <h2 className="text-2xl font-bold text-center mb-8">Featured Businesses</h2>
            {currentBusiness && (
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="flex justify-center space-x-4 mb-4">
                  <Button
                    onClick={() =>
                      setCurrentIndex((prevIndex) =>
                        prevIndex === 0 ? businesses.length - 1 : prevIndex - 1
                      )
                    }
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2"
                  >
                    Previous
                  </Button>
                  <Button
                    onClick={() =>
                      setCurrentIndex((prevIndex) =>
                        (prevIndex + 1) % businesses.length
                      )
                    }
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2"
                  >
                    Next
                  </Button>
                </div>
                <Image
                  src={currentBusiness.logo_url}
                  alt={`${currentBusiness.name} logo`}
                  width={100}
                  height={100}
                  className="rounded-full shadow-md"
                />
                <h3 className="text-xl font-semibold">{currentBusiness.name}</h3>
                <p className="text-gray-400">{currentBusiness.premium_discount}</p>
                <Button
                  onClick={() =>
                    window.open(currentBusiness.website, "_blank", "noopener noreferrer")
                  }
                  className="px-4 py-2"
                >
                  Visit Website
                </Button>
              </div>
            )}
          </div>
        </AnimatedSection>

        

        {/* Modals */}
        <RedeemCodeModal
          isOpen={isRedeemModalOpen}
          onClose={() => setIsRedeemModalOpen(false)}
        />
        <PhysicalCardRedeemModal
          isOpen={isPhysicalCardModalOpen}
          onClose={() => setIsPhysicalCardModalOpen(false)}
        />
      </div>
    </div>
  );
}