import { requireAuth } from '@/lib/auth-server'
import { redirect } from 'next/navigation'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Require authentication for all dashboard pages
  const auth = await requireAuth('/dashboard')
  
  // If we get here, user is authenticated
  return (
    <div className="min-h-screen">
      {children}
    </div>
  )
}
