"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import { X, Play, Check, ArrowRight, ChevronRight, Star, TrendingUp } from 'lucide-react'
import { Link } from "@/components/ui/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AnimatedSection } from "@/components/animated-section"
import { CyclingBusinesses } from "@/components/business/cycling-businesses"
import { Dialog, Transition } from "@headlessui/react"
import { Fragment } from "react"
import { supabase } from "@/lib/supabase"
import { NetworkMap } from "@/components/map/network-map"
import { BusinessDataProvider } from "@/contexts/business-data-context"

// Define types for better code organization
type PricingTier = {
  name: string;
  price: string;
  description: string;
  features: string[];
  cta: string;
  ctaLink: string;
  popular?: boolean;
};

type Feature = {
  title: string;
  description: string;
  icon: React.ReactNode;
  link: string;
};

type VideoModalProps = {
  videoId: string;
  isOpen: boolean;
  onClose: () => void;
  title: string;
};

// Data for pricing tiers
const pricingTiers: PricingTier[] = [
  {
    name: "Premium",
    price: "$100",
    description: "Perfect for local businesses and loyal customers looking to unlock smarter rewards.",
    features: [
      "Dual rewards for customers & businesses",
      "Boosts local revenue & repeat visits",
      "Custom loyalty tiers & perks",
      "Featured in the FUSE Network Directory",
      "Support across U.S. regions"
    ],
    cta: "Start Today",
    ctaLink: "/register",
    popular: true
  },
  {
    name: "Enterprise",
    price: "Custom",
    description: "Tailored loyalty infrastructure for high-volume or multi-location businesses.",
    features: [
      "All Premium features included",
      "Strategic account manager",
      "Custom software integrations",
      "Full API & webhook access",
      "White-labeling & advanced branding"
    ],
    cta: "Contact Sales",
    ctaLink: "/book-call"
  },
];

// Key features data
const keyFeatures: Feature[] = [
  {
    title: "Tokenized Loyalty",
    description: "Convert customers into $FUSE tokens. Referring a purchased VIP Card earns you $FUSE tokens that can be used across the network.",
    icon: <Star className="h-6 w-6 text-[#FFD700]" />,
    link: "/fuse"
  },
  {
    title: "Yield Pool",
    description: "Earn passive income on your crypto. Deposit XRP or USDC and watch it grow automatically with competitive yields.",
    icon: <TrendingUp className="h-6 w-6 text-green-500" />,
    link: "/yield-pool"
  },
  {
    title: "See your Discounts & Rewards",
    description: "VIP customers click here and redeem discounts at all participating businesses in the network.",
    icon: <ArrowRight className="h-6 w-6 text-[#316bff]" />,
    link: "/industry"
  },
  {
    title: "Real-Time Analytics",
    description: "Our team will provide insights about customer behavior to optimize your business.",
    icon: <Check className="h-6 w-6 text-green-500" />,
    link: "/solutions"
  },
];

const VideoModal = ({ videoId, isOpen, onClose, title }: VideoModalProps) => {
  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-lg bg-black text-left align-middle shadow-xl transition-all">
                <div className="relative">
                  <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 z-10"
                    aria-label="Close video"
                  >
                    <X className="h-6 w-6" />
                  </button>
                  <div className="relative pb-[56.25%] h-0">
                    <iframe
                      src={`https://www.youtube.com/embed/${videoId}?autoplay=1`}
                      title={title}
                      className="absolute top-0 left-0 w-full h-full"
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    />
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};





export default function Home() {
  const [showVideo, setShowVideo] = useState(false)
  const [showTokenVideo, setShowTokenVideo] = useState(false)
  const [businesses, setBusinesses] = useState<any[]>([])

  const youtubeVideoId = "uBDquOBgd0c"
  const tokenVideoId = "qjc966ZwKkk"

  useEffect(() => {
    async function loadBusinesses() {
      if (!supabase) return;
      const { data, error } = await supabase
        .from('businesses')
        .select('id, name, category, logo_url, premium_discount, latitude, longitude')
        .eq('is_active', true)
        .order('name')

      if (error) {
        console.error('Error loading businesses:', error)
      } else {
        setBusinesses(data || [])
      }
    }

    loadBusinesses()
  }, [])

  return (
    <>
      {/* Hero Section - Refined messaging and layout */}
      <section className="relative w-full min-h-screen bg-gradient-to-b from-[#000814] via-[#000d1a] to-black overflow-hidden flex items-center justify-center text-white px-4 sm:px-8 py-20">
        {/* Background Image with overlay */}
        <div className="absolute inset-0 z-0 after:content-[''] after:absolute after:inset-0 after:bg-radial-gradient after:from-transparent after:to-black/70">
          <Image
            src="/images/landing-bckgrnd-2.png"
            alt="Fuse.vip Background"
            width={1600}
            height={900}
            className="object-cover w-full h-full opacity-30"
            priority
          />
        </div>

        {/* Foreground Content */}
        <div className="relative z-10 flex flex-col lg:flex-row w-full max-w-7xl mx-auto items-center justify-between gap-12">
          {/* Left Column - Value Proposition */}
          <div className="flex flex-col space-y-6 max-w-xl">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Badge className="bg-[#316bff]/20 text-[#316bff] hover:bg-[#316bff]/30 mb-4">
                Revolutionizing Customer Loyalty
              </Badge>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.1 }}
              className="text-4xl sm:text-5xl md:text-6xl font-bold text-white leading-tight"
            >
              Loyalty Reimagined <span className="text-[#FF914D]">Commerce Reconnected</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
              className="text-lg sm:text-xl text-gray-300"
            >
              Elevate your loyalty with the FUSE VIP Card. Shop local, earn rewards, and help businesses grow. Get yours on the Upgrade Page after signing up today.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.3 }}
              className="pt-2 space-y-4 sm:space-y-0 sm:flex sm:flex-row sm:gap-4"
            >
              <Link href="/register">
                <Button
                  size="lg"
                  className="w-full sm:w-auto bg-[#316bff] hover:bg-[#2151d3] text-white text-base sm:text-lg px-6 sm:px-8 py-6 h-auto"
                >
                  Get Discounts and Rewards Now!
                </Button>
              </Link>

              <Button
                size="lg"
                variant="outline"
                className="w-full sm:w-auto border-white text-black hover:bg-white/10 text-base sm:text-lg px-6 sm:px-8 py-6 h-auto flex items-center justify-center gap-2"
                onClick={() => setShowVideo(true)}
              >
                <Play className="h-5 w-5" /> Watch Demo
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.4 }}
              className="pt-4"
            >
              <p className="text-sm text-gray-400 flex items-center gap-2">
                <Check className="h-4 w-4 text-green-500" /> Purchase with Stripe
              </p>
              <p className="text-sm text-gray-400 flex items-center gap-2 mt-1">
                <Check className="h-4 w-4 text-green-500" /> Join 70+ businesses already using Fuse.vip
              </p>


            </motion.div>
          </div>

          {/* Right Column - Product Showcase */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="relative w-full max-w-md lg:max-w-lg xl:max-w-xl"
          >
            <div className="relative bg-gradient-to-br from-[#1a1f2e] to-[#0d1117] p-4 rounded-xl border border-gray-800 shadow-2xl">
              <div className="absolute -top-2 -right-2 bg-[#FFD700] text-black text-xs font-bold py-1 px-3 rounded-full">
                PREMIUM
              </div>
              <div className="flex items-center justify-between border-b border-gray-800 pb-3 mb-4">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
                <div className="text-xs text-gray-400">Fuse.vip Dashboard</div>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-semibold">FUSE.vip Network</h3>
                    <p className="text-sm text-gray-400">Est. March, 2025</p>
                  </div>
                  <Badge className="bg-green-500/20 text-green-400 hover:bg-green-500/30">
                    +24% ↑
                  </Badge>
                </div>
                <div className="w-full">
                  <img
                    src="/images/resources1.jpg"
                    alt="Fuse VIP Network"
                    className="w-full h-auto rounded-lg object-cover"
                  />
                </div>
                <div className="bg-white/5 p-4 rounded-lg">
                  <h4 className="text-sm font-medium mb-3">Customer Retention</h4>
                  <div className="h-24 flex items-end gap-1">
                    {[35, 45, 30, 60, 75, 65, 80].map((height, i) => (
                      <div
                        key={i}
                        className="bg-[#316bff] rounded-t w-full"
                        style={{ height: `${height}%` }}
                      ></div>
                    ))}
                  </div>
                  <div className="flex justify-between mt-2 text-xs text-gray-400">
                    <span>Mon</span>
                    <span>Tue</span>
                    <span>Wed</span>
                    <span>Thu</span>
                    <span>Fri</span>
                    <span>Sat</span>
                    <span>Sun</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Link href="/upgrade">
                    <Image
                      src="/images/premium-card.png"
                      alt="Premium Fuse Card"
                      width={80}
                      height={50}
                      className="rounded-md cursor-pointer hover:opacity-80 transition-opacity"
                    />
                  </Link>
                  <div>
                    <h4 className="text-sm font-medium">Unlock more rewards with your own Premium VIP Card</h4>
                    <p className="text-xs text-gray-400">Offer a discount to VIP members and thats it.</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Cycling Businesses Section */}
      <CyclingBusinesses />

      {/* Social Proof Section */}
      <section className="bg-white py-10">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap items-center justify-center gap-6 md:gap-10">
            <p className="text-gray-500 text-sm font-medium w-full text-center mb-4">BUILT ON PROVEN TECHNOLOGIES</p>
            {[
              { name: 'Stripe', url: 'https://stripe.com/', },
              { name: 'XRP', url: 'https://xrpl.org/', },
              { name: 'Xaman Wallet', url: 'https://xaman.app/#features', },
              { name: 'Vercel', url: 'https://vercel.com/', }
            ].map((brand, index) => (
              <a
                key={index} //
                href={brand.url}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-[#316bff] hover:bg-[#2151d3] text-white px-4 py-2 rounded-md text-sm sm:text-base font-medium transition-all"
              >
                {brand.name}
              </a>
            ))}
          </div>
        </div>
      </section>

      {/* Network Map Section */}
      <section className="py-20 bg-gray-50" id="network-map">
        <div className="container mx-auto px-4 text-center">
          <AnimatedSection>
            <Link href="/industry">
              <h2 className="text-3xl md:text-4xl font-bold mb-8 text-[#1e293b] hover:text-[#316bff] transition-colors cursor-pointer">
                Find Rewards Near You ↓
              </h2>
            </Link>
            <NetworkMap businesses={businesses} />
          </AnimatedSection>
        </div>
      </section>

      {/* Key Features Section */}
      <section className="py-20 bg-gray-50" id="features">
        <div className="container mx-auto px-4">
          <AnimatedSection>
            <div className="text-center mb-16">
              <Badge className="bg-[#316bff]/10 text-[#316bff] mb-4">
                Key Features
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[#1e293b]">
                Fuse into the Future of Loyalty.
              </h2>
              <p className="text-xl text-[#4a4a4a] max-w-2xl mx-auto">
                Fuse provides all the tools you need to create, manage, and grow your loyalty program.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {keyFeatures.map((feature, index) => (
              <AnimatedSection key={index} delay={index * 0.2}>
                <Link href={feature.link}>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    transition={{ type: "spring", stiffness: 300 }}
                    className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl hover:bg-gray-50 transition-all h-full cursor-pointer"
                  >
                    <div className="w-12 h-12 bg-[#316bff]/10 rounded-lg flex items-center justify-center mb-6">
                      {feature.icon}
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-[#1e293b]">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </motion.div>
                </Link>
              </AnimatedSection>
            ))}
          </div>


          <AnimatedSection delay={0.4}>
            <div className="bg-white rounded-xl shadow-xl overflow-hidden border border-gray-100">
              <div className="grid grid-cols-1 lg:grid-cols-2">
                <div className="p-8 md:p-12 flex flex-col justify-center">
                  <Badge className="bg-[#316bff]/10 text-[#316bff] mb-4 w-fit">
                    Powerful Platform
                  </Badge>
                  <h3 className="text-2xl md:text-3xl font-bold mb-4 text-[#1e293b]">
                    Designed for The Future of Business
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Our platform combines the best of traditional loyalty programs with the innovation of blockchain technology,
                    creating a seamless experience for both businesses and customers.
                  </p>
                  <ul className="space-y-3 mb-8">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                      <span>Easy to set up and manage</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                      <span>Customizable to your brand</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                      <span>Integrates with your existing systems</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                      <span>No technical expertise required</span>
                    </li>
                  </ul>
                  <Link href="/solutions">
                    <Button className="bg-[#316bff] hover:bg-[#2151d3] text-white w-fit">
                      Learn More <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </Link>
                </div>
                <div className="bg-gradient-to-br from-[#0f172a] to-[#1e293b] p-8 md:p-12 text-white">
                  <h3 className="text-2xl font-bold mb-6">How It Works</h3>
                  <ol className="space-y-6">
                    <li className="flex">
                      <div className="flex-shrink-0 w-8 h-8 bg-[#316bff] rounded-full flex items-center justify-center mr-4 mt-0.5">
                        1
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg mb-1">Sign Up</h4>
                        <p className="text-white/70">Create your account and customize your loyalty program to match your brand.</p>
                      </div>
                    </li>
                    <li className="flex">
                      <div className="flex-shrink-0 w-8 h-8 bg-[#316bff] rounded-full flex items-center justify-center mr-4 mt-0.5">
                        2
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg mb-1">Onboard Customers</h4>
                        <p className="text-white/70">Invite your customers to join Fuse.vip and have them tag you in sign up.</p>
                      </div>
                    </li>
                    <li className="flex">
                      <div className="flex-shrink-0 w-8 h-8 bg-[#316bff] rounded-full flex items-center justify-center mr-4 mt-0.5">
                        3
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg mb-1">Reward Transactions</h4>
                        <p className="text-white/70">Businesses earn $FUSE tokens automatically with every purchase of a VIP card.</p>
                      </div>
                    </li>
                    <li className="flex">
                      <div className="flex-shrink-0 w-8 h-8 bg-[#316bff] rounded-full flex items-center justify-center mr-4 mt-0.5">
                        4
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg mb-1">Analyze & Optimize</h4>
                        <p className="text-white/70">Use our analytics to understand customer behavior and optimize your program.</p>
                      </div>
                    </li>
                  </ol>
                </div>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>



      {/* Pricing Section */}
      <section className="py-20 bg-gray-50" id="pricing">
        <div className="container mx-auto px-4">
          <AnimatedSection>
            <div className="text-center mb-16">
              <Badge className="bg-[#316bff]/10 text-[#316bff] mb-4">
                Pricing
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[#1e293b]">
                Simple, Transparent Pricing
              </h2>
              <p className="text-xl text-[#4a4a4a] max-w-2xl mx-auto">
                Choose the plan that's right for your business. All plans include access to our core features.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {pricingTiers.map((tier, index) => (
              <AnimatedSection key={index} delay={index * 0.2}>
                <div className={`bg-white rounded-xl overflow-hidden shadow-lg border ${tier.popular ? 'border-[#316bff]' : 'border-gray-200'} h-full flex flex-col`}>
                  {tier.popular && (
                    <div className="bg-[#316bff] text-white text-center py-2 text-sm font-medium">
                      Most Popular
                    </div>
                  )}
                  <div className="p-8">
                    <h3 className="text-xl font-bold mb-2 text-[#1e293b]">{tier.name}</h3>
                    <div className="mb-4">
                      <span className="text-4xl font-bold text-[#1e293b]">{tier.price}</span>
                      {tier.price !== "Free" && tier.price !== "Custom" && (
                        <span className="text-gray-500">/year</span>
                      )}
                    </div>
                    <p className="text-gray-600 mb-6">{tier.description}</p>

                    {/*Wrap Button Inside Link */}
                    <Link href={tier.ctaLink}>
                      <Button
                        className={`w-full ${tier.popular
                          ? 'bg-[#316bff] hover:bg-[#2151d3] text-white'
                          : 'bg-white border border-[#316bff] text-[#316bff] hover:bg-[#316bff]/5'}`}
                      >
                        {tier.cta}
                      </Button>
                    </Link>

                  </div>
                  <div className="p-8 bg-gray-50 flex-grow">
                    <p className="font-medium text-[#1e293b] mb-4">What's included:</p>
                    <ul className="space-y-3">
                      {tier.features.map((feature, i) => (
                        <li key={i} className="flex items-start">
                          <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </AnimatedSection>
            ))}
          </div>

          <div className="mt-12 text-center">
            <p className="text-gray-500 mb-4">All plans include a premium FUSE VIP card.</p>
            <p className="text-gray-500">
              Need a custom solution? <Link href="/book-call" className="text-[#316bff] hover:underline">Contact our sales team</Link>
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section - Enhanced */}
      <section className="py-20 bg-gradient-to-r from-[#0f172a] to-[#1e293b] text-white" >
        <div className="container mx-auto px-4">
          <AnimatedSection>
            <div className="max-w-4xl mx-auto text-center">
              <Badge className="bg-white/10 text-white hover:bg-white/20 mb-4">
                Get Started Today
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">
                Ready to Transform Your Customer Relationships?
              </h2>
              <p className="text-xl text-white/80 max-w-2xl mx-auto mb-8">
                Join nearly 300 VIP's already using Fuse.vip to increase their rewards.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Link href="/register">
                  <Button
                    size="lg"
                    className="bg-[#FF914D] hover:bg-[#FF915D] text-black font-medium w-full sm:w-auto"
                  >
                    Start Your Free Trial
                  </Button>
                </Link>
                <Link href="/book-call">
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-white text-black hover:bg-grey hover:text-black w-full sm:w-auto transition-colors"
                  >
                    Schedule a Demo
                  </Button>
                </Link>
              </div>
              <p className="text-sm text-white/60 mt-6">
                No credit card required. 14-day free trial.
              </p>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Video Modals */}
      <VideoModal
        videoId={youtubeVideoId}
        isOpen={showVideo}
        onClose={() => setShowVideo(false)}
        title="Fuse.Vip Introduction"
      />
      <VideoModal
        videoId={tokenVideoId}
        isOpen={showTokenVideo}
        onClose={() => setShowTokenVideo(false)}
        title="Fuse Token Information"
      />

    </>
  );
}

export function App({ Component, pageProps }: any) {
  return (
    <BusinessDataProvider>
      <Component {...pageProps} />
    </BusinessDataProvider>
  );
}


