"use client"

import { useState } from "react"
import { <PERSON>Header } from "@/components/page-header"
import { CtaSection } from "@/components/cta-section"
import { useAuth } from "@/contexts/auth-context"
import { supabase } from "@/lib/supabase"
import { useRouter } from "next/navigation"
import { ProtectedRoute } from "@/components/protected-route"
import { BusinessForm } from "@/components/business/business-form"
import { CheckCircle } from "lucide-react"
import { AnimatedSection } from "@/components/animated-section"

export default function RegisterBusinessPage() {
  const { user, refreshSession } = useAuth()
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const router = useRouter()

  const handleFormSubmit = async (formData: any) => {
    if (!user) return

    setLoading(true)

    // Insert into network_applications table
    const { data, error } = await supabase
      .from("network_applications")
      .insert({ 
        ...formData, 
        user_id: user.id,
        status: "pending",
        created_at: new Date().toISOString()
      })
      .select()

    if (!error && data) {
      setSuccess(true)
      // Refresh the user session to update any relevant status
      await refreshSession()

      // Redirect after a short delay
      setTimeout(() => {
        router.push("/dashboard")
      }, 2000)
    } else {
      console.error("Error registering business:", error)
    }

    setLoading(false)
  }

  return (
    <ProtectedRoute>
      <PageHeader
        title="Register Your Business"
        subtitle="BUSINESS APPLICATION"
        description="Join our network of businesses and start offering loyalty rewards to your customers."
      />

      <section className="py-16">
        <div className="container mx-auto px-4">
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#3A56FF]"></div>
            </div>
          ) : success ? (
            <AnimatedSection>
              <div className="bg-white p-8 rounded-lg shadow-md text-center max-w-md mx-auto">
                <div className="bg-green-100 p-4 rounded-full inline-block mb-4">
                  <CheckCircle className="h-12 w-12 text-green-500" />
                </div>
                <h2 className="text-2xl font-bold mb-2">Application Submitted!</h2>
                <p className="text-gray-600 mb-4">
                  Your business application has been submitted successfully. Our team will review it shortly.
                </p>
                <p className="text-sm text-gray-500">
                  You will be redirected to your dashboard in a moment...
                </p>
              </div>
            </AnimatedSection>
          ) : (
            <div className="max-w-3xl mx-auto">
              <BusinessForm onSubmit={handleFormSubmit} onCancel={() => router.push("/dashboard")} />
            </div>
          )}
        </div>
      </section>

      <CtaSection />
    </ProtectedRoute>
  )
}
