'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { 
  Trophy, 
  Zap, 
  Target, 
  Gamepad2,
  ArrowRight,
  ExternalLink
} from 'lucide-react'
import { FuseBird } from '@/components/games/fuse-bird'
import { Game2048 } from '@/components/games/game-2048'
import { RockPaperScissors } from '@/components/games/rock-paper-scissors'
import { GamingProvider } from '@/contexts/gaming-context'
import { FuseGamingProvider } from '@/contexts/fuse-gaming-context'
import { ProtectedRoute } from '@/components/protected-route'
import { useAuth } from '@/contexts/auth-context'
import { useWallet } from '@/hooks/use-wallet'
import { useFuseGaming } from '@/contexts/fuse-gaming-context'
import Link from 'next/link'

function GamingContent() {
  const { user } = useAuth()
  const { isConnected } = useWallet()
  const { trustlineStatus } = useFuseGaming()
  const [activeGame, setActiveGame] = useState<string | null>(null)

  const games = [
    {
      id: 'fuse-bird',
      name: 'Fuse Bird',
      icon: '🐦',
      difficulty: 'Hard',
      description: 'Navigate through pipes in this challenging flying game',
      component: FuseBird
    },
    {
      id: '2048',
      name: '2048',
      icon: '🎯',
      difficulty: 'Medium', 
      description: 'Combine tiles to reach the 2048 tile',
      component: Game2048
    },
    {
      id: 'rock-paper-scissors',
      name: 'Rock Paper Scissors',
      icon: '✂️',
      difficulty: 'Easy',
      description: 'Classic game of strategy and luck',
      component: RockPaperScissors
    }
  ]

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'Medium': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'Hard': return 'bg-red-500/20 text-red-400 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  if (activeGame) {
    const game = games.find(g => g.id === activeGame)
    if (game) {
      const GameComponent = game.component
      return (
        <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
          <div className="container mx-auto px-4 py-8">
            <div className="mb-6">
              <Button 
                onClick={() => setActiveGame(null)}
                variant="outline"
                className="border-white/20 text-white hover:bg-white/10"
              >
                ← Back to Games
              </Button>
            </div>
            <GameComponent />
          </div>
        </div>
      )
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-4 py-2 rounded-full font-bold text-sm mb-4">
            🎮 Gaming Hub
            <motion.span
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              🏆
            </motion.span>
          </div>
          <h1 className="text-4xl md:text-6xl font-bold mb-4">
            Play Games, Win <span className="text-yellow-400">FUSE Prizes</span>
          </h1>
          <p className="text-white/80 text-lg max-w-2xl mx-auto">
            Free to play, pay to compete! Use FUSE tokens for competitive gaming and prizes.
          </p>
        </motion.div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center gap-2">
                <Gamepad2 className="h-5 w-5" />
                Account Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-white/70">Signed In:</span>
                  <Badge variant={user ? "default" : "secondary"}>
                    {user ? "Yes" : "No"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/70">Wallet:</span>
                  <Badge variant={isConnected ? "default" : "secondary"}>
                    {isConnected ? "Connected" : "Not Connected"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Native Token Gaming
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-sm text-white/70">
                  Powered by FUSE token ecosystem
                </div>
                <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                  Live
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center gap-2">
                <Zap className="h-5 w-5" />
                FUSE Gaming
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-white/70">Trustline:</span>
                  <Badge variant={trustlineStatus.hasTrustline ? "default" : "secondary"}>
                    {trustlineStatus.hasTrustline ? "Ready" : "Setup Required"}
                  </Badge>
                </div>
                {trustlineStatus.hasTrustline && (
                  <div className="text-sm text-white/70">
                    Balance: {trustlineStatus.balance} FUSE
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Games Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {games.map((game, index) => (
            <motion.div
              key={game.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/15 transition-all cursor-pointer group">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="text-4xl group-hover:scale-110 transition-transform">
                      {game.icon}
                    </div>
                    <Badge className={getDifficultyColor(game.difficulty)}>
                      {game.difficulty}
                    </Badge>
                  </div>
                  <CardTitle className="text-white">{game.name}</CardTitle>
                  <CardDescription className="text-white/70">
                    {game.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button 
                    onClick={() => setActiveGame(game.id)}
                    className="w-full bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700"
                  >
                    Play Now
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Setup Instructions */}
        {(!user || !isConnected || !trustlineStatus.hasTrustline) && (
          <Card className="bg-white/10 backdrop-blur-sm border-white/20 mb-12">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Target className="h-5 w-5" />
                Getting Started
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!user && (
                <div className="flex items-center justify-between p-4 bg-blue-500/20 rounded-lg border border-blue-500/30">
                  <div>
                    <div className="font-semibold text-blue-300">1. Sign In</div>
                    <div className="text-sm text-blue-200">Create an account to track your progress</div>
                  </div>
                  <Link href="/register">
                    <Button variant="outline" className="border-blue-400 text-blue-300 hover:bg-blue-500/20">
                      Sign Up
                    </Button>
                  </Link>
                </div>
              )}
              
              {user && !isConnected && (
                <div className="flex items-center justify-between p-4 bg-purple-500/20 rounded-lg border border-purple-500/30">
                  <div>
                    <div className="font-semibold text-purple-300">2. Connect Wallet</div>
                    <div className="text-sm text-purple-200">Connect your Xaman wallet for competitive play</div>
                  </div>
                  <Button variant="outline" className="border-purple-400 text-purple-300 hover:bg-purple-500/20">
                    Connect Wallet
                  </Button>
                </div>
              )}

              {user && isConnected && !trustlineStatus.hasTrustline && (
                <div className="flex items-center justify-between p-4 bg-yellow-500/20 rounded-lg border border-yellow-500/30">
                  <div>
                    <div className="font-semibold text-yellow-300">3. Setup FUSE Trustline</div>
                    <div className="text-sm text-yellow-200">Enable FUSE token gaming on Magnetic DEX</div>
                  </div>
                  <Button 
                    variant="outline" 
                    className="border-yellow-400 text-yellow-300 hover:bg-yellow-500/20"
                    onClick={() => window.open('https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet', '_blank')}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Setup Trustline
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Back to Dashboard */}
        <div className="text-center">
          <Link href="/dashboard">
            <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
              ← Back to Dashboard
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default function GamesPage() {
  return (
    <ProtectedRoute>
      <GamingProvider>
        <FuseGamingProvider>
          <GamingContent />
        </FuseGamingProvider>
      </GamingProvider>
    </ProtectedRoute>
  )
}
