import 'mapbox-gl/dist/mapbox-gl.css';
import type React from "react"
import "./globals.css"
import { Inter } from "next/font/google"
import type { Metadata, Viewport } from "next"
import { SiteHeader } from "@/components/site-header"
import { SiteFooter } from "@/components/site-footer"
import { PageTransition } from "@/components/page-transition"
import { ScrollProgressBar } from "@/components/scroll-progress-bar"
import { AuthProvider } from "@/contexts/auth-context"
import { ProfileProvider } from "@/contexts/profile-context"
import { ChatProvider } from "@/components/chat/chat-provider"
import { Toaster } from "sonner"

const inter = Inter({ subsets: ["latin"] })

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false, // Prevents zoom on mobile games
  themeColor: "light",
}

export const metadata: Metadata = {
  title: "Fuse.vip - Loyalty Reimagined. Commerce Reconnected!",
  description:
    "Fuse.vip provides innovative loyalty solutions and digital transformation for businesses across various industries.",
  generator: "v0.dev",
  openGraph: {
    title: "Fuse.vip - Loyalty Reimagined. Commerce Reconnected!",
    description: "Fuse.vip provides innovative loyalty solutions and digital transformation for businesses across various industries.",
    url: "https://www.fuse.vip/",
    siteName: "Fuse.vip",
    images: [
      {
        url: "https://www.fuse.vip/images/fuse-logo.png",
        width: 1200,
        height: 630,
        alt: "Fuse.vip Logo",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Fuse.vip - Loyalty Reimagined. Commerce Reconnected!",
    description: "Fuse.vip provides innovative loyalty solutions and digital transformation for businesses across various industries.",
    images: ["https://www.fuse.vip/images/fuse-logo.png"],
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <ProfileProvider>
              <ScrollProgressBar />
              <div className="flex flex-col min-h-screen">
                <SiteHeader />
                <main className="flex-grow">
                  <PageTransition>{children}</PageTransition>
                </main>
                <SiteFooter />
              </div>
              <ChatProvider />
              <Toaster position="top-right" richColors />
          </ProfileProvider>
        </AuthProvider>
      </body>
    </html>
  )
}
