"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { supabase } from "@/lib/supabase";
import ImprovedQRScanner from "@/components/improved-qr-scanner";
import { parseQRData } from "@/lib/qr-code-generator";
import { ProtectedRoute } from "@/components/protected-route";
import { Button } from "@/components/ui/button";
import { ArrowLeft, CheckCircle, AlertCircle } from "lucide-react";

function ScanPageContent() {
  const { user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [scanResult, setScanResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Handle test QR from URL
  useEffect(() => {
    const testQR = searchParams.get('test_qr');
    if (testQR && user) {
      try {
        const decodedQR = decodeURIComponent(testQR);
        console.log('Processing test QR:', decodedQR);
        handleScanSuccess(decodedQR);
      } catch (error) {
        console.error('Failed to process test QR:', error);
      }
    }
  }, [searchParams, user]);

  const handleScanSuccess = async (decodedText: string) => {
    if (isProcessing) return; // Prevent multiple scans

    setIsProcessing(true);

    try {
      console.log("Raw QR data:", decodedText); // Debug log

      // Parse QR code data to determine type
      const qrData = parseQRData(decodedText.trim());
      console.log("Parsed QR data:", qrData); // Debug log

      if (!qrData) {
        throw new Error(`Invalid QR Code format. Scanned data: ${decodedText.substring(0, 100)}...`);
      }

      if (qrData.userId === user?.id) {
        throw new Error("You cannot scan your own QR code");
      }

      let successMessage = "";
      let interactionType: 'user_scan' | 'business_scan' = 'user_scan';

      // Check if user is a cardholder for business scans
      if (qrData.type === 'business') {
        const { data: profile } = await supabase
          .from('profiles')
          .select('is_card_holder')
          .eq('id', user?.id)
          .single();

        if (!profile?.is_card_holder) {
          throw new Error("Only VIP cardholders can earn FUSE rewards by scanning business QR codes. Upgrade to VIP to start earning!");
        }

        // Check for cooldown (1 scan per business per day)
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const { data: existingInteraction } = await supabase
          .from('qr_interactions')
          .select('id')
          .eq('scanner_user_id', user?.id)
          .eq('scanned_business_id', qrData.userId)
          .eq('interaction_type', 'business_scan')
          .gte('created_at', today.toISOString())
          .lt('created_at', tomorrow.toISOString())
          .single();

        if (existingInteraction) {
          throw new Error("You've already scanned this business today. Come back tomorrow to earn more FUSE!");
        }

        // Check if business exists first
        let { data: business, error: businessError } = await supabase
          .from('businesses')
          .select('id, name, status, category')
          .eq('id', qrData.userId)
          .single();

        if (businessError || !business) {
          // Business doesn't exist in local database - offer to create it
          console.log(`Business ${qrData.userId} not found in local database`);

          // Try to create a placeholder business for FUSE Network businesses
          const { data: newBusiness, error: createError } = await supabase
            .from('businesses')
            .insert({
              id: qrData.userId,
              name: `FUSE Network Business`,
              category: 'FUSE Partner',
              description: 'FUSE Network partner business - auto-created from QR scan',
              status: 'approved', // Auto-approve FUSE Network businesses
              user_id: null, // No specific owner
              website: 'https://fuse.vip',
              phone: 'Contact via FUSE Network',
              email: '<EMAIL>',
              address: 'FUSE Network Partner Location',
              auto_created: true
            })
            .select('id, name, status, category')
            .single();

          if (createError) {
            console.error('Failed to create business:', createError);
            throw new Error(`Business not found in database (ID: ${qrData.userId.slice(0, 8)}...). This appears to be a FUSE Network business that hasn't been synced to your local database yet. Please contact support to sync FUSE Network businesses.`);
          }

          console.log('Auto-created FUSE Network business:', newBusiness);
          // Use the newly created business
          business = newBusiness;
        }

        if (business.status !== 'approved') {
          throw new Error(`This business (${business.name}) is not yet approved for FUSE rewards. Status: ${business.status}`);
        }

        // Log the business interaction and award 100 FUSE tokens
        const { error: interactionError } = await supabase.from("qr_interactions").insert({
          scanner_user_id: user?.id,
          scanned_business_id: qrData.userId,
          interaction_type: 'business_scan',
          qr_data: decodedText.trim(),
          interaction_metadata: {
            business_name: business.name,
            fuse_tokens_awarded: 100,
            scan_timestamp: new Date().toISOString()
          }
        });

        if (interactionError) {
          console.error("Business interaction error:", interactionError);
          throw new Error(`Failed to log business interaction: ${interactionError.message}. Please try again.`);
        }

        // Success! Business interaction logged with 100 FUSE tokens awarded
        console.log(`Successfully logged business scan for ${business.name} - 100 FUSE tokens awarded`);

        successMessage = `🎉 Success! You earned 100 FUSE tokens at ${business.name}!`;

        // Redirect to success page with business info
        router.push(`/scan/success?business_id=${qrData.userId}&reward=100`);
        return;
      } else {
        // Handle user QR code scan
        interactionType = 'user_scan';

        // Log the user interaction
        const { error: interactionError } = await supabase.from("qr_interactions").insert({
          scanner_user_id: user?.id,
          scanned_user_id: qrData.userId,
          interaction_type: 'user_scan',
          qr_data: decodedText.trim(),
        });

        if (interactionError) {
          throw interactionError;
        }

        // Get scanned user's profile for personalized message
        const { data: scannedProfile } = await supabase
          .from('profiles')
          .select('first_name, last_name, is_card_holder')
          .eq('id', qrData.userId)
          .single();

        const userName = scannedProfile
          ? `${scannedProfile.first_name} ${scannedProfile.last_name}`
          : 'another user';

        const vipStatus = scannedProfile?.is_card_holder ? ' (VIP Member)' : '';

        successMessage = `Successfully connected with ${userName}${vipStatus}! Check your interactions to see the connection.`;
      }

      setScanResult({
        success: true,
        message: successMessage
      });

      // Auto-redirect after 3 seconds
      setTimeout(() => {
        router.push('/dashboard');
      }, 3000);

    } catch (error) {
      console.error("QR Scan Error:", error);
      setScanResult({
        success: false,
        message: error instanceof Error ? error.message : "Failed to process QR code. Please try again."
      });

      // Allow retry after 2 seconds
      setTimeout(() => {
        setIsProcessing(false);
        setScanResult(null);
      }, 2000);
    } finally {
      // Always reset processing state
      if (!scanResult?.success) {
        setIsProcessing(false);
      }
    }
  };

  const handleScanError = (error: string) => {
    // Only show critical errors to user
    if (error.includes('Camera') || error.includes('permission')) {
      setScanResult({
        success: false,
        message: "Camera access is required to scan QR codes. Please allow camera permissions and try again."
      });
    }
  };

  const handleGoBack = () => {
    router.push('/dashboard');
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <Button
              onClick={handleGoBack}
              variant="ghost"
              className="text-white hover:bg-white/10 mr-4"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Dashboard
            </Button>
          </div>

          <div className="max-w-md mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">
                Scan QR Code
              </h1>
              <p className="text-gray-300">
                Scan QR codes to connect with other users or log business visits
              </p>
            </div>

            {/* Scanner or Result */}
            <div className="bg-white rounded-xl p-6 shadow-xl">
              {scanResult ? (
                <div className="flex flex-col items-center space-y-4 text-center">
                  {scanResult.success ? (
                    <>
                      <CheckCircle className="text-green-500 w-16 h-16" />
                      <h2 className="text-xl font-bold text-green-600">Success!</h2>
                      <p className="text-gray-600">{scanResult.message}</p>
                      <p className="text-sm text-gray-400 italic">
                        Redirecting to dashboard...
                      </p>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="text-red-500 w-16 h-16" />
                      <h2 className="text-xl font-bold text-red-600">Error</h2>
                      <p className="text-gray-600">{scanResult.message}</p>
                      {!scanResult.message.includes('Camera') && (
                        <p className="text-sm text-gray-400 italic">
                          Retrying in a moment...
                        </p>
                      )}
                    </>
                  )}
                </div>
              ) : (
                <div>
                  <ImprovedQRScanner
                    onScanSuccess={handleScanSuccess}
                    onScanError={handleScanError}
                  />

                  {isProcessing && (
                    <div className="mt-4 text-center">
                      <div className="inline-flex items-center px-4 py-2 bg-blue-50 rounded-lg">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                        <span className="text-blue-600 text-sm">Processing scan...</span>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Instructions */}
            {!scanResult && (
              <div className="mt-6 text-center">
                <div className="bg-white/10 rounded-lg p-4">
                  <h3 className="text-white font-semibold mb-2">How to scan:</h3>
                  <ul className="text-gray-300 text-sm space-y-1">
                    <li>• Allow camera access when prompted</li>
                    <li>• Point your camera at the QR code</li>
                    <li>• Keep the code within the scanning frame</li>
                    <li>• Hold steady until the scan completes</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}

export default function ScanPage() {
  return (
    <Suspense fallback={
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      </ProtectedRoute>
    }>
      <ScanPageContent />
    </Suspense>
  );
}
