"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { supabase } from "@/lib/supabase";
import { ProtectedRoute } from "@/components/protected-route";
import { Button } from "@/components/ui/button";
import { CheckCircle, ArrowLeft, Coins, Calendar, MapPin } from "lucide-react";

interface BusinessInfo {
  id: string;
  name: string;
  category: string;
  logo_url?: string;
  website?: string;
}

export default function ScanSuccessPage() {
  const { user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const businessId = searchParams.get('business_id');
  const reward = searchParams.get('reward') || '100';
  
  const [business, setBusiness] = useState<BusinessInfo | null>(null);
  const [totalVisits, setTotalVisits] = useState(0);
  const [totalFuseEarned, setTotalFuseEarned] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!businessId || !user) return;

    const fetchData = async () => {
      try {
        // Fetch business information
        const { data: businessData, error: businessError } = await supabase
          .from('businesses')
          .select('id, name, category, logo_url, website')
          .eq('id', businessId)
          .single();

        if (businessError) {
          console.error('Error fetching business:', businessError);
        } else {
          setBusiness(businessData);
        }

        // Fetch user's total visits and FUSE earned
        const { data: visitsData, error: visitsError } = await supabase
          .from('business_visits')
          .select('id')
          .eq('user_id', user.id);

        if (visitsError) {
          console.error('Error fetching visits:', visitsError);
        } else {
          const visitCount = visitsData?.length || 0;
          setTotalVisits(visitCount);
          setTotalFuseEarned(visitCount * 100); // 100 FUSE per visit
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [businessId, user]);

  const handleGoToDashboard = () => {
    router.push('/dashboard');
  };

  const handleViewRewards = () => {
    router.push('/dashboard/rewards');
  };

  if (!businessId) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-2xl font-bold mb-4">Invalid Request</h1>
            <p className="mb-6">No business information found.</p>
            <Button onClick={handleGoToDashboard} className="bg-blue-600 hover:bg-blue-700">
              Go to Dashboard
            </Button>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-green-900 via-green-800 to-emerald-900">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <Button
              onClick={handleGoToDashboard}
              variant="ghost"
              className="text-white hover:bg-white/10 mr-4"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Dashboard
            </Button>
          </div>

          <div className="max-w-2xl mx-auto">
            {/* Success Animation */}
            <div className="text-center mb-8">
              <div className="relative">
                <CheckCircle className="text-green-400 w-24 h-24 mx-auto mb-4 animate-pulse" />
                <div className="absolute inset-0 bg-green-400/20 rounded-full w-24 h-24 mx-auto animate-ping"></div>
              </div>
              <h1 className="text-4xl font-bold text-white mb-2">
                Scan Successful!
              </h1>
              <p className="text-green-200 text-lg">
                You've earned {reward} $FUSE tokens!
              </p>
            </div>

            {/* Business Info Card */}
            <div className="bg-white rounded-xl p-6 shadow-xl mb-6">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-600"></div>
                </div>
              ) : business ? (
                <div className="flex items-center space-x-4">
                  {business.logo_url ? (
                    <img 
                      src={business.logo_url} 
                      alt={business.name}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                      <MapPin className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                  <div className="flex-1">
                    <h2 className="text-xl font-bold text-gray-900">{business.name}</h2>
                    <p className="text-gray-600">{business.category}</p>
                    {business.website && (
                      <a 
                        href={business.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        Visit Website
                      </a>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="flex items-center text-green-600 font-bold">
                      <Coins className="h-5 w-5 mr-1" />
                      +{reward} FUSE
                    </div>
                    <div className="flex items-center text-gray-500 text-sm">
                      <Calendar className="h-4 w-4 mr-1" />
                      {new Date().toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600">Business information not found</p>
                </div>
              )}
            </div>

            {/* Rewards Summary */}
            <div className="bg-white/10 rounded-xl p-6 mb-6">
              <h3 className="text-white font-bold text-lg mb-4">Your FUSE Rewards</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400">{totalVisits}</div>
                  <div className="text-green-200 text-sm">Total Visits</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400">{totalFuseEarned}</div>
                  <div className="text-green-200 text-sm">Total FUSE Earned</div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                onClick={handleViewRewards}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white font-semibold py-3"
              >
                <Coins className="h-5 w-5 mr-2" />
                View All Rewards
              </Button>
              <Button 
                onClick={handleGoToDashboard}
                variant="outline"
                className="flex-1 border-white text-white hover:bg-white/10 font-semibold py-3"
              >
                Continue Exploring
              </Button>
            </div>

            {/* Next Steps */}
            <div className="mt-8 text-center">
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-semibold mb-2">What's Next?</h4>
                <p className="text-gray-300 text-sm">
                  Visit more Fuse.VIP partner businesses to earn additional FUSE tokens. 
                  Each business can be scanned once per day for rewards.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
