import { NextRequest, NextResponse } from 'next/server';
import { PerplexityClient, checkRateLimit } from '@/lib/perplexity';
import { getContextualPrompt } from '@/lib/fuse-context';
import { getAuthenticatedUser } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    // Get user IP for rate limiting
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown';

    // Check rate limit
    if (!checkRateLimit(ip)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      );
    }

    const { message, conversationHistory = [] } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      );
    }

    // Get Perplexity API key
    const apiKey = process.env.PERPLEXITY_API_KEY;
    if (!apiKey) {
      console.error('Perplexity API key not configured');
      return NextResponse.json(
        { error: 'Chat service not configured' },
        { status: 500 }
      );
    }

    // Initialize Perplexity client
    const perplexity = new PerplexityClient(apiKey);

    // Get user context if authenticated (simplified for now)
    let userContext = '';
    // TODO: Re-enable authentication once cookies API issues are resolved
    // try {
    //   const cookieStore = await cookies();
    //   const user = await getAuthenticatedUser(cookieStore);
    //   if (user) {
    //     userContext = `\n\nUSER CONTEXT: The user is authenticated with email: ${user.email}. You can provide personalized responses and mention their account status.`;
    //   }
    // } catch (error) {
    //   // User not authenticated, continue without user context
    //   console.log('User not authenticated for chat');
    // }

    // Prepare messages for Perplexity
    const systemPrompt = getContextualPrompt(message) + userContext;

    // Simple message structure for now - just system and user message
    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt
      },
      {
        role: 'user' as const,
        content: message
      }
    ];

    // Get response from Perplexity
    const response = await perplexity.chat(messages);

    if (!response.choices || response.choices.length === 0) {
      throw new Error('No response from Perplexity API');
    }

    const assistantMessage = response.choices[0].message.content;

    return NextResponse.json({
      message: assistantMessage,
      usage: response.usage,
      conversationId: response.id
    });

  } catch (error) {
    console.error('Chat API error:', error);
    
    // Return a helpful fallback response
    const fallbackMessage = `I apologize, but I'm experiencing technical difficulties right now. 

For immediate assistance with Fuse.vip, please:
- Email <NAME_EMAIL>
- Join our Discord: https://discord.gg/n9d7PEbm
- Follow us on Twitter: @fuserewards

Fuse.vip is a blockchain-based loyalty platform that helps businesses create engaging customer loyalty programs using the $FUSE token on the XRP Ledger. Our token launches June 15, 2025!`;

    return NextResponse.json({
      message: fallbackMessage,
      error: 'Service temporarily unavailable'
    }, { status: 200 }); // Return 200 to show fallback message
  }
}

export async function GET() {
  return NextResponse.json({
    status: 'Chat API is running',
    endpoints: {
      POST: '/api/chat - Send a chat message',
    },
    rateLimit: '10 requests per minute per IP'
  });
}
