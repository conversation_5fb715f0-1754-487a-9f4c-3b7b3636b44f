import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyFuseTransaction } from '@/lib/fuse-token-utils'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    const { sessionId, score, gameData, userId, transactionHash } = await request.json()

    if (!sessionId || !userId || score === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Get the FUSE gaming session
    const { data: session, error: sessionError } = await supabase
      .from('fuse_gaming_sessions')
      .select('*')
      .eq('id', sessionId)
      .eq('user_id', userId)
      .single()

    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Gaming session not found' },
        { status: 404 }
      )
    }

    // Verify transaction if provided and not already verified
    if (transactionHash && !session.fuse_transaction_verified) {
      try {
        const verificationResult = await verifyFuseTransaction(
          transactionHash, 
          session.entry_fee_fuse.toString(),
          'rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx' // Gaming wallet
        )

        if (verificationResult.verified) {
          // Update session as verified
          await supabase
            .from('fuse_gaming_sessions')
            .update({
              transaction_hash: transactionHash,
              fuse_transaction_verified: true,
              started_at: new Date().toISOString()
            })
            .eq('id', sessionId)

          // Record the transaction
          await supabase
            .from('fuse_gaming_transactions')
            .insert({
              gaming_session_id: sessionId,
              user_id: userId,
              transaction_type: 'entry_fee',
              fuse_amount: session.entry_fee_fuse,
              from_wallet: verificationResult.source || session.wallet_address,
              to_wallet: 'rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx',
              transaction_hash: transactionHash,
              xrpl_verified: true,
              verified_at: new Date().toISOString()
            })
        } else {
          return NextResponse.json(
            { error: 'Transaction verification failed' },
            { status: 400 }
          )
        }
      } catch (verifyError) {
        console.error('Transaction verification error:', verifyError)
        return NextResponse.json(
          { error: 'Failed to verify transaction' },
          { status: 500 }
        )
      }
    }

    // Check if session is verified before allowing score submission
    const { data: updatedSession } = await supabase
      .from('fuse_gaming_sessions')
      .select('fuse_transaction_verified')
      .eq('id', sessionId)
      .single()

    if (!updatedSession?.fuse_transaction_verified) {
      return NextResponse.json(
        { error: 'Session not verified. Please complete payment first.' },
        { status: 400 }
      )
    }

    // Submit the competitive score using the database function
    const { data: result, error: submitError } = await supabase.rpc('submit_fuse_competitive_score', {
      p_user_id: userId,
      p_game_type: session.game_type,
      p_score: score,
      p_gaming_session_id: sessionId,
      p_game_data: gameData,
      p_moves_count: gameData?.moves_count,
      p_highest_tile: gameData?.highest_tile,
      p_game_duration_seconds: gameData?.game_duration_seconds
    })

    if (submitError) {
      console.error('Error submitting FUSE competitive score:', submitError)
      return NextResponse.json(
        { error: 'Failed to submit score to leaderboard' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      result: result,
      message: 'Score submitted successfully'
    })

  } catch (error) {
    console.error('Error in FUSE submit-score API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const userId = searchParams.get('userId')
  const gameType = searchParams.get('gameType')

  if (!userId) {
    return NextResponse.json(
      { error: 'User ID is required' },
      { status: 400 }
    )
  }

  try {
    let query = supabase
      .from('fuse_gaming_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(50)

    if (gameType) {
      query = query.eq('game_type', gameType)
    }

    const { data: sessions, error } = await query

    if (error) {
      console.error('Error fetching FUSE gaming sessions:', error)
      return NextResponse.json(
        { error: 'Failed to fetch gaming sessions' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      sessions: sessions || []
    })

  } catch (error) {
    console.error('Error in get FUSE gaming sessions API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Endpoint to verify a transaction manually
export async function PATCH(request: NextRequest) {
  try {
    const { sessionId, transactionHash, userId } = await request.json()

    if (!sessionId || !transactionHash || !userId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Get the session
    const { data: session, error: sessionError } = await supabase
      .from('fuse_gaming_sessions')
      .select('*')
      .eq('id', sessionId)
      .eq('user_id', userId)
      .single()

    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Gaming session not found' },
        { status: 404 }
      )
    }

    // Verify the transaction
    const verificationResult = await verifyFuseTransaction(
      transactionHash,
      session.entry_fee_fuse.toString(),
      'rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx'
    )

    if (!verificationResult.verified) {
      return NextResponse.json(
        { error: 'Transaction verification failed', details: verificationResult.error },
        { status: 400 }
      )
    }

    // Update session
    const { error: updateError } = await supabase
      .from('fuse_gaming_sessions')
      .update({
        transaction_hash: transactionHash,
        fuse_transaction_verified: true,
        started_at: new Date().toISOString()
      })
      .eq('id', sessionId)

    if (updateError) {
      return NextResponse.json(
        { error: 'Failed to update session' },
        { status: 500 }
      )
    }

    // Record transaction
    await supabase
      .from('fuse_gaming_transactions')
      .insert({
        gaming_session_id: sessionId,
        user_id: userId,
        transaction_type: 'entry_fee',
        fuse_amount: session.entry_fee_fuse,
        from_wallet: verificationResult.source || session.wallet_address,
        to_wallet: 'rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx',
        transaction_hash: transactionHash,
        xrpl_verified: true,
        verified_at: new Date().toISOString()
      })

    return NextResponse.json({
      success: true,
      verified: true,
      message: 'Transaction verified successfully'
    })

  } catch (error) {
    console.error('Error verifying FUSE transaction:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
