import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    const { sessionId, score, gameData, userId } = await request.json()

    if (!sessionId || !userId || score === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Update the game session with final score and game data
    const { data: session, error: updateError } = await supabase
      .from('game_sessions')
      .update({
        final_score: score,
        game_data: gameData,
        moves_count: gameData?.moves_count,
        highest_tile: gameData?.highest_tile,
        game_duration_seconds: gameData?.game_duration_seconds,
        ended_at: new Date().toISOString()
      })
      .eq('id', sessionId)
      .eq('user_id', userId)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating game session:', updateError)
      return NextResponse.json(
        { error: 'Failed to update game session' },
        { status: 500 }
      )
    }

    // If this is a competitive game, submit to leaderboards
    if (session.session_type === 'competitive' && session.is_verified) {
      try {
        // Call the database function to submit competitive score
        const { error: submitError } = await supabase.rpc('submit_competitive_score', {
          p_user_id: userId,
          p_game_type: session.game_type,
          p_score: score,
          p_entry_fee_xrp: session.entry_fee_xrp,
          p_wallet_address: session.wallet_address,
          p_transaction_hash: session.transaction_hash,
          p_game_data: gameData,
          p_moves_count: gameData?.moves_count,
          p_highest_tile: gameData?.highest_tile,
          p_game_duration_seconds: gameData?.game_duration_seconds
        })

        if (submitError) {
          console.error('Error submitting competitive score:', submitError)
          // Don't fail the request, just log the error
        }
      } catch (error) {
        console.error('Error calling submit_competitive_score function:', error)
      }
    }

    return NextResponse.json({
      success: true,
      session: session
    })

  } catch (error) {
    console.error('Error in submit-score API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const userId = searchParams.get('userId')
  const gameType = searchParams.get('gameType')

  if (!userId) {
    return NextResponse.json(
      { error: 'User ID is required' },
      { status: 400 }
    )
  }

  try {
    let query = supabase
      .from('game_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(50)

    if (gameType) {
      query = query.eq('game_type', gameType)
    }

    const { data: sessions, error } = await query

    if (error) {
      console.error('Error fetching game sessions:', error)
      return NextResponse.json(
        { error: 'Failed to fetch game sessions' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      sessions: sessions || []
    })

  } catch (error) {
    console.error('Error in get game sessions API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
