import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { generateUniqueUserQRId, generateQRCodeDataURL } from '@/lib/qr-code-generator';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, forceRegenerate = false } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Check if user already has a QR code
    if (!forceRegenerate) {
      const { data: existingQR } = await supabase
        .from('user_qr_codes')
        .select('qr_data, qr_code_url')
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();

      if (existingQR?.qr_code_url) {
        return NextResponse.json({
          success: true,
          qrCodeUrl: existingQR.qr_code_url,
          message: 'QR code already exists'
        });
      }
    }

    // Get user profile for email
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('user_email')
      .eq('id', userId)
      .single();

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Generate new QR code
    const qrData = generateUniqueUserQRId(userId, profile.user_email);
    const qrCodeDataUrl = await generateQRCodeDataURL(qrData, { 
      size: 256,
      color: {
        dark: '#1f2937', // gray-800
        light: '#ffffff'
      }
    });

    // Save to database
    const { error } = await supabase
      .from('user_qr_codes')
      .upsert({
        user_id: userId,
        qr_data: qrData,
        qr_code_url: qrCodeDataUrl,
        is_active: true,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    if (error) {
      console.error('Error saving QR code:', error);
      return NextResponse.json(
        { error: 'Failed to save QR code' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      qrCodeUrl: qrCodeDataUrl,
      message: forceRegenerate ? 'QR code regenerated successfully' : 'QR code generated successfully'
    });

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Bulk generate QR codes for all users without them
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const adminKey = searchParams.get('adminKey');

    // Simple admin key check (in production, use proper authentication)
    if (adminKey !== 'fuse-admin-2024') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const supabase = await createClient();

    // Get all users and filter out those who already have QR codes
    const { data: allUsers, error: allUsersError } = await supabase
      .from('profiles')
      .select('id, user_email');

    if (allUsersError) {
      console.error('Error fetching all users:', allUsersError);
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      );
    }

    // Get users who already have QR codes
    const { data: usersWithQR } = await supabase
      .from('user_qr_codes')
      .select('user_id')
      .eq('is_active', true);

    const userIdsWithQR = new Set(usersWithQR?.map(u => u.user_id) || []);

    // Filter out users who already have QR codes
    const usersWithoutQR = allUsers?.filter(user => !userIdsWithQR.has(user.id)) || [];
    const usersError = null;

    if (usersError) {
      console.error('Error fetching users:', usersError);
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      );
    }

    if (!usersWithoutQR || usersWithoutQR.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'All users already have QR codes',
        generated: 0
      });
    }

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    // Generate QR codes for each user
    for (const user of usersWithoutQR) {
      try {
        const qrData = generateUniqueUserQRId(user.id, user.user_email);
        const qrCodeDataUrl = await generateQRCodeDataURL(qrData, { 
          size: 256,
          color: {
            dark: '#1f2937',
            light: '#ffffff'
          }
        });

        const { error } = await supabase
          .from('user_qr_codes')
          .insert({
            user_id: user.id,
            qr_data: qrData,
            qr_code_url: qrCodeDataUrl,
            is_active: true
          });

        if (error) {
          console.error(`Error saving QR code for user ${user.id}:`, error);
          results.push({ userId: user.id, success: false, error: error.message });
          errorCount++;
        } else {
          results.push({ userId: user.id, success: true });
          successCount++;
        }
      } catch (error) {
        console.error(`Error generating QR code for user ${user.id}:`, error);
        results.push({ 
          userId: user.id, 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
        errorCount++;
      }
    }

    return NextResponse.json({
      success: true,
      message: `Generated QR codes for ${successCount} users`,
      generated: successCount,
      errors: errorCount,
      results: results
    });

  } catch (error) {
    console.error('Bulk generation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
