import { NextResponse } from "next/server";
import { serverEnv } from "@/lib/server-env";

// Basic in-memory rate limiting
const RATE_LIMIT = 10; // requests per minute
const tokenCache = new Map<string, { count: number; timestamp: number }>();

export async function GET(request: Request) {
  try {
    const ip = (request.headers.get("x-forwarded-for") ?? "unknown").toString();
    const now = Date.now();

    const clientData = tokenCache.get(ip) || { count: 0, timestamp: now };

    // Reset counter if a minute has passed
    if (now - clientData.timestamp > 60000) {
      clientData.count = 0;
      clientData.timestamp = now;
    }

    clientData.count++;
    tokenCache.set(ip, clientData);

    if (clientData.count > RATE_LIMIT) {
      return NextResponse.json({ error: "Rate limit exceeded" }, { status: 429 });
    }

    if (!serverEnv.MAPBOX_ACCESS_TOKEN) {
      console.error("Mapbox Access Token missing in environment");
      return NextResponse.json({ error: "Token not configured" }, { status: 500 });
    }

    return NextResponse.json({
      token: serverEnv.MAPBOX_ACCESS_TOKEN,
      expiresIn: 300, // token "lifetime" in seconds (5 minutes)
    });
  } catch (error) {
    console.error("Error generating Mapbox token:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}