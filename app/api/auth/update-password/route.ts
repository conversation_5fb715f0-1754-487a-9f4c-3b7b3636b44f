import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { createAdminClient } from '@/lib/supabase/admin'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()
    
    if (!email) {
      console.error('Missing email in request payload')
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    if (!password) {
      console.error('Missing password in request payload')
      return NextResponse.json(
        { error: 'Password is required' },
        { status: 400 }
      )
    }

    // Create admin client
    const supabaseAdmin = createAdminClient()

    // Find user by email
    console.log('Looking up user by email:', email)
    const { data: { users }, error: listError } = await supabaseAdmin.auth.admin.listUsers({
      filter: {
        email: email
      }
    })
    
    if (listError) {
      console.error('Error listing users:', listError)
      return NextResponse.json(
        { error: 'Failed to find user' },
        { status: 500 }
      )
    }

    if (!users || users.length === 0) {
      console.error('User not found with email:', email)
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    const userId = users[0].id
    console.log('Updating password for user ID:', userId)

    // Update the user's password using admin client
    const { error } = await supabaseAdmin.auth.admin.updateUserById(
      userId,
      { password }
    )

    if (error) {
      console.error('Password update error:', error)
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    console.log('Password updated successfully for user:', email)

    return NextResponse.json({
      success: true,
      message: 'Password updated successfully'
    })

  } catch (error) {
    console.error('Password update exception:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}
