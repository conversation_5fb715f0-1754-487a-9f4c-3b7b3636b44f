import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createTokensFromSupabaseSession, setTokenCookies } from '@/lib/jwt'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    console.log('Login attempt for email:', email)

    if (!email || !password) {
      console.error('Missing email or password')
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Check if environment variables are defined
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables missing')
      return NextResponse.json(
        { error: 'Supabase configuration is missing' },
        { status: 500 }
      )
    }

    console.log('Creating Supabase client...')
    const supabase = await createClient()

    // Authenticate with Supabase
    console.log('Attempting Supabase authentication...')
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      console.error('Supabase auth error:', error)
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      )
    }

    if (!data.session) {
      console.error('No session returned from Supabase')
      return NextResponse.json(
        { error: 'Authentication failed - no session' },
        { status: 401 }
      )
    }

    console.log('Authentication successful, creating JWT tokens...')
    // Create JWT tokens
    const { accessToken, refreshToken } = await createTokensFromSupabaseSession(data.session)

    // Create response
    const response = NextResponse.json({
      user: data.user,
      session: data.session,
      message: 'Login successful'
    })

    // Set JWT tokens in cookies
    setTokenCookies(response, accessToken, refreshToken)

    console.log('Login successful for user:', data.user.id)
    return response
  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
