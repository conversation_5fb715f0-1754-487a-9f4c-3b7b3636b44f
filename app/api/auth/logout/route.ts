import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'
import { clearTokenCookies } from '@/lib/jwt'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)

    // Sign out from Supabase
    const { error } = await supabase.auth.signOut()

    if (error) {
      console.error('Supabase logout error:', error)
    }

    // Create response
    const response = NextResponse.json({
      message: 'Logout successful'
    })

    // Clear JWT tokens from cookies
    clearTokenCookies(response)

    return response
  } catch (error) {
    console.error('Logout error:', error)
    
    // Even if there's an error, clear the cookies
    const response = NextResponse.json(
      { error: 'Logout completed with errors' },
      { status: 200 }
    )
    
    clearTokenCookies(response)
    return response
  }
}
