import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'
import {
  getTokenFromCookies,
  verifyJWT,
  createTokensFromSupabaseSession,
  setTokenCookies,
  clearTokenCookies,
  isTokenExpired,
  REFRESH_TOKEN_COOKIE
} from '@/lib/jwt'

export async function POST(request: NextRequest) {
  try {
    // Get refresh token directly from request cookies as a fallback
    // This is more reliable in middleware and API routes
    const cookieStore = cookies()
    let refreshToken = request.cookies.get(REFRESH_TOKEN_COOKIE)?.value;
    
    // If not found in request, try from cookie store
    if (!refreshToken) {
      try {
        refreshToken = cookieStore.get(REFRESH_TOKEN_COOKIE)?.value || null;
      } catch (cookieError) {
        console.error('Error accessing cookie store:', cookieError);
      }
    }
    
    if (!refreshToken) {
      console.log('No refresh token found');
      return NextResponse.json(
        { error: 'No refresh token' },
        { status: 401 }
      );
    }
    
    // Verify refresh token
    let validJwtToken = false;
    try {
      const payload = await verifyJWT(refreshToken);
      if (payload && !isTokenExpired(payload)) {
        validJwtToken = true;
      } else {
        console.log('Invalid or expired refresh token');
        // Continue to Supabase refresh as fallback
      }
    } catch (jwtError) {
      console.log('JWT verification failed, falling back to Supabase refresh');
      // Continue to Supabase refresh as fallback
    }

    // Get fresh session from Supabase
    const supabase = await createClient();
    const { data, error } = await supabase.auth.refreshSession();

    if (error || !data.session) {
      console.error('Supabase refresh error:', error);
      const response = NextResponse.json(
        { error: 'Failed to refresh session' },
        { status: 401 }
      );
      clearTokenCookies(response);
      return response;
    }

    // Create new JWT tokens
    const { accessToken, refreshToken: newRefreshToken } = await createTokensFromSupabaseSession(data.session);

    // Create response
    const response = NextResponse.json({
      user: data.user,
      session: data.session,
      message: 'Token refreshed successfully'
    });

    // Set JWT tokens in cookies
    setTokenCookies(response, accessToken, newRefreshToken);

    return response;
  } catch (error) {
    console.error('Refresh error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
