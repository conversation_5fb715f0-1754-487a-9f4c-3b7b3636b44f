import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'
import {
  getTokenFromCookies,
  verifyJWT,
  createTokensFromSupabaseSession,
  setTokenCookies,
  clearTokenCookies,
  isTokenExpired
} from '@/lib/jwt'

export async function POST(request: NextRequest) {
  try {
    // Get refresh token from cookies
    const refreshToken = await getTokenFromCookies(cookies(), 'refresh')

    if (!refreshToken) {
      return NextResponse.json(
        { error: 'No refresh token found' },
        { status: 401 }
      )
    }

    // Verify refresh token
    const refreshPayload = await verifyJWT(refreshToken)

    if (!refreshPayload || isTokenExpired(refreshPayload)) {
      const response = NextResponse.json(
        { error: 'Invalid or expired refresh token' },
        { status: 401 }
      )
      clearTokenCookies(response)
      return response
    }

    // Get fresh session from Supabase
    const supabase = await createClient()
    const { data, error } = await supabase.auth.refreshSession()

    if (error || !data.session) {
      console.error('Supabase refresh error:', error)
      const response = NextResponse.json(
        { error: 'Failed to refresh session' },
        { status: 401 }
      )
      clearTokenCookies(response)
      return response
    }

    // Create new JWT tokens
    const { accessToken, refreshToken: newRefreshToken } = await createTokensFromSupabaseSession(data.session)

    // Create response
    const response = NextResponse.json({
      user: data.user,
      session: data.session,
      message: 'Token refreshed successfully'
    })

    // Set new JWT tokens in cookies
    setTokenCookies(response, accessToken, newRefreshToken)

    return response
  } catch (error) {
    console.error('Token refresh error:', error)
    const response = NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
    clearTokenCookies(response)
    return response
  }
}
