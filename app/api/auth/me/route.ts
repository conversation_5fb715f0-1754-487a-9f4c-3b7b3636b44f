import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'
import { getTokenFromCookies, verifyJWT, isTokenExpired } from '@/lib/jwt'

export async function GET(request: NextRequest) {
  try {
    // First try JWT-based authentication
    const cookieStore = cookies()
    const accessToken = await getTokenFromCookies(cookieStore, 'access')
    
    if (accessToken) {
      try {
        const payload = await verifyJWT(accessToken)
        if (payload && !isTokenExpired(payload)) {
          // Valid JWT token, return user data
          return NextResponse.json({
            user: {
              id: payload.sub,
              email: payload.email,
              role: payload.role || 'authenticated',
            }
          })
        }
      } catch (jwtError) {
        console.log('JWT verification failed, falling back to Supabase session')
        // Continue to Supabase auth as fallback
      }
    }
    
    // Create Supabase client using cookies
    const supabase = await createClient()
    
    // Get session from cookies
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      console.error('Session error:', sessionError)
      return NextResponse.json(
        { error: 'Authentication error', details: sessionError.message },
        { status: 401 }
      )
    }
    
    if (!session) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }
    
    // Return user data
    return NextResponse.json({
      user: {
        id: session.user.id,
        email: session.user.email,
        role: session.user.role || 'authenticated',
      }
    })
  } catch (error) {
    console.error('Auth error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}


