import { NextRequest, NextResponse } from 'next/server'
import { getAuthenticatedUser, getUserProfile } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user from JWT
    const user = await getAuthenticatedUser()

    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    // Get user profile
    const profile = await getUserProfile(user.sub)

    return NextResponse.json({
      user: {
        id: user.sub,
        email: user.email,
        role: user.role,
      },
      profile,
    })
  } catch (error) {
    console.error('Get user error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
