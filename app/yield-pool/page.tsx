"use client";

import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment } from "react";
import { CompartmentalizedYieldPool } from "@/components/yield-pool/compartmentalized-yield-pool";
import { PageTransition } from "@/components/page-transition";
import { ScrollProgressBar } from "@/components/scroll-progress-bar";
import { TrendingUp, Shield, Zap, X } from "lucide-react";
import { GamingProvider } from "@/contexts/gaming-context";
import { FuseGamingProvider } from "@/contexts/fuse-gaming-context";
import { FuseBird } from "@/components/games/fuse-bird";
import { RockPaperScissors } from "@/components/games/rock-paper-scissors";
import { Game2048 } from "@/components/games/game-2048";
import { LiveGamingNotifications, useGamingNotifications } from "@/components/gaming/live-gaming-notifications";
import { initializeMobileOptimizations } from "@/lib/mobile-browser-utils";



const GameModal = ({ isOpen, onClose, game }: {
  isOpen: boolean;
  onClose: () => void;
  game: 'fuse-bird' | 'rock-paper-scissors' | '2048' | null;
}) => {
  if (!game) return null;

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-auto transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all">
                <div className="relative p-6">
                  <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-full p-2 z-10"
                    aria-label="Close game"
                  >
                    <X className="h-5 w-5" />
                  </button>
                  <div className="pt-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
                      {game === 'fuse-bird' ? '🐦 Fuse Bird' :
                       game === 'rock-paper-scissors' ? '✂️ Rock Paper Scissors' :
                       '🔢 2048'}
                    </h3>
                    {game === 'fuse-bird' ? (
                      <GamingProvider>
                        <FuseGamingProvider>
                          <FuseBird />
                        </FuseGamingProvider>
                      </GamingProvider>
                    ) : game === 'rock-paper-scissors' ? (
                      <GamingProvider>
                        <FuseGamingProvider>
                          <RockPaperScissors />
                        </FuseGamingProvider>
                      </GamingProvider>
                    ) : (
                      <GamingProvider>
                        <FuseGamingProvider>
                          <Game2048 />
                        </FuseGamingProvider>
                      </GamingProvider>
                    )}
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default function YieldPoolPage() {
  const [selectedGame, setSelectedGame] = useState<'fuse-bird' | 'rock-paper-scissors' | '2048' | null>(null);

  // Initialize mobile optimizations
  useEffect(() => {
    const cleanup = initializeMobileOptimizations();
    return cleanup;
  }, []);

  // Show gaming notifications after user has been on page for a bit
  const showNotifications = useGamingNotifications(true);

  const handleOpenGame = (game: 'fuse-bird' | 'rock-paper-scissors' | '2048') => {
    setSelectedGame(game);
  };

  return (
    <GamingProvider>
      <FuseGamingProvider>
        <PageTransition>
          <ScrollProgressBar />
          <div className="min-h-screen bg-gradient-to-b from-[#000814] via-[#001122] to-black">
            <div className="container mx-auto px-4 py-12">
            {/* Hero Section with Simplified Messaging */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="mb-12 text-center"
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                className="inline-flex items-center gap-2 bg-green-500/20 text-green-400 px-6 py-2 rounded-full font-medium text-sm mb-6"
              >
                <TrendingUp className="h-4 w-4" />
                Earn While You Hold
              </motion.div>

              <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                  Fuse With The Future
                </span>
              </h1>

              <p className="text-2xl text-white/90 max-w-4xl mx-auto mb-4 font-medium">
                Burn FUSE tokens for multipliers and compete with FUSE for prizes
              </p>

              <p className="text-lg text-white/70 max-w-3xl mx-auto mb-8">
                Unlock powerful multipliers by burning FUSE tokens and compete in skill-based games using FUSE tokens for cryptocurrency rewards. The future of native token gaming is here.
              </p>

              {/* Key Benefits */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.5 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6"
                >
                  <Zap className="h-8 w-8 text-yellow-400 mx-auto mb-3" />
                  <h3 className="text-white font-semibold mb-2">Burn for Multipliers</h3>
                  <p className="text-white/70 text-sm">Burn FUSE tokens to unlock permanent reward multipliers and exclusive features</p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.5 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6"
                >
                  <Shield className="h-8 w-8 text-blue-400 mx-auto mb-3" />
                  <h3 className="text-white font-semibold mb-2">Secure & Transparent</h3>
                  <p className="text-white/70 text-sm">Built on XRP Ledger with provable token burning and full transparency</p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.5 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6"
                >
                  <TrendingUp className="h-8 w-8 text-green-400 mx-auto mb-3" />
                  <h3 className="text-white font-semibold mb-2">Gaming Rewards</h3>
                  <p className="text-white/70 text-sm">Compete in skill-based games for real FUSE token prizes</p>
                </motion.div>
              </div>
            </motion.div>

            {/* Use the new compartmentalized component */}
            <CompartmentalizedYieldPool onOpenGame={handleOpenGame} />


          </div>
        </div>



        {/* Game Modal */}
        <GameModal
          isOpen={selectedGame !== null}
          onClose={() => setSelectedGame(null)}
          game={selectedGame}
        />

        {/* Live Gaming Notifications */}
        {showNotifications && <LiveGamingNotifications />}
        </PageTransition>
      </FuseGamingProvider>
    </GamingProvider>
  );
}