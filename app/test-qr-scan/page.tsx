"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import { supabase } from "@/lib/supabase";
import { ProtectedRoute } from "@/components/protected-route";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, TestTube, User, Building2, AlertTriangle, CheckCircle } from "lucide-react";
import { useRouter } from "next/navigation";

export default function TestQRScanPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [userProfile, setUserProfile] = useState<any>(null);
  const [businesses, setBusinesses] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [testResults, setTestResults] = useState<any[]>([]);

  useEffect(() => {
    if (!user) return;

    const loadData = async () => {
      try {
        // Get user profile
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        setUserProfile(profile);

        // Get approved businesses
        const { data: businessData } = await supabase
          .from('businesses')
          .select('id, name, category, status')
          .eq('status', 'approved')
          .limit(5);

        setBusinesses(businessData || []);
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [user]);

  const generateTestQR = (businessId: string) => {
    return JSON.stringify({
      userId: businessId,
      type: 'business',
      timestamp: Date.now()
    });
  };

  const testQRScan = async (businessId: string, businessName: string) => {
    const qrData = generateTestQR(businessId);
    
    setTestResults(prev => [...prev, {
      business: businessName,
      qrData,
      timestamp: new Date().toLocaleTimeString(),
      status: 'testing'
    }]);

    // Simulate the scan by redirecting to scan page with the data
    const encodedQR = encodeURIComponent(qrData);
    router.push(`/scan?test_qr=${encodedQR}`);
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <Button
              onClick={() => router.push('/dashboard')}
              variant="ghost"
              className="text-white hover:bg-white/10 mr-4"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Dashboard
            </Button>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">
                🧪 QR Scan Testing
              </h1>
              <p className="text-gray-300">
                Test QR scanning with real businesses from your database
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* User Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Your Account Status
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Email:</span>
                      <span className="text-sm">{user?.email}</span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">VIP Cardholder:</span>
                      <div className="flex items-center gap-2">
                        {userProfile?.is_card_holder ? (
                          <>
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="text-sm text-green-600">Yes</span>
                          </>
                        ) : (
                          <>
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                            <span className="text-sm text-red-600">No</span>
                          </>
                        )}
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Name:</span>
                      <span className="text-sm">
                        {userProfile?.first_name} {userProfile?.last_name}
                      </span>
                    </div>
                  </div>

                  {!userProfile?.is_card_holder && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        <span className="text-sm font-semibold text-red-800">
                          VIP Membership Required
                        </span>
                      </div>
                      <p className="text-xs text-red-700">
                        You need VIP cardholder status to earn FUSE rewards. 
                        Business QR scans will fail until you upgrade.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Available Businesses */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Test with Real Businesses
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {businesses.length === 0 ? (
                    <div className="text-center py-8">
                      <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        No Approved Businesses
                      </h3>
                      <p className="text-gray-600 text-sm mb-4">
                        There are no approved businesses in your database yet. 
                        You need to create and approve businesses first.
                      </p>
                      <Button 
                        onClick={() => router.push('/admin/businesses')}
                        size="sm"
                      >
                        Manage Businesses
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <p className="text-sm text-gray-600 mb-4">
                        Click a business below to generate and test its QR code:
                      </p>
                      {businesses.map((business) => (
                        <div 
                          key={business.id}
                          className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                        >
                          <div>
                            <h4 className="font-medium text-gray-900">{business.name}</h4>
                            <p className="text-sm text-gray-600">{business.category}</p>
                            <p className="text-xs text-gray-400">ID: {business.id.slice(0, 8)}...</p>
                          </div>
                          <Button
                            onClick={() => testQRScan(business.id, business.name)}
                            size="sm"
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            <TestTube className="h-4 w-4 mr-2" />
                            Test QR
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Instructions */}
            <Card className="mt-8">
              <CardHeader>
                <CardTitle>Testing Instructions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Prerequisites:</h4>
                    <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                      <li>You must be a VIP cardholder (is_card_holder = true)</li>
                      <li>At least one business must be approved in the database</li>
                      <li>The business_visits table must exist (run the migration)</li>
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">How to test:</h4>
                    <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                      <li>Ensure you have VIP status (check above)</li>
                      <li>Click "Test QR" on any approved business</li>
                      <li>You'll be redirected to the scan page with test data</li>
                      <li>The scan should process automatically</li>
                      <li>You should see success and earn 100 FUSE</li>
                    </ol>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">If testing fails:</h4>
                    <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                      <li>Check the browser console for detailed error messages</li>
                      <li>Use the /debug-qr page for step-by-step validation</li>
                      <li>Verify the database migration has been run</li>
                      <li>Ensure your user profile has is_card_holder = true</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
