# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://haqbtbpmyadkocakqnew.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhhcWJ0YnBteWFka29jYWtxbmV3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NjM3OTksImV4cCI6MjA2MjEzOTc5OX0.jW8pms-oHeY9jnEZeGprQjPvOfEvQBdhvlJ7snL78Wk

SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhhcWJ0YnBteWFka29jYWtxbmV3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjU2Mzc5OSwiZXhwIjoyMDYyMTM5Nzk5fQ.4chAp1N2ZayZM6ukrDrQ79l2tZ8PjDj2Gtk0VBqaiQA
SUPABASE_JWT_SECRET=OYZYdBMVLorOJQeYrUKzr8psxSbe/3Dhxk2a2BkjS6/ssHqtNq23Na4hxF0QhF3D90xjJtGOVf1RGIXs5AnuTA==

# Postgres
POSTGRES_URL=postgres://postgres.haqbtbpmyadkocakqnew:<EMAIL>:6543/postgres?sslmode=require&supa=base-pooler.x
POSTGRES_PRISMA_URL=postgres://postgres.haqbtbpmyadkocakqnew:<EMAIL>:6543/postgres?sslmode=require&supa=base-pooler.xtgres_prisma_url
POSTGRES_URL_NON_POOLING=postgres://postgres.haqbtbpmyadkocakqnew:<EMAIL>:5432/postgres?sslmode=require
POSTGRES_USER=postgres
POSTGRES_PASSWORD=JUI38oP1HPLz5cjO
POSTGRES_DATABASE=postgres
POSTGRES_HOST=db.haqbtbpmyadkocakqnew.supabase.co

# Xaman Wallet
NEXT_PUBLIC_XAMAN_API_KEY=e49c965e-77ef-4f66-baa9-09c54383050e

# Stripe
STRIPE_SECRET_KEY=***********************************************************************************************************
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_51QwvpfE4IOnXedOC1kZlMlM6P9eUsTT9dVWu9sOwRrOEM5Oj6OF2VjOw3PN33sUyUncFdJtMzAgMyIRPiNt9e6Jf00Byb0YgkN
STRIPE_WEBHOOK_SECRET=27d270ae7893af99ada8aa5bad0f8d55496ca765c1bc6ae48cf1f171972ce6cc

# Mapbox
NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoiZnVzZWdyb3d0aDEiLCJhIjoiY205aXRma3Z0MDM3aDJrcTBucTkycnY5NCJ9.ILB_lsVONNKMMIZdIe5J5w
MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoiZnVzZWdyb3d0aDEiLCJhIjoiY205aXRma3Z0MDM3aDJrcTBucTkycnY5NCJ9.ILB_lsVONNKMMIZdIe5J5w

# Perplexity AI
PERPLEXITY_API_KEY=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