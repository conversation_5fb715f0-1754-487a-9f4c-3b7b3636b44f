"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Wallet, Send, Check, AlertCircle, ExternalLink } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useWallet } from '@/hooks/use-wallet'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface XamanTransactionTestProps {
  className?: string
}

export function XamanTransactionTest({ className }: XamanTransactionTestProps) {
  const { user } = useAuth()
  const { isConnected, walletAddress, connect, createPayment } = useWallet()
  const [isLoading, setIsLoading] = useState(false)
  const [transactionResult, setTransactionResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const GAMING_WALLET = 'rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx'
  const TEST_AMOUNTS = [
    { label: 'Bronze', amount: 2, color: 'bg-orange-600' },
    { label: 'Silver', amount: 10, color: 'bg-gray-400' },
    { label: 'Gold', amount: 50, color: 'bg-yellow-500' }
  ]

  const handleConnectWallet = async () => {
    try {
      setIsLoading(true)
      setError(null)
      await connect()
    } catch (err) {
      setError('Failed to connect wallet. Please try again.')
      console.error('Wallet connection error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestTransaction = async (amount: number, tier: string) => {
    if (!isConnected || !createPayment) {
      setError('Wallet not connected')
      return
    }

    try {
      setIsLoading(true)
      setError(null)
      setTransactionResult(null)

      // Convert XRP to drops (1 XRP = 1,000,000 drops)
      const amountInDrops = (amount * 1000000).toString()
      const memo = `Fuse.vip ${tier} gaming entry fee: ${amount} XRP - TEST`

      console.log('Creating payment:', {
        destination: GAMING_WALLET,
        amount: amountInDrops,
        memo
      })

      const paymentResult = await createPayment(GAMING_WALLET, amountInDrops, memo)
      
      console.log('Payment result:', paymentResult)
      setTransactionResult(paymentResult)

    } catch (err) {
      console.error('Transaction error:', err)
      setError(`Transaction failed: ${err instanceof Error ? err.message : 'Unknown error'}`)
    } finally {
      setIsLoading(false)
    }
  }

  const openXamanApp = () => {
    if (transactionResult?.refs?.qr_uri_quality_opts?.[0]) {
      window.open(transactionResult.refs.qr_uri_quality_opts[0], '_blank')
    }
  }

  return (
    <div className={`bg-gradient-to-r from-purple-900/20 to-blue-900/20 rounded-lg p-6 border border-purple-500/20 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-white flex items-center gap-3">
          <Wallet className="h-6 w-6 text-purple-400" />
          Xaman Transaction Test
        </h3>
        <Badge variant="outline" className="border-purple-500 text-purple-400">
          🧪 Testing Mode
        </Badge>
      </div>

      {error && (
        <Alert className="mb-4 border-red-500/20 bg-red-900/20">
          <AlertCircle className="h-4 w-4 text-red-400" />
          <AlertDescription className="text-red-200">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {!user ? (
        <div className="text-center">
          <p className="text-white/70 mb-4">Please sign in to test Xaman transactions</p>
          <Button disabled className="w-full">
            Sign In Required
          </Button>
        </div>
      ) : !isConnected ? (
        <div className="text-center">
          <p className="text-white/70 mb-4">Connect your Xaman wallet to test transactions</p>
          <Button 
            onClick={handleConnectWallet} 
            disabled={isLoading}
            className="w-full bg-purple-600 hover:bg-purple-700"
          >
            <Wallet className="h-4 w-4 mr-2" />
            {isLoading ? 'Connecting...' : 'Connect Xaman Wallet'}
          </Button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Wallet Status */}
          <div className="bg-green-900/20 border border-green-500/20 rounded-lg p-4">
            <div className="flex items-center text-green-400 mb-2">
              <Check className="h-4 w-4 mr-2" />
              <span className="font-medium">Wallet Connected</span>
            </div>
            <p className="text-white/70 text-sm">
              Address: {walletAddress?.substring(0, 12)}...{walletAddress?.substring(-8)}
            </p>
          </div>

          {/* Gaming Wallet Info */}
          <div className="bg-black/30 rounded-lg p-4 border border-purple-500/30">
            <h4 className="text-purple-400 font-medium mb-2">Test Destination:</h4>
            <code className="text-white font-mono text-sm break-all block">
              {GAMING_WALLET}
            </code>
          </div>

          {/* Test Transaction Buttons */}
          <div className="space-y-3">
            <h4 className="text-white font-medium">Test Entry Fee Transactions:</h4>
            {TEST_AMOUNTS.map((tier) => (
              <Button
                key={tier.label}
                onClick={() => handleTestTransaction(tier.amount, tier.label)}
                disabled={isLoading}
                className={`w-full ${tier.color} hover:opacity-80 text-white font-semibold`}
              >
                <Send className="h-4 w-4 mr-2" />
                Test {tier.label} - {tier.amount} XRP
              </Button>
            ))}
          </div>

          {/* Transaction Result */}
          {transactionResult && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-blue-900/20 border border-blue-500/20 rounded-lg p-4"
            >
              <h4 className="text-blue-400 font-medium mb-3 flex items-center gap-2">
                <Check className="h-4 w-4" />
                Transaction Created Successfully
              </h4>
              
              <div className="space-y-3">
                <div>
                  <p className="text-white/70 text-sm">Transaction UUID:</p>
                  <code className="text-white text-xs break-all">
                    {transactionResult.uuid}
                  </code>
                </div>
                
                {transactionResult.refs?.qr_uri_quality_opts?.[0] && (
                  <Button
                    onClick={openXamanApp}
                    className="w-full bg-blue-600 hover:bg-blue-700"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open in Xaman App
                  </Button>
                )}
                
                <div className="text-xs text-white/50">
                  <p>✅ Payment request created</p>
                  <p>✅ Destination: Gaming wallet</p>
                  <p>✅ Memo: Included for tracking</p>
                  <p>⏳ Waiting for user to sign in Xaman app</p>
                </div>
              </div>
            </motion.div>
          )}

          {/* Instructions */}
          <div className="bg-yellow-900/20 border border-yellow-500/20 rounded-lg p-4">
            <h4 className="text-yellow-400 font-medium mb-2">How to Test:</h4>
            <ol className="text-white/70 text-sm space-y-1 list-decimal list-inside">
              <li>Click any test transaction button above</li>
              <li>A payment request will be created</li>
              <li>Click "Open in Xaman App" to sign the transaction</li>
              <li>Complete the payment in your Xaman wallet</li>
              <li>The XRP will be sent to the gaming wallet</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  )
}
