"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Wallet, Copy, Check, Calendar, Trophy, Info } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useIsMobile } from '@/hooks/use-mobile'
import { getBrowserInfo } from '@/lib/mobile-browser-utils'
import { AccountOffer } from '@/types/xrpl-offers'

interface GamingWalletInfoProps {
  className?: string
  compact?: boolean
}

export function GamingWalletInfo({ className, compact = false }: GamingWalletInfoProps) {
  const [copied, setCopied] = useState(false)
  const isMobile = useIsMobile()
  const browserInfo = getBrowserInfo()

  const GAMING_WALLET = 'rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx'
  const PUBLICATION_DATE = 'June 13, 2025'
  const PUBLICATION_TIME = new Date().toLocaleTimeString()

  const handleCopyWallet = async () => {
    try {
      await navigator.clipboard.writeText(GAMING_WALLET)
      setCopied(true)
      
      // Haptic feedback for mobile
      if (browserInfo.supportsVibration && isMobile) {
        navigator.vibrate(25)
      }
      
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy wallet address:', err)
    }
  }

  if (compact) {
    return (
      <div className={`bg-gradient-to-r from-green-900/20 to-emerald-900/20 rounded-lg p-4 border border-green-500/20 ${className}`}>
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-semibold text-white flex items-center gap-2">
            <Wallet className="h-4 w-4 text-green-400" />
            Gaming Wallet
          </h4>
          <Badge variant="outline" className="border-green-500 text-green-400 text-xs">
            Live
          </Badge>
        </div>
        
        <div className="bg-black/30 rounded p-3 border border-green-500/30">
          <div className="flex items-center justify-between mb-1">
            <span className="text-green-400 text-xs">Submission Address:</span>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleCopyWallet}
              className="h-6 px-2 text-xs hover:bg-green-600/20"
            >
              {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
            </Button>
          </div>
          <code className="text-white font-mono text-xs break-all block">
            {GAMING_WALLET}
          </code>
        </div>
        
        <p className="text-white/60 text-xs mt-2">
          📅 Published: {PUBLICATION_DATE} • 🏆 Weekly & Monthly Rewards
        </p>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`bg-gradient-to-r from-green-900/20 to-emerald-900/20 rounded-lg p-6 border border-green-500/20 ${className}`}
    >
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-white flex items-center gap-3">
          <Wallet className="h-6 w-6 text-green-400" />
          Official Gaming Wallet
        </h3>
        <Badge variant="outline" className="border-green-500 text-green-400">
          🟢 Live & Active
        </Badge>
      </div>
      
      {/* Wallet Address Section */}
      <div className="space-y-4">
        <div className="bg-black/30 rounded-lg p-4 border border-green-500/30">
          <div className="flex items-center justify-between mb-3">
            <span className="text-green-400 font-medium">Submission Wallet Address:</span>
            <Button
              onClick={handleCopyWallet}
              size="sm"
              className={`transition-all duration-200 ${
                copied 
                  ? 'bg-green-600 hover:bg-green-700 text-white' 
                  : 'bg-green-600 hover:bg-green-700 text-white'
              }`}
            >
              {copied ? (
                <>
                  <Check className="h-4 w-4 mr-1" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-1" />
                  Copy
                </>
              )}
            </Button>
          </div>
          <code className="text-white font-mono text-sm break-all block bg-black/50 p-3 rounded border">
            {GAMING_WALLET}
          </code>
        </div>

        {/* Publication Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-black/20 rounded-lg p-4 border border-green-500/10">
            <h4 className="text-green-400 font-medium mb-2 flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Published
            </h4>
            <p className="text-white font-semibold">{PUBLICATION_DATE}</p>
            <p className="text-white/70 text-sm">{PUBLICATION_TIME} UTC</p>
          </div>
          
          <div className="bg-black/20 rounded-lg p-4 border border-green-500/10">
            <h4 className="text-green-400 font-medium mb-2 flex items-center gap-2">
              <Trophy className="h-4 w-4" />
              Status
            </h4>
            <p className="text-white font-semibold">Ready for Submissions</p>
            <p className="text-white/70 text-sm">All games active</p>
          </div>
        </div>

        {/* Reward Schedule */}
        <div className="bg-gradient-to-r from-yellow-900/20 to-orange-900/20 rounded-lg p-4 border border-yellow-500/20">
          <h4 className="text-yellow-400 font-medium mb-3 flex items-center gap-2">
            🏆 Reward Schedule
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-3">
              <div className="text-2xl">📅</div>
              <div>
                <p className="text-white font-medium">Weekly Rewards</p>
                <p className="text-white/70 text-sm">Every Sunday</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="text-2xl">🗓️</div>
              <div>
                <p className="text-white font-medium">Monthly Rewards</p>
                <p className="text-white/70 text-sm">Last day of month</p>
              </div>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-black/30 rounded border border-yellow-500/30">
            <p className="text-yellow-200 text-sm flex items-start gap-2">
              <Info className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <span>
                <strong>Pro Tip:</strong> Submit your scores to this wallet to participate in competitive gaming rewards. 
                Include your game type and score in the transaction memo for proper tracking.
              </span>
            </p>
          </div>
        </div>



        {/* Competitive Gaming Coming Soon */}
        <div className="bg-gray-900/20 rounded-lg p-4 border border-gray-500/20">
          <h4 className="text-gray-400 font-medium mb-3 flex items-center gap-2">
            🚧 Competitive Gaming - Coming Soon in Fuse
          </h4>
          <div className="bg-black/30 rounded p-3 border border-gray-500/30">
            <div className="text-gray-300 text-sm space-y-2 text-center">
              <div className="text-4xl mb-2">🎮</div>
              <div><strong>Under Development</strong></div>
              <div className="text-gray-400">
                We're working on an exciting competitive gaming system with XRP prizes,
                leaderboards, and automated payments through the XRP Ledger.
              </div>
            </div>
          </div>
          <p className="text-gray-300 text-sm mt-3 text-center">
            This feature will be available soon as part of the Fuse ecosystem.
            For now, enjoy playing the games for free!
          </p>
          <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-2 text-xs">
            <div className="bg-black/20 rounded p-2 text-center">
              <span className="text-gray-400 font-medium">🏆</span>
              <div className="text-gray-300">Leaderboards</div>
            </div>
            <div className="bg-black/20 rounded p-2 text-center">
              <span className="text-gray-400 font-medium">💰</span>
              <div className="text-gray-300">XRP Prizes</div>
            </div>
            <div className="bg-black/20 rounded p-2 text-center">
              <span className="text-gray-400 font-medium">⚡</span>
              <div className="text-gray-300">Auto Payments</div>
            </div>
          </div>
        </div>

        {/* Development Notice */}
        <div className="bg-blue-900/20 rounded-lg p-4 border border-blue-500/20">
          <h4 className="text-blue-400 font-medium mb-2 flex items-center gap-2">
            ℹ️ Development Status
          </h4>
          <p className="text-blue-200 text-sm">
            Competitive gaming features are currently under development.
            We're building a secure, automated system for XRP-based gaming competitions.
            Follow our updates for the latest news on when this feature will be available!
          </p>
        </div>
      </div>
    </motion.div>
  )
}
