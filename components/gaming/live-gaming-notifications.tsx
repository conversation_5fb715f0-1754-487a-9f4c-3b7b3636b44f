'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Trophy, Coins, Users, TrendingUp } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

interface GameNotification {
  id: string
  type: 'winner' | 'prize_pool' | 'new_player' | 'high_score'
  message: string
  game?: string
  amount?: number
  player?: string
  timestamp: Date
}

// Real notifications - only show actual game activity, no fake prize pools
const mockNotifications: GameNotification[] = [
  {
    id: '3',
    type: 'high_score',
    message: 'New high score achieved in Rock Paper Scissors!',
    game: 'Rock Paper Scissors',
    player: 'Anonymous Player',
    timestamp: new Date(Date.now() - 180000)
  },
  {
    id: '4',
    type: 'new_player',
    message: 'New players are joining competitive gaming',
    timestamp: new Date(Date.now() - 300000)
  }
]

export function LiveGamingNotifications() {
  const [notifications, setNotifications] = useState<GameNotification[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isVisible, setIsVisible] = useState(true)
  const [xrpPrice, setXrpPrice] = useState<number>(2.30) // Default fallback price

  // Fetch XRP price
  useEffect(() => {
    const fetchXrpPrice = async () => {
      try {
        const response = await fetch("https://api.coingecko.com/api/v3/simple/price?ids=ripple&vs_currencies=usd")
        const data = await response.json()
        const price = data.ripple.usd
        setXrpPrice(price)
      } catch (error) {
        console.error("Error fetching XRP price:", error)
        setXrpPrice(2.30) // Fallback price if fetch fails
      }
    }

    fetchXrpPrice()
    // Update price every 5 minutes
    const priceInterval = setInterval(fetchXrpPrice, 300000)

    return () => clearInterval(priceInterval)
  }, [])

  useEffect(() => {
    // Only show notifications occasionally to avoid spam
    const interval = setInterval(() => {
      if (mockNotifications.length > 0 && Math.random() > 0.7) { // Only 30% chance to show
        setNotifications(prev => {
          const newNotification = mockNotifications[Math.floor(Math.random() * mockNotifications.length)]
          const updated = { ...newNotification, id: Date.now().toString(), timestamp: new Date() }
          return [updated, ...prev].slice(0, 3) // Keep only last 3
        })
      }
    }, 15000) // New notification every 15 seconds (less frequent)

    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    if (notifications.length > 0) {
      const interval = setInterval(() => {
        setCurrentIndex(prev => (prev + 1) % notifications.length)
      }, 4000) // Cycle through notifications every 4 seconds

      return () => clearInterval(interval)
    }
  }, [notifications.length])

  const getIcon = (type: string) => {
    switch (type) {
      case 'winner': return <Trophy className="h-4 w-4 text-yellow-400" />
      case 'prize_pool': return <Coins className="h-4 w-4 text-green-400" />
      case 'new_player': return <Users className="h-4 w-4 text-blue-400" />
      case 'high_score': return <TrendingUp className="h-4 w-4 text-purple-400" />
      default: return <Trophy className="h-4 w-4 text-yellow-400" />
    }
  }

  const getGameEmoji = (game?: string) => {
    switch (game) {
      case '2048': return '🎯'
      case 'Fuse Bird': return '🐦'
      case 'Rock Paper Scissors': return '✂️'
      default: return '🎮'
    }
  }

  const formatTimeAgo = (timestamp: Date) => {
    const seconds = Math.floor((Date.now() - timestamp.getTime()) / 1000)
    if (seconds < 60) return `${seconds}s ago`
    const minutes = Math.floor(seconds / 60)
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    return `${hours}h ago`
  }

  if (!isVisible || notifications.length === 0) return null

  const currentNotification = notifications[currentIndex]

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentNotification.id}
          initial={{ opacity: 0, x: 300, scale: 0.8 }}
          animate={{ opacity: 1, x: 0, scale: 1 }}
          exit={{ opacity: 0, x: 300, scale: 0.8 }}
          transition={{ duration: 0.5, type: "spring", stiffness: 100 }}
          className="bg-gradient-to-r from-gray-900 to-gray-800 text-white rounded-lg shadow-2xl border border-gray-700 p-4 backdrop-blur-sm"
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              {getIcon(currentNotification.type)}
              <Badge 
                variant="secondary" 
                className="bg-white/10 text-white text-xs"
              >
                LIVE
              </Badge>
            </div>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          {/* Content */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              {currentNotification.game && (
                <span className="text-lg">{getGameEmoji(currentNotification.game)}</span>
              )}
              <div className="flex-1">
                {currentNotification.player && (
                  <div className="font-semibold text-sm text-yellow-400">
                    {currentNotification.player}
                  </div>
                )}
                <div className="text-sm text-gray-300">
                  {currentNotification.message}
                </div>
              </div>
            </div>

            {/* No longer showing fake prize amounts */}

            <div className="flex items-center justify-between text-xs text-gray-400">
              <span>{formatTimeAgo(currentNotification.timestamp)}</span>
              <span>{currentIndex + 1} of {notifications.length}</span>
            </div>
          </div>

          {/* Progress bar */}
          <div className="mt-3 w-full bg-gray-700 rounded-full h-1">
            <motion.div
              className="bg-gradient-to-r from-yellow-400 to-orange-500 h-1 rounded-full"
              initial={{ width: "0%" }}
              animate={{ width: "100%" }}
              transition={{ duration: 4, ease: "linear" }}
              key={currentNotification.id}
            />
          </div>

          {/* CTA */}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="w-full mt-3 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-400 hover:to-orange-400 text-black font-semibold py-2 px-4 rounded-lg text-sm transition-all duration-200"
            onClick={() => {
              // Navigate to games
              window.location.href = '/#games'
            }}
          >
            🎮 Play & Win Now
          </motion.button>
        </motion.div>
      </AnimatePresence>

      {/* Minimized indicator when closed */}
      {!isVisible && (
        <motion.button
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 1 }}
          onClick={() => setIsVisible(true)}
          className="bg-gradient-to-r from-yellow-500 to-orange-500 text-black p-3 rounded-full shadow-lg hover:scale-110 transition-transform"
        >
          <Trophy className="h-5 w-5" />
        </motion.button>
      )}
    </div>
  )
}

// Hook to show notifications on specific pages
export function useGamingNotifications(enabled: boolean = true) {
  const [shouldShow, setShouldShow] = useState(false)

  useEffect(() => {
    if (!enabled) return

    // Show notifications after a delay
    const timer = setTimeout(() => {
      setShouldShow(true)
    }, 3000)

    return () => clearTimeout(timer)
  }, [enabled])

  return shouldShow
}
