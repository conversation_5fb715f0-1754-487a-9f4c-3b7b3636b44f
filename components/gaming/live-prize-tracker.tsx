'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { TrendingUp, Trophy, Users } from 'lucide-react'
import { useGaming } from '@/contexts/gaming-context'

interface GamePrizeData {
  gameType: string
  displayName: string
  icon: string
  totalPrizePool: number
  totalPlayers: number
  description: string
}

interface LivePrizeTrackerProps {
  showIndividualGames?: boolean
  showTotalOnly?: boolean
  className?: string
}

export function LivePrizeTracker({
  showIndividualGames = false,
  showTotalOnly = false,
  className = ""
}: LivePrizeTrackerProps) {
  // Add error boundary for gaming context
  let leaderboards: any[] = []
  let fetchLeaderboards: () => void = () => {}

  try {
    const gamingContext = useGaming()
    leaderboards = gamingContext.leaderboards
    fetchLeaderboards = gamingContext.fetchLeaderboards
  } catch (error) {
    console.warn('Gaming context not available, using fallback values')
  }

  const [prizeData, setPrizeData] = useState<GamePrizeData[]>([])
  const [totalPrizePool, setTotalPrizePool] = useState(0)
  const [totalPlayers, setTotalPlayers] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  // Calculate prize pools from leaderboard data
  useEffect(() => {
    const calculatePrizePools = () => {
      const gameTypes = ['2048', 'fuse-bird', 'rock-paper-scissors']
      const gameData: GamePrizeData[] = []
      let grandTotal = 0
      let grandTotalPlayers = 0

      gameTypes.forEach(gameType => {
        // Get all competitive leaderboards for this game (exclude free tier)
        const gameLeaderboards = leaderboards.filter(lb => 
          lb.game_type === gameType && 
          lb.entry_fee_tier !== 'free' &&
          lb.is_active
        )

        // Sum up prize pools and players for this game
        const gamePrizePool = gameLeaderboards.reduce((sum, lb) => sum + lb.prize_pool_xrp, 0)
        const gamePlayers = gameLeaderboards.reduce((sum, lb) => sum + lb.total_entries, 0)

        grandTotal += gamePrizePool
        grandTotalPlayers += gamePlayers

        gameData.push({
          gameType,
          displayName: gameType === '2048' ? '2048' : 
                     gameType === 'fuse-bird' ? 'Fuse Bird' : 
                     'Rock Paper Scissors',
          icon: gameType === '2048' ? '🎯' : 
                gameType === 'fuse-bird' ? '🐦' : '✂️',
          totalPrizePool: gamePrizePool,
          totalPlayers: gamePlayers,
          description: gameType === '2048' ? 'Merge tiles, win XRP' :
                      gameType === 'fuse-bird' ? 'Flappy fun with prizes' :
                      'Classic game, crypto rewards'
        })
      })

      setPrizeData(gameData)
      setTotalPrizePool(grandTotal)
      setTotalPlayers(grandTotalPlayers)
      setIsLoading(false)
    }

    if (leaderboards.length > 0) {
      calculatePrizePools()
    }
  }, [leaderboards])

  // Fetch leaderboards on mount and set up refresh interval
  useEffect(() => {
    fetchLeaderboards()
    
    // Refresh every 30 seconds
    const interval = setInterval(() => {
      fetchLeaderboards()
    }, 30000)

    return () => clearInterval(interval)
  }, [fetchLeaderboards])

  if (showTotalOnly) {
    return (
      <motion.div
        whileHover={{ scale: 1.05 }}
        className={`bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 ${className}`}
      >
        <div className="text-3xl font-bold text-yellow-400 mb-2">
          <motion.span
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
          >
            {isLoading ? '...' : totalPrizePool.toFixed(1)}
          </motion.span>
          <span className="text-lg"> XRP</span>
        </div>
        <div className="text-white/80">Total Prize Pool</div>
        <div className="text-sm text-blue-400 mt-1">
          {isLoading ? 'Loading...' : `${totalPlayers} active players`}
        </div>
      </motion.div>
    )
  }

  if (showIndividualGames) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>
        {prizeData.map((game, index) => (
          <motion.div
            key={game.gameType}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.2 }}
            whileHover={{ scale: 1.05, y: -5 }}
            className="bg-gradient-to-br from-white/20 to-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/30 cursor-pointer group"
          >
            <div className="text-4xl mb-3 group-hover:scale-110 transition-transform">
              {game.icon}
            </div>
            <h3 className="text-xl font-bold mb-2">{game.displayName}</h3>
            <p className="text-white/70 text-sm mb-3">{game.description}</p>
            <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-3 py-1 rounded-full text-sm font-bold">
              {isLoading ? 'Loading...' : 
               game.totalPrizePool > 0 ? 
               `Prize Pool: ${game.totalPrizePool.toFixed(1)} XRP` :
               'No active prizes'
              }
            </div>
            {!isLoading && game.totalPlayers > 0 && (
              <div className="text-xs text-white/60 mt-2">
                {game.totalPlayers} competing
              </div>
            )}
          </motion.div>
        ))}
      </div>
    )
  }

  // Default: show summary stats
  return (
    <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>
      <motion.div
        whileHover={{ scale: 1.05 }}
        className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
      >
        <div className="text-3xl font-bold text-yellow-400 mb-2">
          {isLoading ? '...' : totalPrizePool.toFixed(1)}
          <span className="text-lg"> XRP</span>
        </div>
        <div className="text-white/80">Total Prize Pool</div>
        <div className="text-sm text-blue-400 mt-1">
          {isLoading ? 'Loading...' : 'Live tracking'}
        </div>
      </motion.div>

      <motion.div
        whileHover={{ scale: 1.05 }}
        className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
      >
        <div className="text-3xl font-bold text-green-400 mb-2">
          {isLoading ? '...' : totalPlayers}
        </div>
        <div className="text-white/80">Active Players</div>
        <div className="text-sm text-green-400 mt-1">
          {isLoading ? 'Loading...' : 'Competing now'}
        </div>
      </motion.div>

      <motion.div
        whileHover={{ scale: 1.05 }}
        className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
      >
        <div className="text-3xl font-bold text-purple-400 mb-2">
          {isLoading ? '...' : prizeData.filter(g => g.totalPrizePool > 0).length}
        </div>
        <div className="text-white/80">Active Games</div>
        <div className="text-sm text-purple-400 mt-1">
          {isLoading ? 'Loading...' : 'With prizes'}
        </div>
      </motion.div>
    </div>
  )
}

// Hook for getting live prize data
export function useLivePrizeData() {
  // Add error boundary for gaming context
  let leaderboards: any[] = []
  let fetchLeaderboards: () => void = () => {}

  try {
    const gamingContext = useGaming()
    leaderboards = gamingContext.leaderboards
    fetchLeaderboards = gamingContext.fetchLeaderboards
  } catch (error) {
    console.warn('Gaming context not available, using fallback values')
  }

  const [totalPrizePool, setTotalPrizePool] = useState(0)
  const [totalPlayers, setTotalPlayers] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const calculateTotals = () => {
      const competitiveLeaderboards = leaderboards.filter(lb => 
        lb.entry_fee_tier !== 'free' && lb.is_active
      )

      const total = competitiveLeaderboards.reduce((sum, lb) => sum + lb.prize_pool_xrp, 0)
      const players = competitiveLeaderboards.reduce((sum, lb) => sum + lb.total_entries, 0)

      setTotalPrizePool(total)
      setTotalPlayers(players)
      setIsLoading(false)
    }

    if (leaderboards.length > 0) {
      calculateTotals()
    }
  }, [leaderboards])

  useEffect(() => {
    fetchLeaderboards()
    const interval = setInterval(fetchLeaderboards, 30000)
    return () => clearInterval(interval)
  }, [fetchLeaderboards])

  return { totalPrizePool, totalPlayers, isLoading }
}
