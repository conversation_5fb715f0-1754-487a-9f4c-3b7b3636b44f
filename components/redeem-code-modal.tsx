"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/contexts/auth-context";

interface RedeemCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function RedeemCodeModal({ isOpen, onClose }: RedeemCodeModalProps) {
  const { user } = useAuth();
  const [vipCode, setVipCode] = useState("");
  const [isRedeeming, setIsRedeeming] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [statusMsg, setStatusMsg] = useState("");

  const handleCodeRedeem = async (e) => {
    e.preventDefault();
    setIsRedeeming(true);
    setErrorMsg("");
    setStatusMsg("Redeeming code...");

    try {
      // Check if user is authenticated using auth context
      if (!user) {
        throw new Error("Please sign in first");
      }

      // First, check if the VIP code exists and is valid
      const { data: codeData, error: codeError } = await supabase
        .from('vip_codes')
        .select('*')
        .eq('code', vipCode)
        .eq('is_active', true)
        .gt('remaining_uses', 0)
        .single();

      if (codeError || !codeData) {
        throw new Error("Invalid or already redeemed VIP code. Please check your code and try again.");
      }

      // Get current user ID from auth context
      const userId = user.id;
      
      // Calculate dates
      const now = new Date();
      const oneYearFromNow = new Date(now);
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      
      // Update the user's profile
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          is_card_holder: true,
          membership_start_date: now.toISOString(),
          membership_end_date: oneYearFromNow.toISOString()
        })
        .eq('id', userId);
      
      if (profileError) {
        throw new Error("Failed to update membership status");
      }
      
  

      setVipCode("");
      setStatusMsg("Code redeemed successfully! Redirecting...");
      
      setTimeout(() => {
        window.location.href = "/dashboard";
      }, 2000);

    } catch (error) {
      console.error("Error redeeming code:", error);
      setErrorMsg(error.message);
      setStatusMsg("");
    } finally {
      setIsRedeeming(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Redeem VIP Code</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleCodeRedeem} className="space-y-4">
          <div>
            <Input
              placeholder="Enter your VIP code"
              value={vipCode}
              onChange={(e) => setVipCode(e.target.value)}
              disabled={isRedeeming}
              className="w-full"
            />
          </div>
          
          {errorMsg && (
            <p className="text-red-500 text-sm">{errorMsg}</p>
          )}
          
          {statusMsg && (
            <p className="text-green-500 text-sm">{statusMsg}</p>
          )}
          
          <div className="flex justify-end">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onClose} 
              disabled={isRedeeming}
              className="mr-2"
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!vipCode || isRedeeming}
            >
              {isRedeeming ? "Processing..." : "Redeem Code"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
