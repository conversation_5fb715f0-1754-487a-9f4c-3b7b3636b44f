"use client";

import { motion } from "framer-motion";
import { 
  <PERSON>, 
  TrendingUp, 
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Zap
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface FuseTokenPreviewProps {
  compact?: boolean;
  showCTA?: boolean;
}

export function FuseTokenPreview({ compact = false, showCTA = true }: FuseTokenPreviewProps) {
  const handleLearnMore = () => {
    window.location.href = '/yield-pool#fuse-multiplier';
  };

  if (compact) {
    return (
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/30 rounded-lg p-4"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full">
              <Flame className="h-4 w-4 text-white" />
            </div>
            <div>
              <div className="text-white font-medium text-sm">FUSE Token Burning</div>
              <div className="text-white/70 text-xs">Boost yields up to 3x</div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 text-xs">
              Coming Soon
            </Badge>
            {showCTA && (
              <Button
                size="sm"
                variant="ghost"
                onClick={handleLearnMore}
                className="text-purple-400 hover:text-purple-300 hover:bg-purple-500/10"
              >
                <ArrowRight className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="bg-gradient-to-br from-purple-900/40 to-pink-900/40 border-purple-500/30 backdrop-blur-sm relative overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-pink-500/5 to-purple-500/5 animate-pulse"></div>
        
        <CardHeader className="relative">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <motion.div
                animate={{ 
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{ 
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
              >
                <Flame className="h-5 w-5 text-white" />
              </motion.div>
              Burn FUSE Tokens for Yield Multipliers
            </CardTitle>
            <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 animate-pulse">
              Coming Soon
            </Badge>
          </div>
          <CardDescription className="text-white/80">
            Unlock powerful yield multipliers and exclusive features by burning FUSE tokens
          </CardDescription>
        </CardHeader>
        
        <CardContent className="relative space-y-4">
          {/* Key Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/10 rounded-lg p-4 text-center">
              <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-400 rounded-full flex items-center justify-center mx-auto mb-2">
                <TrendingUp className="h-5 w-5 text-white" />
              </div>
              <div className="text-white font-medium text-sm mb-1">Up to 3x Multiplier</div>
              <div className="text-white/70 text-xs">Boost your yield earnings significantly</div>
            </div>
            
            <div className="bg-white/10 rounded-lg p-4 text-center">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full flex items-center justify-center mx-auto mb-2">
                <Zap className="h-5 w-5 text-white" />
              </div>
              <div className="text-white font-medium text-sm mb-1">Premium Features</div>
              <div className="text-white/70 text-xs">Access exclusive grid bot strategies</div>
            </div>
            
            <div className="bg-white/10 rounded-lg p-4 text-center">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-400 rounded-full flex items-center justify-center mx-auto mb-2">
                <Sparkles className="h-5 w-5 text-white" />
              </div>
              <div className="text-white font-medium text-sm mb-1">VIP Benefits</div>
              <div className="text-white/70 text-xs">Priority support and early access</div>
            </div>
          </div>

          {/* Multiplier Tiers Preview */}
          <div className="bg-white/5 rounded-lg p-4">
            <div className="text-white font-medium mb-3 text-center">Multiplier Tiers</div>
            <div className="grid grid-cols-4 gap-2 text-center">
              <div className="space-y-1">
                <div className="text-orange-400 font-bold text-lg">1.25x</div>
                <div className="text-white/70 text-xs">Bronze</div>
                <div className="text-white/50 text-xs">1K FUSE</div>
              </div>
              <div className="space-y-1">
                <div className="text-gray-300 font-bold text-lg">1.5x</div>
                <div className="text-white/70 text-xs">Silver</div>
                <div className="text-white/50 text-xs">5K FUSE</div>
              </div>
              <div className="space-y-1">
                <div className="text-yellow-400 font-bold text-lg">2.0x</div>
                <div className="text-white/70 text-xs">Gold</div>
                <div className="text-white/50 text-xs">15K FUSE</div>
              </div>
              <div className="space-y-1">
                <div className="text-purple-400 font-bold text-lg">3.0x</div>
                <div className="text-white/70 text-xs">Platinum</div>
                <div className="text-white/50 text-xs">50K FUSE</div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          {showCTA && (
            <div className="text-center space-y-3">
              <p className="text-white/70 text-sm">
                Start accumulating FUSE tokens now to be ready for launch!
              </p>
              <Button
                onClick={handleLearnMore}
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0"
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Learn More About Multipliers
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
