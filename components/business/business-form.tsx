"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { AnimatedCard } from "@/components/animated-card"
import { AnimatedSection } from "@/components/animated-section"
import { Save, X } from "lucide-react"
import { supabase } from "@/lib/supabase"
import { useAuth } from "@/contexts/auth-context"
import { BusinessCombobox } from "./business-combobox"

interface BusinessFormProps {
  initialData?: any
  onSubmit: (data: any) => void
  onCancel: () => void
}

export function BusinessForm({ initialData, onSubmit, onCancel }: BusinessFormProps) {
  const { user } = useAuth()
  const [formData, setFormData] = useState({
    business_name: initialData?.business_name || "",
    website: initialData?.website || "",
    category: initialData?.category || "",
    contact_name: initialData?.contact_name || "",
    contact_email: initialData?.contact_email || "",
    contact_phone: initialData?.contact_phone || "",
    proposed_discount: initialData?.proposed_discount || "",
    logo_url: initialData?.logo_url || "",
    logo_path: initialData?.logo_path || "",
    status: initialData?.status || "pending",
    created_at: initialData?.created_at || new Date().toISOString(),
    updated_at: initialData?.updated_at || new Date().toISOString(),
    loyalty_reward_frequency: initialData?.loyalty_reward_frequency || "monthly",
    user_id: initialData?.user_id || user?.id || "",
    business_address: initialData?.business_address || "",
    referring_business_id: initialData?.referring_business_id || "",
  })

  const [logoFile, setLogoFile] = useState<File | null>(null)

  const handleFileUpload = async (file: File) => {
    if (file.size > 5 * 1024 * 1024) {
      alert("File size exceeds 5MB. Please upload a smaller file.")
      return
    }

    if (!supabase) {
      alert("Unable to upload file. Please try again later.")
      return
    }

    try {
      // Generate a unique filename using timestamp and random string
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;

      const { data, error } = await supabase.storage
        .from("business-logos")
        .upload(fileName, file)

      if (error) {
        console.error("Storage upload error:", error)
        alert("Error uploading file: " + error.message)
        return
      }

      const { data: urlData } = supabase.storage.from("business-logos").getPublicUrl(data.path)
      setFormData((prev) => ({ ...prev, logo_url: urlData.publicUrl }))
    } catch (err) {
      console.error("File upload error:", err)
      alert("Error uploading file. Please try again.")
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setLogoFile(file)
      handleFileUpload(file)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user?.id) {
      alert("You must be logged in to submit a business application")
      return
    }

    if (!supabase) {
      alert("Unable to submit application. Please try again later.")
      return
    }

    try {
      if (initialData) {
        // Update existing business
        onSubmit({
          ...formData,
          user_id: user.id,
          updated_at: new Date().toISOString()
        })
      } else {
        // New business - insert into network_applications
        const { data, error } = await supabase
          .from("network_applications")
          .insert({
            ...formData,
            user_id: user.id,
            status: "pending",
            created_at: new Date().toISOString()
          })
          .select()

        if (error) {
          console.error("Error submitting business application:", error)
          alert("Error submitting business application. Please try again.")
          return
        }

        alert("Congratulations! Your business application has been submitted successfully. Please allow our team 24 hours to review it.")
        onSubmit(data?.[0] || formData)
      }
    } catch (err) {
      console.error("Error in form submission:", err)
      alert("An unexpected error occurred. Please try again.")
    }
  }

  const categories = [
    "Retail",
    "Food & Beverage",
    "Healthcare",
    "Technology",
    "Finance",
    "Education",
    "Entertainment",
    "Hospitality",
    "Manufacturing",
    "Real Estate",
    "Other",
  ]

  return (
    <AnimatedSection>
      <AnimatedCard className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-6">{initialData ? "Edit Business" : "Register Your Business"}</h2>
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="business_name" className="block text-sm font-medium text-gray-700 mb-1">
                Business Name *
              </label>
              <input
                type="text"
                id="business_name"
                name="business_name"
                value={formData.business_name}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              />
            </div>
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Category *
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              >
                <option value="">Select Category</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="mb-6">
            <label htmlFor="referring_business_id" className="block text-sm font-medium text-gray-700 mb-1">
              Referring Business
            </label>
            <BusinessCombobox
              value={formData.referring_business_id}
              onChange={(value) => setFormData(prev => ({ ...prev, referring_business_id: value }))}
            />
            <p className="text-xs text-gray-500 mt-1">
              If you were referred by a business, please select it from the dropdown
            </p>
          </div>

          <div className="mb-6">
            <label htmlFor="business_address" className="block text-sm font-medium text-gray-700 mb-1">
              Business Address *
            </label>
            <input
              type="text"
              id="business_address"
              name="business_address"
              value={formData.business_address}
              onChange={handleChange}
              required
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <label htmlFor="contact_name" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Name *
              </label>
              <input
                type="text"
                id="contact_name"
                name="contact_name"
                value={formData.contact_name}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              />
            </div>
            <div>
              <label htmlFor="contact_phone" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Phone *
              </label>
              <input
                type="tel"
                id="contact_phone"
                name="contact_phone"
                value={formData.contact_phone}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              />
            </div>
            <div>
              <label htmlFor="contact_email" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Email *
              </label>
              <input
                type="email"
                id="contact_email"
                name="contact_email"
                value={formData.contact_email}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1">
                Website
              </label>
              <input
                type="url"
                id="website"
                name="website"
                value={formData.website}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              />
            </div>
            <div>
              <label htmlFor="logo_file" className="block text-sm font-medium text-gray-700 mb-1">
                Upload Logo (Max: 5MB)
              </label>
              <button
                type="button"
                onClick={() => document.getElementById("logo_file")?.click()}
                className="px-4 py-2 bg-[#3A56FF] text-white rounded-md"
              >
                Select File
              </button>
              <input
                type="file"
                id="logo_file"
                name="logo_file"
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
              />
              {logoFile && <p className="text-sm text-gray-500 mt-2">Selected File: {logoFile.name}</p>}
              {formData.logo_url && (
                <div className="mt-2">
                  <img src={formData.logo_url} alt="Logo Preview" className="h-20 w-20 object-cover rounded-md" />
                </div>
              )}
            </div>
            <div>
              <label htmlFor="proposed_discount" className="block text-sm font-medium text-gray-700 mb-1">
                Proposed Discount (%)
              </label>
              <input
                type="number"
                id="proposed_discount"
                name="proposed_discount"
                value={formData.proposed_discount}
                onChange={handleChange}
                min="0"
                max="100"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              />
            </div>
          </div>

          <div className="mb-6">
            <label htmlFor="loyalty_reward_frequency" className="block text-sm font-medium text-gray-700 mb-1">
              Loyalty Reward Frequency
            </label>
            <select
              id="loyalty_reward_frequency"
              name="loyalty_reward_frequency"
              value={formData.loyalty_reward_frequency}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
            >
              <option value="monthly">Monthly - Rewards distributed every month</option>
              <option value="quarterly">Quarterly - Rewards distributed every 3 months</option>
              <option value="annually">Annually - Rewards distributed once per year</option>
            </select>
            <p className="text-xs text-gray-500 mt-1">
              Choose how often you want to distribute loyalty rewards to your customers
            </p>
          </div>

          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center"
            >
              <X className="h-4 w-4 mr-2" /> Cancel
            </button>
            <button type="submit" className="px-4 py-2 bg-[#3A56FF] text-white rounded-md flex items-center">
              <Save className="h-4 w-4 mr-2" /> {initialData ? "Update" : "Register"}
            </button>
          </div>
        </form>
      </AnimatedCard>
    </AnimatedSection>
  )
}
