"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { ChevronLeft, ChevronRight, ExternalLink, Percent, Building2 } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { AnimatedSection } from "@/components/animated-section";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface Business {
  name: string;
  premium_discount: string;
  logo_url: string;
  website: string;
}

export function CyclingBusinesses() {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isPaused, setIsPaused] = useState(false);

  // Fetch businesses from Supabase
  useEffect(() => {
    const fetchBusinesses = async () => {
      if (!supabase) return;

      try {
        const { data, error } = await supabase
          .from("businesses")
          .select("name, premium_discount, logo_url, website")
          .not("premium_discount", "is", null)
          .order("name");

        if (error) {
          console.error("Error fetching businesses:", error);
        } else {
          const processedData: Business[] =
            data?.map((business: any) => ({
              name: business.name,
              premium_discount: business.premium_discount,
              logo_url: business.logo_url || "/placeholder-logo.png",
              website: business.website,
            })) || [];
          setBusinesses(processedData);
          if (processedData.length > 0) {
            setCurrentIndex(Math.floor(Math.random() * processedData.length));
          }
        }
      } catch (error) {
        console.error("Error fetching businesses:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBusinesses();
  }, []);

  // Auto-cycle through businesses
  useEffect(() => {
    if (businesses.length <= 1 || isPaused) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % businesses.length);
    }, 4000); // Change every 4 seconds

    return () => clearInterval(interval);
  }, [businesses.length, isPaused]);

  const goToPrevious = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? businesses.length - 1 : prevIndex - 1
    );
  };

  const goToNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % businesses.length);
  };

  const handleVisitWebsite = (website: string) => {
    if (website) {
      // Ensure the URL has a protocol
      const url = website.startsWith('http') ? website : `https://${website}`;
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  if (isLoading) {
    return (
      <section className="py-12 bg-gradient-to-r from-[#316bff]/5 to-purple-500/5">
        <div className="container mx-auto px-4">
          <div className="flex justify-center items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#316bff]"></div>
          </div>
        </div>
      </section>
    );
  }

  if (businesses.length === 0) {
    return null;
  }

  const currentBusiness = businesses[currentIndex];

  return (
    <section className="py-16 bg-gradient-to-r from-[#316bff]/5 via-purple-500/5 to-pink-500/5 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 bg-[#316bff] rounded-full animate-pulse"></div>
        <div className="absolute top-32 right-20 w-16 h-16 bg-purple-500 rounded-full animate-bounce"></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-pink-500 rounded-full animate-ping"></div>
        <div className="absolute bottom-32 right-1/3 w-14 h-14 bg-yellow-400 rounded-full animate-pulse"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-20 left-1/3 w-6 h-6 bg-gradient-to-r from-[#316bff] to-purple-500 rounded-full"
          animate={{
            y: [0, -20, 0],
            opacity: [0.3, 0.7, 0.3],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute bottom-40 right-1/4 w-4 h-4 bg-gradient-to-r from-pink-500 to-orange-500 rounded-full"
          animate={{
            x: [0, 15, 0],
            opacity: [0.4, 0.8, 0.4],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1,
          }}
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <AnimatedSection>
          <div className="text-center mb-12">
            <Badge className="bg-gradient-to-r from-[#316bff] to-purple-600 text-white mb-4 text-lg px-6 py-2">
              🎯 Exclusive Discounts Available
            </Badge>
            <h2 className="text-3xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-[#316bff] to-purple-600 bg-clip-text text-transparent">
              Discover Amazing Deals
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              VIP members get exclusive discounts at these featured businesses. Click to visit their websites!
            </p>
          </div>
        </AnimatedSection>

        <div 
          className="relative max-w-4xl mx-auto"
          onMouseEnter={() => setIsPaused(true)}
          onMouseLeave={() => setIsPaused(false)}
        >
          <AnimatePresence mode="wait">
            <motion.div
              key={currentIndex}
              initial={{ opacity: 0, scale: 0.8, y: 50 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: -50 }}
              transition={{ duration: 0.6, ease: "easeInOut" }}
              className="bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-100 hover:shadow-3xl transition-all duration-300 hover:scale-[1.02] cursor-pointer"
              onClick={() => handleVisitWebsite(currentBusiness.website)}
            >
              <div className="grid grid-cols-1 lg:grid-cols-2 min-h-[300px]">
                {/* Business Logo/Image Section */}
                <div className="relative bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-8">
                  {currentBusiness.logo_url && currentBusiness.logo_url !== "/placeholder-logo.png" ? (
                    <div className="relative w-48 h-48 rounded-xl overflow-hidden shadow-lg">
                      <Image
                        src={currentBusiness.logo_url}
                        alt={currentBusiness.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-48 h-48 bg-gradient-to-br from-[#316bff]/20 to-purple-500/20 rounded-xl flex items-center justify-center shadow-lg">
                      <Building2 className="h-20 w-20 text-[#316bff]" />
                    </div>
                  )}
                  
                  {/* Discount Badge */}
                  <div className="absolute top-4 right-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-full font-bold text-lg shadow-lg">
                    {currentBusiness.premium_discount}
                  </div>
                </div>

                {/* Business Info Section */}
                <div className="p-8 flex flex-col justify-center">
                  <h3 className="text-3xl font-bold text-gray-900 mb-4">
                    {currentBusiness.name}
                  </h3>
                  
                  <div className="mb-6">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-sm font-medium text-green-600">VIP Discount Available</span>
                    </div>

                    {/* Striking Premium Discount Display */}
                    <div className="bg-gradient-to-r from-[#316bff]/10 to-purple-600/10 rounded-xl p-6 border-2 border-dashed border-[#316bff]/30 mb-4">
                      <div className="text-center">
                        <p className="text-sm text-gray-500 mb-2">EXCLUSIVE VIP OFFER</p>
                        <div className="text-4xl md:text-5xl font-black bg-gradient-to-r from-[#316bff] to-purple-600 bg-clip-text text-transparent mb-2 tracking-tight">
                          {currentBusiness.premium_discount}
                        </div>
                        <p className="text-gray-600 font-medium">Show your FUSE VIP card to redeem</p>
                      </div>
                    </div>
                  </div>

                  <Button
                    onClick={() => handleVisitWebsite(currentBusiness.website)}
                    className="bg-gradient-to-r from-[#316bff] to-purple-600 hover:from-[#2151d3] hover:to-purple-700 text-white font-semibold text-lg px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2 group"
                  >
                    Visit Website
                    <ExternalLink className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Navigation Controls */}
          {businesses.length > 1 && (
            <>
              <button
                onClick={goToPrevious}
                className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg rounded-full p-3 transition-all duration-200 hover:scale-110 z-10"
                aria-label="Previous business"
              >
                <ChevronLeft className="h-6 w-6 text-gray-700" />
              </button>
              
              <button
                onClick={goToNext}
                className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white shadow-lg rounded-full p-3 transition-all duration-200 hover:scale-110 z-10"
                aria-label="Next business"
              >
                <ChevronRight className="h-6 w-6 text-gray-700" />
              </button>
            </>
          )}

          {/* Dots Indicator */}
          {businesses.length > 1 && (
            <div className="flex justify-center mt-8 gap-2">
              {businesses.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-200 ${
                    index === currentIndex
                      ? "bg-[#316bff] scale-125"
                      : "bg-gray-300 hover:bg-gray-400"
                  }`}
                  aria-label={`Go to business ${index + 1}`}
                />
              ))}
            </div>
          )}
        </div>

        {/* Call to Action */}
        <AnimatedSection delay={0.3}>
          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">
              Want access to all these exclusive discounts?
            </p>
            <a href="/register">
              <Button
                size="lg"
                className="bg-gradient-to-r from-[#316bff] to-purple-600 hover:from-[#2151d3] hover:to-purple-700 text-white font-semibold text-lg px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Get Your VIP Card Now! 🎯
              </Button>
            </a>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}
