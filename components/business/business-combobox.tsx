"use client";

import React, { useState, useEffect } from "react";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList, CommandInput } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { supabase } from "@/lib/supabase";

interface Business {
  id: string;
  name: string;
}

interface BusinessComboboxProps {
  value: string;
  onChange: (value: string) => void;
  onNameChange?: (name: string) => void;
}

export function BusinessCombobox({ value, onChange, onNameChange }: BusinessComboboxProps) {
  const [open, setOpen] = useState(false);
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const fetchBusinesses = async (search: string) => {
    setLoading(true);
    try {
      let query = supabase.from("businesses").select("id, name").eq("is_active", true).order("name");

      if (search) {
        query = query.ilike("name", `%${search}%`);
      }

      const { data, error } = await query.limit(100);

      if (error) {
        console.error("Error fetching businesses:", error);
        return;
      }

      setBusinesses(data || []);
    } catch (err) {
      console.error("Error in business fetch:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (isOpen && businesses.length === 0) {
      fetchBusinesses(searchTerm); // Fetch businesses only when the dropdown is opened
    }
  };

  const handleSelect = (currentValue: string) => {
    const selectedBusiness = businesses.find((business) => business.id === currentValue);
    onChange(currentValue === value ? "" : currentValue);
    if (onNameChange && selectedBusiness) {
      onNameChange(selectedBusiness.name);
    }
    setOpen(false);
  };

  const handleInputChange = (inputValue: string) => {
    setSearchTerm(inputValue);
    fetchBusinesses(inputValue); // Fetch businesses based on the search term
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          role="combobox" 
          aria-expanded={open} 
          className="w-full justify-between bg-white border-gray-300 text-gray-700 hover:bg-gray-50"
        >
          {value
            ? businesses.find((business) => business.id === value)?.name || "Select business..."
            : "Select business..."}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0 business-dropdown-content relative z-50">
        <Command className="rounded-lg border-0">
          <CommandInput 
            placeholder="Search businesses..." 
            onValueChange={handleInputChange}
            className="h-10 border-b border-gray-200"
          />
          <div className="bg-white w-full">
            {loading ? (
              <div className="py-6 text-center text-sm text-gray-500 bg-white">
                <div className="flex items-center justify-center">
                  <div className="animate-spin h-5 w-5 border-2 border-gray-300 border-t-blue-600 rounded-full mr-2"></div>
                  Loading businesses...
                </div>
              </div>
            ) : (
              <CommandList className="max-h-[300px] overflow-y-auto border-t border-gray-200">
                <CommandEmpty>
                  <div className="py-6 text-center text-sm text-gray-500 bg-white">No businesses found.</div>
                </CommandEmpty>
                <CommandGroup>
                  {businesses.map((business) => (
                    <CommandItem 
                      key={business.id} 
                      value={business.id} 
                      onSelect={() => handleSelect(business.id)}
                      className={cn(
                        "business-dropdown-item cmd-item px-4 py-2 text-sm text-gray-800 hover:bg-blue-100 hover:text-black",
                        value === business.id && "bg-blue-600 text-white"
                      )}
                    >
                      <Check className={cn(
                        "mr-2 h-4 w-4 flex-shrink-0", 
                        value === business.id ? "text-white" : "opacity-0"
                      )} />
                      <span className="truncate block w-full">{business.name}</span>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            )}
          </div>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export function BusinessForm({ onSubmit, onCancel }: { onSubmit: (formData: any) => void; onCancel: () => void }) {
  const [formData, setFormData] = useState({
    businessName: "",
    category: "",
    contactName: "",
    contactEmail: "",
    website: "",
    proposedDiscount: "",
    logoUrl: "",
  });
  const [uploading, setUploading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!supabase) {
      alert("Unable to upload file. Please try again later.");
      return;
    }

    setUploading(true);

    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const { data, error } = await supabase.storage.from("business-logos").upload(fileName, file);

      if (error) {
        console.error("Error uploading file:", error);
        setUploading(false);
        return;
      }

      const { data: urlData } = supabase.storage.from("business-logos").getPublicUrl(data.path);
      setFormData((prev) => ({ ...prev, logoUrl: urlData.publicUrl }));
    } catch (err) {
      console.error("File upload error:", err);
    } finally {
      setUploading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="businessName" className="block text-sm font-medium text-gray-700">
          Business Name
        </label>
        <input
          type="text"
          id="businessName"
          name="businessName"
          value={formData.businessName}
          onChange={handleInputChange}
          required
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>

      {/* Other form fields... */}

      <div>
        <label htmlFor="logoUpload" className="block text-sm font-medium text-gray-700">
          Upload Logo
        </label>
        <input
          type="file"
          id="logoUpload"
          accept="image/*"
          onChange={handleFileUpload}
          className="mt-1 block w-full text-sm text-gray-500"
        />
        {uploading && <p className="text-sm text-blue-500 mt-2">Uploading...</p>}
        {formData.logoUrl && (
          <div className="mt-2">
            <img src={formData.logoUrl} alt="Logo Preview" className="h-20 w-20 object-cover rounded-md" />
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          disabled={uploading}
        >
          Submit
        </button>
      </div>
    </form>
  );
}
