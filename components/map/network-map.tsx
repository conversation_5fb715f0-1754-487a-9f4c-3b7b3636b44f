"use client"

import { useEffect, useRef } from "react"
import mapboxgl from "mapbox-gl"

// Set your Mapbox Access Token
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN!;

interface Business {
  id: string;
  name: string;
  latitude?: number;
  longitude?: number;
  logo_url?: string;
}

export function NetworkMap({ businesses }: { businesses: Business[] }) {
  const mapContainerRef = useRef<HTMLDivElement>(null)
  const mapRef = useRef<mapboxgl.Map | null>(null)

  useEffect(() => {
    if (!mapContainerRef.current) return;

    mapRef.current = new mapboxgl.Map({
      container: mapContainerRef.current,
      style: "mapbox://styles/mapbox/streets-v11", // Standard style
      center: [-98.5795, 39.8283], // Center of the USA
      zoom: 3.5,
    });

    // Add zoom and rotation controls to the map
    mapRef.current.addControl(new mapboxgl.NavigationControl(), "top-right");

    return () => {
      mapRef.current?.remove();
    };
  }, []);

  useEffect(() => {
    if (!mapRef.current || !businesses.length) return;

    // Clear existing markers if needed (future optimization)

    businesses.forEach((business) => {
      if (!business.latitude || !business.longitude) return;

      const markerEl = document.createElement("div");
      markerEl.className = "business-marker";
      markerEl.style.backgroundImage = `url(${business.logo_url || "/placeholder-logo.png"})`;
      markerEl.style.width = "40px";
      markerEl.style.height = "40px";
      markerEl.style.backgroundSize = "cover";
      markerEl.style.backgroundPosition = "center";
      markerEl.style.backgroundRepeat = "no-repeat";
      markerEl.style.borderRadius = "50%";
      markerEl.style.border = "2px solid white";
      markerEl.style.boxShadow = "0 2px 6px rgba(0,0,0,0.3)";
      markerEl.style.cursor = "pointer";

      new mapboxgl.Marker(markerEl)
        .setLngLat([business.longitude, business.latitude])
        .setPopup(
          new mapboxgl.Popup({ offset: 25 }).setHTML(`
            <div style="text-align: center;">
              <strong>${business.name}</strong>
            </div>
          `)
        )
        .addTo(mapRef.current!);
    });
  }, [businesses]);

  return (
    <div
      ref={mapContainerRef}
      className="w-full h-[600px] relative rounded-lg overflow-hidden"
    />
  );
}