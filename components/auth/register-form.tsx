"use client";

import { useState, useRef } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import Link from "next/link"
import { motion } from "framer-motion"
import { AlertCircle, CheckCircle, Loader2, Building2, ChevronDown, Upload, X } from "lucide-react"
import { supabase } from "@/lib/supabase"
import { BusinessCombobox } from "@/components/business/business-combobox"

export function RegisterForm() {
  const { refreshSession } = useAuth()
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [phone, setPhone] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isBusinessApplicant, setIsBusinessApplicant] = useState(false);
  const [businessExpanded, setBusinessExpanded] = useState(false);
  const [businessName, setBusinessName] = useState("");
  const [website, setWebsite] = useState("");
  const [category, setCategory] = useState("");
  const [contactName, setContactName] = useState("");
  const [contactEmail, setContactEmail] = useState("");
  const [proposedDiscount, setProposedDiscount] = useState("");
  const [loyaltyRewardFrequency, setLoyaltyRewardFrequency] = useState("monthly");
  const [logoUrl, setLogoUrl] = useState("");
  const [referringBusinessId, setReferringBusinessId] = useState("");
  const [referringBusinessName, setReferringBusinessName] = useState("");
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const categories = ["Retail", "Food & Beverage", "Healthcare", "Technology", "Finance", "Education", "Entertainment", "Hospitality", "Beauty & Personal Care", "Real Estate", "Other"];

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        setError("Logo file size must be less than 5MB");
        return;
      }
      setLogoFile(file);
      handleFileUpload(file);
    }
  };

  const handleFileUpload = async (file: File) => {
    if (!supabase) {
      setError("Unable to upload file. Please try again later.");
      return;
    }

    setIsUploading(true);
    setError(null);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `${fileName}`;
      const { error: uploadError } = await supabase.storage.from('business-logos').upload(filePath, file);
      if (uploadError) throw new Error(uploadError.message);
      const { data } = supabase.storage.from('business-logos').getPublicUrl(filePath);
      setLogoUrl(data.publicUrl);
    } catch (err) {
      console.error('Error uploading logo:', err);
      setError(`Error uploading logo: ${err.message}`);
    } finally {
      setIsUploading(false);
    }
  };

  const clearSelectedFile = () => {
    setLogoFile(null);
    setLogoUrl("");
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate form
    if (!email || !password || !confirmPassword || !firstName || !lastName) {
      setError("All required fields must be filled")
      return
    }
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }
    if (password.length < 6) {
      setError("Password must be at least 6 characters");
      return;
    }

    // Validate business fields if applying as business
    if (isBusinessApplicant) {
      if (!businessName || !category || !contactName || !contactEmail || !proposedDiscount) {
        setError("All required business fields must be filled")
        return
      }
    }

    setIsLoading(true);
    try {
      // Get the current session
      const { data: sessionData } = await supabase.auth.getSession();
      
      // Register the user directly with Supabase
      const { data, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/dashboard`,
          data: {
            first_name: firstName,
            last_name: lastName,
            phone: phone,
            is_business_applicant: isBusinessApplicant,
            profile_id: crypto.randomUUID(),
            confirmed_at: new Date().toISOString(),
            // Additional business metadata if applicable
            ...(isBusinessApplicant && {
              business_name: businessName,
              business_website: website,
              business_category: category,
              business_contact_name: contactName,
              business_contact_email: contactEmail,
              business_proposed_discount: proposedDiscount,
              business_logo_url: logoUrl,
              referring_business_id: referringBusinessId
            })
          }
        }
      });

      if (signUpError) {
        setError(signUpError.message || "Failed to register. Please try again.")
        setIsLoading(false)
        return
      }

      const userId = data?.user?.id

      if (!userId) {
        setError("User registration successful but user ID not found. Please try logging in.")
        setIsLoading(false)
        return
      }

      // Sign in the user immediately after signup
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      
      if (signInError) {
        setError(signInError.message || "Account created but failed to sign in. Please try logging in.")
        setIsLoading(false)
        return
      }

      // Create profile
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .insert({
          id: userId,
          first_name: firstName,
          last_name: lastName,
          phone: phone,
          is_card_holder: false,
          created_at: new Date().toISOString(),
          user_email: email,
          is_business_applicant: isBusinessApplicant,
          xrp_wallet_address: "",
          membership_start_date: null,
          membership_end_date: null,
          card_tier: null,
          referring_business_id: referringBusinessId,
        })
        .select()
        .single();

      // Generate QR code for the new user
      try {
        const qrResponse = await fetch('/api/generate-qr-codes', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: userId,
            forceRegenerate: false
          }),
        });

        if (!qrResponse.ok) {
          console.warn('Failed to generate QR code during registration');
        } else {
          console.log('QR code generated successfully for new user');
        }
      } catch (qrError) {
        console.warn('Error generating QR code during registration:', qrError);
        // Don't fail registration if QR code generation fails
      }

      // Set success first - we consider the registration successful even if profile creation has issues
      // This is because the auth user was created successfully
      if (!isBusinessApplicant) {
        // For normal users, set success and redirect to dashboard
        setSuccess(true)

        // Refresh the auth context to ensure proper synchronization
        try {
          // Use the auth context's refreshSession to sync the new user state
          await refreshSession()

          // Store user info for session recovery
          localStorage.setItem('user_id', userId)
          localStorage.setItem('user_email', email)

          // Immediate redirect to dashboard - no delay needed
          router.push("/dashboard")
        } catch (refreshError) {
          console.error("Error refreshing session after registration:", refreshError)
          // Even if refresh fails, still redirect - the middleware will handle auth
          router.push("/dashboard")
        }

        setIsLoading(false)
        return
      }

      // Only show profile error if it exists and we're still processing
      if (profileError) {
        console.error("Error creating profile:", profileError)
        setError("Error creating user profile. Please contact support.")
        setIsLoading(false)
        return
      }

      // If business applicant, submit to network_applications table
      if (isBusinessApplicant) {
        const { data: applicationData, error: applicationError } = await supabase
          .from("network_applications")
          .insert({
            user_id: userId,
            business_name: businessName,
            website: website,
            category: category,
            contact_name: contactName,
            contact_email: contactEmail,
            proposed_discount: proposedDiscount,
            loyalty_reward_frequency: loyaltyRewardFrequency,
            logo_url: logoUrl,
            referring_business_id: referringBusinessId,
          status: "pending", // All applications start as pending
          created_at: new Date().toISOString(),
        })
        .select()
        .single();
        if (applicationError) {
          console.error("Error submitting application:", applicationError)
          setError("Error submitting business application. Please contact support.")
          setIsLoading(false)
          return
        }
      }

      setSuccess(true)

      // Refresh the auth context to ensure proper synchronization
      try {
        // Use the auth context's refreshSession to sync the new user state
        await refreshSession()

        // Store user info for session recovery
        localStorage.setItem('user_id', userId)
        localStorage.setItem('user_email', email)

        // Immediate redirect to dashboard - no delay needed
        router.push("/dashboard")
      } catch (refreshError) {
        console.error("Error refreshing session after registration:", refreshError)
        // Even if refresh fails, still redirect - the middleware will handle auth
        router.push("/dashboard")
      }

      setIsLoading(false)
    } catch (err) {
      console.error("Registration error:", err);
      setError("An unexpected error occurred. Please try again.");
      setIsLoading(false);
    }
  }

  return (
    <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-8">
        {success ? (
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome to FUSE.VIP!</h2>
            <p className="text-gray-600 mb-4">
              Your account has been created successfully. You're being redirected to your dashboard where you can explore features and upgrade to VIP.
            </p>
            <div className="flex items-center justify-center">
              <Loader2 className="animate-spin h-5 w-5 mr-2 text-[#3A56FF]" />
              <span className="text-sm text-gray-500">Taking you to your dashboard...</span>
            </div>
            <div className="mt-2 flex justify-center">
              <Link
                href="/dashboard"
                className="inline-block bg-[#3A56FF] text-white px-4 py-2 rounded-md font-medium hover:bg-[#2A46EF] transition-colors"
              >
                Go to Dashboard
              </Link>
            </div>
          </motion.div>
        ) : (
          <>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Create an Account</h2>

            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-start"
              >
                <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
                <span>{error}</span>
              </motion.div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="space-y-6">
                {/* Personal Information Section */}
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                        First Name *
                      </label>
                      <input
                        id="firstName"
                        type="text"
                        value={firstName}
                        onChange={(e) => setFirstName(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                        Last Name *
                      </label>
                      <input
                        id="lastName"
                        type="text"
                        value={lastName}
                        onChange={(e) => setLastName(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                        required
                      />
                    </div>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address *
                    </label>
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number
                    </label>
                    <input
                      id="phone"
                      type="tel"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                        Password *
                      </label>
                      <input
                        id="password"
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                        placeholder="••••••••"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                        Confirm Password *
                      </label>
                      <input
                        id="confirmPassword"
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                        placeholder="••••••••"
                        required
                      />
                    </div>
                  </div>
                </div>
                <div className="border-t border-gray-200 pt-4">
                          <h4 className="text-md font-medium text-gray-700 mb-2">Referring Business</h4>
                          <div>
                            <label
                              htmlFor="referringBusinessId"
                              className="block text-sm font-medium text-gray-700 mb-1"
                            >
                              Select Referring Business (Optional)
                            </label>
                            <BusinessCombobox
                              value={referringBusinessId}
                              onChange={setReferringBusinessId}
                              onNameChange={setReferringBusinessName}
                            />
                            <p className="mt-1 text-xs text-gray-500">
                              If you were referred by a business, please select it from the dropdown
                            </p>
                          </div>
                        </div>

               

                {/* Business Information Section - Conditionally Rendered */}
                {isBusinessApplicant && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="bg-gray-50 p-4 rounded-md"
                  >
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium text-gray-900">Business Information</h3>
                      <button
                        type="button"
                        onClick={() => setBusinessExpanded(!businessExpanded)}
                        className="text-[#3A56FF] flex items-center text-sm"
                      >
                        {businessExpanded ? "Collapse" : "Expand"}
                        <ChevronDown
                          className={`ml-1 h-4 w-4 transition-transform ${businessExpanded ? "rotate-180" : ""}`}
                        />
                      </button>
                    </div>

                    {businessExpanded && (
                      <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label htmlFor="businessName" className="block text-sm font-medium text-gray-700 mb-1">
                              Business Name *
                            </label>
                            <input
                              id="businessName"
                              type="text"
                              value={businessName}
                              onChange={(e) => setBusinessName(e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                              required={isBusinessApplicant}
                            />
                          </div>
                          <div>
                            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                              Category *
                            </label>
                            <select
                              id="category"
                              value={category}
                              onChange={(e) => setCategory(e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                              required={isBusinessApplicant}
                            >
                              <option value="">Select Category</option>
                              {categories.map((cat) => (
                                <option key={cat} value={cat}>
                                  {cat}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label htmlFor="contactName" className="block text-sm font-medium text-gray-700 mb-1">
                              Contact Name *
                            </label>
                            <input
                              id="contactName"
                              type="text"
                              value={contactName}
                              onChange={(e) => setContactName(e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                              required={isBusinessApplicant}
                            />
                          </div>
                          <div>
                            <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700 mb-1">
                              Contact Email *
                            </label>
                            <input
                              id="contactEmail"
                              type="email"
                              value={contactEmail}
                              onChange={(e) => setContactEmail(e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                              required={isBusinessApplicant}
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1">
                              Website
                            </label>
                            <input
                              id="website"
                              type="url"
                              value={website}
                              onChange={(e) => setWebsite(e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label htmlFor="proposedDiscount" className="block text-sm font-medium text-gray-700 mb-1">
                              Proposed Discount (%) *
                            </label>
                            <input
                              id="proposedDiscount"
                              type="number"
                              min="0"
                              max="100"
                              value={proposedDiscount}
                              onChange={(e) => setProposedDiscount(e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                              required={isBusinessApplicant}
                            />
                          </div>
                        </div>

                        <div>
                          <label htmlFor="loyaltyRewardFrequency" className="block text-sm font-medium text-gray-700 mb-1">
                            Loyalty Reward Frequency
                          </label>
                          <select
                            id="loyaltyRewardFrequency"
                            value={loyaltyRewardFrequency}
                            onChange={(e) => setLoyaltyRewardFrequency(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                          >
                            <option value="monthly">Monthly - Rewards distributed every month</option>
                            <option value="quarterly">Quarterly - Rewards distributed every 3 months</option>
                            <option value="annually">Annually - Rewards distributed once per year</option>
                          </select>
                          <p className="mt-1 text-xs text-gray-500">
                            Choose how often you want to distribute loyalty rewards to your customers
                          </p>
                        </div>

                        <div>
                          <label htmlFor="logoFile" className="block text-sm font-medium text-gray-700 mb-1">
                            Business Logo
                          </label>
                          <div className="mt-1 flex items-center">
                            <button
                              type="button"
                              onClick={() => fileInputRef.current?.click()}
                              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#3A56FF]"
                              disabled={isUploading}
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              {isUploading ? "Uploading..." : "Upload Logo"}
                            </button>
                            <input
                              ref={fileInputRef}
                              id="logoFile"
                              name="logoFile"
                              type="file"
                              accept="image/*"
                              onChange={handleFileSelect}
                              className="sr-only"
                            />
                            {logoFile && (
                              <div className="ml-3 flex items-center">
                                <span className="text-sm text-gray-500">{logoFile.name}</span>
                                <button
                                  type="button"
                                  onClick={clearSelectedFile}
                                  className="ml-2 text-gray-400 hover:text-gray-500"
                                >
                                  <X className="h-4 w-4" />
                                </button>
                              </div>
                            )}
                          </div>
                          {logoUrl && (
                            <div className="mt-2">
                              <div className="relative w-20 h-20 overflow-hidden rounded-md border border-gray-200">
                                <img src={logoUrl} alt="Logo preview" className="w-full h-full object-cover" />
                              </div>
                            </div>
                          )}
                          <p className="mt-1 text-xs text-gray-500">
                            Upload a logo for your business (max 5MB). Recommended size: 400x400px.
                          </p>
                        </div>

                        <div className="border-t border-gray-200 pt-4">
                          <h4 className="text-md font-medium text-gray-700 mb-2">Referring Business</h4>
                          <div>
                            <label
                              htmlFor="referringBusinessId"
                              className="block text-sm font-medium text-gray-700 mb-1"
                            >
                              Select Referring Business (Optional)
                            </label>
                            <BusinessCombobox
                              value={referringBusinessId}
                              onChange={setReferringBusinessId}
                              onNameChange={setReferringBusinessName}
                            />
                            <p className="mt-1 text-xs text-gray-500">
                              If you were referred by a business, please select it from the dropdown
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </motion.div>
                )}
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full mt-6 bg-[#3A56FF] text-white py-2 px-4 rounded-md font-medium hover:bg-[#2A46EF] transition-colors disabled:opacity-70 disabled:cursor-not-allowed flex justify-center items-center"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="animate-spin h-5 w-5 mr-2" />
                    Creating Account...
                  </>
                ) : (
                  "Create Account"
                )}
              </button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Already have an account?{" "}
                <Link href="/login" className="text-[#3A56FF] font-medium hover:underline">
                  Sign in
                </Link>
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
