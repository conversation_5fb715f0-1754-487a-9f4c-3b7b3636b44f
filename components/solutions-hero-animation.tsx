"use client"

import { useEffect, useRef } from "react"

export function SolutionsHeroAnimation() {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Create script element for dotlottie-player
    const script = document.createElement("script")
    script.src = "https://unpkg.com/@dotlottie/player-component@2.7.12/dist/dotlottie-player.mjs"
    script.type = "module"
    document.head.appendChild(script)

    // Create the dotlottie-player element
    const createPlayer = () => {
      if (containerRef.current) {
        // Clear any existing content
        containerRef.current.innerHTML = ""

        // Create the player element
        const player = document.createElement("dotlottie-player")
        player.setAttribute("src", "https://lottie.host/db6ab33d-1c32-44c5-afcf-30ad216800e7/pGP2vYe4rq.lottie")
        player.setAttribute("background", "transparent")
        player.setAttribute("speed", "1")
        player.setAttribute("loop", "")
        player.setAttribute("autoplay", "")
        player.style.width = "100%"
        player.style.height = "300px"
        player.style.maxWidth = "600px"
        player.style.margin = "0 auto"

        // Append the player to the container
        containerRef.current.appendChild(player)
      }
    }

    // Wait for script to load before creating player
    script.onload = createPlayer

    // If script is already loaded, create player immediately
    if (
      document.querySelector(
        'script[src="https://unpkg.com/@dotlottie/player-component@2.7.12/dist/dotlottie-player.mjs"]',
      )
    ) {
      createPlayer()
    }

    // Cleanup function
    return () => {
      // Only remove the script if we added it
      const existingScript = document.querySelector(
        'script[src="https://unpkg.com/@dotlottie/player-component@2.7.12/dist/dotlottie-player.mjs"]',
      )
      if (existingScript && script === existingScript) {
        document.head.removeChild(script)
      }
    }
  }, [])

  return <div ref={containerRef} className="flex justify-center my-8"></div>
}
