"use client"

import { ReactNode } from "react"

interface PublicRouteProps {
  children: ReactNode
}

/**
 * PublicRoute component ensures that pages wrapped with it
 * are always accessible to non-authenticated users.
 * 
 * This component serves as documentation and a safeguard
 * to ensure public pages remain public.
 */
export function PublicRoute({ children }: PublicRouteProps) {
  // This component doesn't perform any authentication checks
  // It's purely for documentation and ensuring public access
  return <>{children}</>
}

/**
 * Hook to check if current route should be public
 * This can be used by components to conditionally render
 * authentication-related UI
 */
export function useIsPublicRoute() {
  if (typeof window === 'undefined') return true
  
  const pathname = window.location.pathname
  
  const publicRoutes = [
    '/',
    '/login',
    '/register',
    '/reset-password',
    '/about',
    '/contact',
    '/about-us',
    '/solutions',
    '/industry',
    '/fuse',
    '/upgrade',
    '/reviews',
    '/resources',
    '/book-call',
    '/register-business',
    '/privacy',
    '/terms',
    '/yield-pool',
    '/wecare4you',
  ]
  
  return publicRoutes.some(route => pathname === route || pathname.startsWith(route))
}
