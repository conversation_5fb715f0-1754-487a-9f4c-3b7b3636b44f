"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Wallet } from "lucide-react"
import { useWallet } from "@/hooks/use-wallet"
import { WalletModal } from "./wallet-modal"

export function WalletConnectButton() {
  const { isConnected, isConnecting, connect, disconnect, walletAddress } = useWallet()
  const [isHovering, setIsHovering] = useState(false)
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false)

  const handleClick = async () => {
    if (isConnected) {
      disconnect()
    } else {
      setIsWalletModalOpen(true) // Open the WalletModal
    }
  }

  return (
    <>
      <motion.button
        className={`flex items-center space-x-1 ${
          isConnected ? "bg-green-500" : isConnecting ? "bg-gray-500" : "bg-[#3A56FF]"
        } text-white px-3 py-1.5 rounded-md text-sm font-medium`}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
        onClick={handleClick}
        disabled={isConnecting}
      >
        <Wallet className="h-4 w-4 mr-1" />
        <span>
          {isConnecting
            ? "Connecting..."
            : isConnected
              ? isHovering
                ? "Disconnect"
                : `${walletAddress.substring(0, 4)}...${walletAddress.substring(walletAddress.length - 4)}`
              : "Connect Wallet"}
        </span>
      </motion.button>

      {/* Wallet Modal */}
      <WalletModal
        isOpen={isWalletModalOpen}
        onClose={() => setIsWalletModalOpen(false)}
      />
    </>
  )
}
