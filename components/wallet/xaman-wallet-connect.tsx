'use client';

import { useEffect, useState } from 'react';
import { useWallet } from '@/hooks/use-wallet';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/auth-context';

interface XamanWalletConnectProps {
  onConnected?: () => void;
  showCard?: boolean;
}

export function XamanWalletConnect({
  onConnected,
  showCard = true,
}: XamanWalletConnectProps) {
  const { user } = useAuth();
  const {
    isConnected,
    walletAddress,
    connect,
    disconnect,
    accountInfo,
    fetchAccountInfo,
  } = useWallet();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isConnected && walletAddress) {
      fetchAccountInfo();

      if (user?.id) {
        updateProfile(walletAddress);
      }
    }
  }, [isConnected, walletAddress, fetchAccountInfo, user?.id]);

  const updateProfile = async (address: string) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ xrp_wallet_address: address })
        .eq('id', user.id);

      if (error) {
        console.error('Error updating profile:', error);
      }
    } catch (err) {
      console.error('Unexpected error updating profile:', err);
    }
  };

  const handleConnect = async () => {
    setIsLoading(true);
    try {
      const success = await connect();
      if (success && onConnected) {
        onConnected();
      }
    } catch (error) {
      console.error('Failed to connect wallet:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnect = async () => {
    setIsLoading(true);
    try {
      await disconnect();
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const content = (
    <>
      {isConnected ? (
        <div className="space-y-4">
          <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-md">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Connected Address
            </p>
            <p className="font-mono text-sm break-all">{walletAddress}</p>
          </div>

          {accountInfo && (
            <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-md">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Fuse Balance
              </p>
              <p className="text-xl font-bold">
                {(parseInt(accountInfo.Balance) / 1000000).toFixed(2)} Fuse
              </p>
            </div>
          )}

          <Button
            variant="destructive"
            className="w-full"
            onClick={handleDisconnect}
            disabled={isLoading}
          >
            {isLoading ? 'Disconnecting...' : 'Disconnect Wallet'}
          </Button>
        </div>
      ) : (
        <Button className="w-full" onClick={handleConnect} disabled={isLoading}>
          {isLoading ? 'Connecting...' : 'Connect Xaman Wallet'}
        </Button>
      )}
    </>
  );

  if (!showCard) return content;

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Xaman Wallet</CardTitle>
        <CardDescription>
          Connect your Xaman wallet to manage your Fuse tokens.
        </CardDescription>
      </CardHeader>
      <CardContent>{content}</CardContent>
    </Card>
  );
}
