'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function WalletDebug() {
  const [apiKey, setApiKey] = useState<string>('');
  const [sdkLoaded, setSdkLoaded] = useState<boolean>(false);
  
  useEffect(() => {
    // Get the API key from environment variables
    const key = process.env.NEXT_PUBLIC_XAMAN_API_KEY;
    
    setApiKey(key ? `${key.substring(0, 8)}...` : 'Not found'); // Truncate the key for display purposes
    
    // Check if the SDK is loaded and has the "on" method
    const checkSdk = setInterval(() => {
      if (typeof window !== 'undefined' && window.xumm && window.xumm.on) {
        setSdkLoaded(true);
        clearInterval(checkSdk);
      }
    }, 500);
    
    return () => clearInterval(checkSdk);
  }, []);
  
  return (
    <Card className="w-full max-w-md mx-auto mt-8">
      <CardHeader>
        <CardTitle>Wallet Debug Info</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="font-medium">API Key:</span>
            <span>{apiKey}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">SDK Loaded:</span>
            <span>{sdkLoaded ? '✅' : '❌'}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">Environment:</span>
            <span>{process.env.NODE_ENV}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}