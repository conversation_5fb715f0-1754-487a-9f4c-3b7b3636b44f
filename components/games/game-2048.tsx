'use client'

import { useEffect, useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from "@/components/ui/button"
import { ChevronUp, ChevronDown, ChevronLeft, ChevronRight, Trophy, Target, Zap } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { FuseCompetitiveModal } from './fuse-competitive-modal'
import { GameLeaderboard } from './game-leaderboard'
import { useFuseGaming } from '@/contexts/fuse-gaming-context'

const GRID_SIZE = 4
const CELL_SIZE = 6 // in rem
const CELL_GAP = 0.5 // in rem

type Tile = {
  value: number
  id: string
  mergedFrom?: Tile[]
  justMerged?: boolean
  isNew?: boolean
  row: number
  col: number
}

export function Game2048() {
  const [board, setBoard] = useState<Tile[]>([])
  const [score, setScore] = useState(0)
  const [bestScore, setBestScore] = useState(0)
  const [isGameOver, setIsGameOver] = useState(false)
  const gameContainerRef = useRef<HTMLDivElement>(null)
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null)
  const [touchEnd, setTouchEnd] = useState<{ x: number; y: number } | null>(null)

  // FUSE competitive gaming state
  const [showFuseModal, setShowFuseModal] = useState(false)
  const [showLeaderboard, setShowLeaderboard] = useState(false)
  const [isCompetitiveMode, setIsCompetitiveMode] = useState(false)
  const [isFuseMode, setIsFuseMode] = useState(false)
  const [competitiveSessionId, setCompetitiveSessionId] = useState<string | null>(null)
  const [entryFee, setEntryFee] = useState(0)
  const [gameStartTime, setGameStartTime] = useState<number | null>(null)
  const [moveCount, setMoveCount] = useState(0)

  const { submitFuseGameScore } = useFuseGaming()



  useEffect(() => {
    initializeGame()
    const storedBestScore = localStorage.getItem('bestScore')
    if (storedBestScore) setBestScore(parseInt(storedBestScore))
    
    if (gameContainerRef.current) {
      gameContainerRef.current.focus()
    }
  }, [])

  useEffect(() => {
    if (score > bestScore) {
      setBestScore(score)
      localStorage.setItem('bestScore', score.toString())
    }
  }, [score, bestScore])

  const initializeGame = () => {
    const newBoard: Tile[] = []
    addNewTile(newBoard)
    addNewTile(newBoard)
    setBoard(newBoard)
    setScore(0)
    setIsGameOver(false)
    setMoveCount(0)
    setGameStartTime(Date.now())
  }

  const startFuseCompetitiveGame = (sessionId: string, fee: number) => {
    setCompetitiveSessionId(sessionId)
    setEntryFee(fee)
    setIsCompetitiveMode(true)
    setIsFuseMode(true)
    initializeGame()
  }

  const addNewTile = (board: Tile[]) => {
    const emptyTiles = []
    for (let row = 0; row < GRID_SIZE; row++) {
      for (let col = 0; col < GRID_SIZE; col++) {
        if (!board.some(tile => tile.row === row && tile.col === col)) {
          emptyTiles.push({ row, col })
        }
      }
    }
    if (emptyTiles.length > 0) {
      const { row, col } = emptyTiles[Math.floor(Math.random() * emptyTiles.length)]
      board.push({
        value: Math.random() < 0.9 ? 2 : 4,
        id: `${row}-${col}-${Date.now()}`,
        row,
        col,
        isNew: true
      })
    }
  }

  const move = (direction: 'up' | 'down' | 'left' | 'right') => {
    if (isGameOver) return

    let newBoard = board.map(tile => ({ ...tile, justMerged: false, isNew: false }))
    let changed = false
    let newScore = score

    // Track moves in competitive mode
    if (isCompetitiveMode) {
      setMoveCount(prev => prev + 1)
    }

    const sortedTiles = [...newBoard].sort((a, b) => {
      if (direction === 'up' || direction === 'down') {
        return direction === 'up' ? a.row - b.row : b.row - a.row
      } else {
        return direction === 'left' ? a.col - b.col : b.col - a.col
      }
    })

    for (const tile of sortedTiles) {
      let { row, col } = tile
      let newRow = row
      let newCol = col

      while (true) {
        newRow += direction === 'up' ? -1 : direction === 'down' ? 1 : 0
        newCol += direction === 'left' ? -1 : direction === 'right' ? 1 : 0

        if (newRow < 0 || newRow >= GRID_SIZE || newCol < 0 || newCol >= GRID_SIZE) {
          newRow -= direction === 'up' ? -1 : direction === 'down' ? 1 : 0
          newCol -= direction === 'left' ? -1 : direction === 'right' ? 1 : 0
          break
        }

        const targetTile = newBoard.find(t => t.row === newRow && t.col === newCol)
        if (targetTile) {
          if (targetTile.value === tile.value && !targetTile.justMerged) {
            newBoard = newBoard.filter(t => t !== targetTile && t !== tile)
            newBoard.push({
              value: tile.value * 2,
              id: tile.id,
              row: newRow,
              col: newCol,
              justMerged: true
            })
            newScore += tile.value * 2
            changed = true
          } else {
            newRow -= direction === 'up' ? -1 : direction === 'down' ? 1 : 0
            newCol -= direction === 'left' ? -1 : direction === 'right' ? 1 : 0
          }
          break
        }
      }

      if (newRow !== row || newCol !== col) {
        changed = true
        tile.row = newRow
        tile.col = newCol
      }
    }

    if (changed) {
      addNewTile(newBoard)
      setBoard(newBoard)
      setScore(newScore)
      if (isGameOverState(newBoard)) {
        setIsGameOver(true)
        handleGameEnd(newBoard, newScore)
      }
    } else if (isGameOverState(newBoard)) {
      setIsGameOver(true)
      handleGameEnd(newBoard, newScore)
    }
  }

  const handleGameEnd = async (finalBoard: Tile[], finalScore: number) => {
    if (isCompetitiveMode && competitiveSessionId && gameStartTime) {
      const gameDuration = Math.floor((Date.now() - gameStartTime) / 1000)
      const highestTile = Math.max(...finalBoard.map(tile => tile.value))

      const gameData = {
        final_score: finalScore,
        moves_count: moveCount,
        highest_tile: highestTile,
        game_duration_seconds: gameDuration,
        entry_fee: entryFee
      }

      try {
        await submitFuseGameScore(competitiveSessionId, finalScore, gameData)
      } catch (error) {
        console.error('Failed to submit FUSE competitive score:', error)
      }
    }
  }

  const isGameOverState = (board: Tile[]) => {
    if (board.length < GRID_SIZE * GRID_SIZE) return false

    for (const tile of board) {
      const { row, col, value } = tile
      if (
        (row > 0 && board.some(t => t.row === row - 1 && t.col === col && t.value === value)) ||
        (row < GRID_SIZE - 1 && board.some(t => t.row === row + 1 && t.col === col && t.value === value)) ||
        (col > 0 && board.some(t => t.row === row && t.col === col - 1 && t.value === value)) ||
        (col < GRID_SIZE - 1 && board.some(t => t.row === row && t.col === col + 1 && t.value === value))
      ) {
        return false
      }
    }

    return true
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    switch (e.key) {
      case 'ArrowUp':
        move('up')
        break
      case 'ArrowDown':
        move('down')
        break
      case 'ArrowLeft':
        move('left')
        break
      case 'ArrowRight':
        move('right')
        break
    }
  }

  // Touch event handlers for swipe gestures
  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0]
    setTouchStart({ x: touch.clientX, y: touch.clientY })
    setTouchEnd(null)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    const touch = e.touches[0]
    setTouchEnd({ x: touch.clientX, y: touch.clientY })
  }

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const deltaX = touchEnd.x - touchStart.x
    const deltaY = touchEnd.y - touchStart.y
    const minSwipeDistance = 50

    // Determine if it's a horizontal or vertical swipe
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      // Horizontal swipe
      if (Math.abs(deltaX) > minSwipeDistance) {
        if (deltaX > 0) {
          move('right')
        } else {
          move('left')
        }
      }
    } else {
      // Vertical swipe
      if (Math.abs(deltaY) > minSwipeDistance) {
        if (deltaY > 0) {
          move('down')
        } else {
          move('up')
        }
      }
    }

    setTouchStart(null)
    setTouchEnd(null)
  }

  const cellColor = (value: number) => {
    switch (value) {
      case 2: return 'bg-[#eee4da] text-[#776e65]'
      case 4: return 'bg-[#ede0c8] text-[#776e65]'
      case 8: return 'bg-[#f2b179] text-white'
      case 16: return 'bg-[#f59563] text-white'
      case 32: return 'bg-[#f67c5f] text-white'
      case 64: return 'bg-[#f65e3b] text-white'
      case 128: return 'bg-[#edcf72] text-white'
      case 256: return 'bg-[#edcc61] text-white'
      case 512: return 'bg-[#edc850] text-white'
      case 1024: return 'bg-[#edc53f] text-white'
      case 2048: return 'bg-[#edc22e] text-white'
      default: return 'bg-[#cdc1b4]'
    }
  }

  const tileVariants = {
    initial: { scale: 0 },
    enter: { scale: 1 },
    merged: {
      scale: [1, 1.1, 1],
      transition: { duration: 0.3 }
    }
  }

  return (
    <div
      className="flex flex-col items-center justify-center bg-[#faf8ef] text-[#776e65] p-4 rounded-lg"
      ref={gameContainerRef}
      tabIndex={0}
      onKeyDown={handleKeyDown}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      aria-label="2048 Game Board"
    >
      <div className="w-full max-w-md flex flex-col items-center">
        <div className="flex justify-between items-center mb-4 w-full">
          <div>
            <h1 className="text-4xl font-bold flex items-center gap-2">
              2048
              {isCompetitiveMode && (
                <span className="text-lg bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-2 py-1 rounded text-sm">
                  🏆 COMPETITIVE
                </span>
              )}
            </h1>
            {isCompetitiveMode && (
              <div className="text-sm text-gray-600 mt-1">
                Entry Fee: {entryFee} FUSE • Moves: {moveCount}
              </div>
            )}
          </div>
          <div className="flex gap-2">
            <div className="bg-[#bbada0] p-2 h-12 w-16 rounded-md text-white flex flex-col items-center">
              <div className="text-xs">SCORE</div>
              <div className="font-bold text-sm">{score}</div>
            </div>
            <div className="bg-[#bbada0] h-12 w-16 rounded-md p-2 text-white flex flex-col items-center">
              <div className="text-xs">BEST</div>
              <div className="font-bold text-sm">{bestScore}</div>
            </div>
          </div>
        </div>
        <div className="bg-[#bbada0] p-2 rounded-lg w-fit touch-none select-none">
          <div className="relative" style={{ width: `${CELL_SIZE * GRID_SIZE + CELL_GAP * (GRID_SIZE - 1)}rem`, height: `${CELL_SIZE * GRID_SIZE + CELL_GAP * (GRID_SIZE - 1)}rem` }}>
            {/* Background grid */}
            {Array.from({ length: GRID_SIZE * GRID_SIZE }).map((_, index) => (
              <div
                key={`cell-${index}`}
                className="absolute bg-[#cdc1b4] rounded-md"
                style={{
                  width: `${CELL_SIZE}rem`,
                  height: `${CELL_SIZE}rem`,
                  left: `${(index % GRID_SIZE) * (CELL_SIZE + CELL_GAP)}rem`,
                  top: `${Math.floor(index / GRID_SIZE) * (CELL_SIZE + CELL_GAP)}rem`,
                }}
              />
            ))}
            {/* Tiles */}
            <AnimatePresence>
              {board.map((tile) => (
                <motion.div
                  key={tile.id}
                  initial={tile.isNew ? { scale: 0, x: tile.col * (CELL_SIZE + CELL_GAP) + 'rem', y: tile.row * (CELL_SIZE + CELL_GAP) + 'rem' } : { scale: 0 }}
                  animate={{
                    scale: 1,
                    x: tile.col * (CELL_SIZE + CELL_GAP) + 'rem',
                    y: tile.row * (CELL_SIZE + CELL_GAP) + 'rem',
                  }}
                  exit={{ scale: 0 }}
                  transition={tile.isNew ? { duration: 0.15 } : { x: { duration: 0.15 }, y: { duration: 0.15 } }}
                  className={`absolute rounded-md flex items-center justify-center text-xl font-bold ${cellColor(tile.value)}`}
                  style={{
                    width: `${CELL_SIZE}rem`,
                    height: `${CELL_SIZE}rem`,
                  }}
                >
                  <motion.div
                    variants={tileVariants}
                    animate={tile.justMerged ? "merged" : "enter"}
                    className="w-full h-full flex items-center justify-center"
                  >
                    {tile.value}
                  </motion.div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
        <div className="mt-4 text-xs text-center max-w-sm">
          <p><strong>HOW TO PLAY:</strong> Use your <strong>arrow keys</strong> or <strong>swipe</strong> to move the tiles. When two tiles with the same number touch, they <strong>merge into one!</strong></p>
        </div>

        {/* Touch Controls for Mobile */}
        <div className="mt-4 grid grid-cols-3 gap-2 md:hidden">
          <div></div>
          <Button
            onClick={() => move('up')}
            className="bg-[#8f7a66] text-white hover:bg-[#9f8a76] p-3 h-12 w-12 flex items-center justify-center"
            aria-label="Move Up"
          >
            <ChevronUp className="h-6 w-6" />
          </Button>
          <div></div>
          <Button
            onClick={() => move('left')}
            className="bg-[#8f7a66] text-white hover:bg-[#9f8a76] p-3 h-12 w-12 flex items-center justify-center"
            aria-label="Move Left"
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>
          <Button
            onClick={() => move('down')}
            className="bg-[#8f7a66] text-white hover:bg-[#9f8a76] p-3 h-12 w-12 flex items-center justify-center"
            aria-label="Move Down"
          >
            <ChevronDown className="h-6 w-6" />
          </Button>
          <Button
            onClick={() => move('right')}
            className="bg-[#8f7a66] text-white hover:bg-[#9f8a76] p-3 h-12 w-12 flex items-center justify-center"
            aria-label="Move Right"
          >
            <ChevronRight className="h-6 w-6" />
          </Button>
        </div>

        <div className="mt-4 space-y-2">
          <div className="flex gap-2">
            <Button onClick={initializeGame} className="bg-[#8f7a66] text-white hover:bg-[#9f8a76]">
              New Game
            </Button>
            <Button
              onClick={() => setShowFuseModal(true)}
              className="bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white"
            >
              <Zap className="h-4 w-4 mr-1" />
              Compete with FUSE
            </Button>
            <Button
              onClick={() => setShowLeaderboard(!showLeaderboard)}
              variant="outline"
              className="border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900"
            >
              <Target className="h-4 w-4 mr-1" />
              Leaderboard
            </Button>
          </div>
        </div>
      </div>

      {/* Leaderboard */}
      {showLeaderboard && (
        <div className="mt-6">
          <GameLeaderboard
            gameType="2048"
            onOpenCompetitive={() => setShowCompetitiveModal(true)}
          />
        </div>
      )}

      {/* FUSE Competitive Game Modal */}
      <FuseCompetitiveModal
        isOpen={showFuseModal}
        onClose={() => setShowFuseModal(false)}
        gameType="2048"
        onGameStart={startFuseCompetitiveGame}
      />

      <Dialog open={isGameOver} onOpenChange={setIsGameOver}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {isCompetitiveMode ? '🏆 Competitive Game Over!' : 'Game Over!'}
            </DialogTitle>
            <DialogDescription>
              Your score: {score}
              {score === bestScore && score > 0 && " (New Best!)"}
              {isCompetitiveMode && (
                <div className="mt-2 text-sm">
                  <div>Moves: {moveCount}</div>
                  <div>Entry Fee: {entryFee} FUSE</div>
                  <div className="text-green-600 font-semibold">Score submitted to FUSE leaderboard!</div>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={initializeGame} className="bg-[#8f7a66] text-white hover:bg-[#9f8a76]">
              Play Again
            </Button>
            {isCompetitiveMode && (
              <Button
                onClick={() => {
                  setIsCompetitiveMode(false)
                  setCompetitiveSessionId(null)
                  setShowLeaderboard(true)
                  setIsGameOver(false)
                }}
                variant="outline"
                className="text-gray-700 hover:bg-gray-50 hover:text-gray-900"
              >
                View Leaderboard
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
