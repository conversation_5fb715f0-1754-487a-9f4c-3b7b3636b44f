'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription 
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Wallet, 
  ExternalLink, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Trophy,
  Zap,
  ArrowRight
} from 'lucide-react'
import { useAuth } from '@/contexts/auth-context'
import { useWallet } from '@/hooks/use-wallet'
import { useFuseGaming } from '@/contexts/fuse-gaming-context'
import { formatFuseAmount, hasSufficientFuseBalance } from '@/lib/fuse-token-utils'

interface FuseCompetitiveModalProps {
  isOpen: boolean
  onClose: () => void
  gameType: string
  onGameStart: (sessionId: string, entryFee: number) => void
}

export function FuseCompetitiveModal({
  isOpen,
  onClose,
  gameType,
  onGameStart
}: FuseCompetitiveModalProps) {
  const { user } = useAuth()
  const { isConnected, walletAddress } = useWallet()
  const {
    trustlineStatus,
    isLoading,
    checkUserTrustline,
    startFuseCompetitiveGame,
    createFusePayment,
    getMagneticDexUrl
  } = useFuseGaming()

  const [currentStep, setCurrentStep] = useState<'check' | 'trustline' | 'payment' | 'processing'>('check')
  const [sessionId, setSessionId] = useState<string | null>(null)
  const [paymentUrl, setPaymentUrl] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  const entryFee = 1 // 1 FUSE token entry fee

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setCurrentStep('check')
      setSessionId(null)
      setPaymentUrl(null)
      setError(null)
      
      // Check trustline status
      if (isConnected && walletAddress) {
        checkUserTrustline()
      }
    }
  }, [isOpen, isConnected, walletAddress, checkUserTrustline])

  // Update step based on trustline status
  useEffect(() => {
    if (!trustlineStatus.checking && isOpen) {
      if (trustlineStatus.hasTrustline) {
        if (hasSufficientFuseBalance(trustlineStatus.balance, entryFee.toString())) {
          setCurrentStep('payment')
        } else {
          setError('Insufficient FUSE balance. You need at least 1 FUSE token to play.')
        }
      } else {
        setCurrentStep('trustline')
      }
    }
  }, [trustlineStatus, isOpen, entryFee])

  const handleStartGame = async () => {
    if (!user || !isConnected) {
      setError('Please connect your wallet and sign in')
      return
    }

    try {
      setCurrentStep('processing')
      setError(null)

      // Create gaming session
      const newSessionId = await startFuseCompetitiveGame(gameType, entryFee)
      if (!newSessionId) {
        throw new Error('Failed to create gaming session')
      }

      setSessionId(newSessionId)

      // Create payment
      const payment = await createFusePayment(newSessionId, gameType)
      if (payment?.next?.always) {
        setPaymentUrl(payment.next.always)
        
        // Open payment in new window/tab
        window.open(payment.next.always, '_blank')
        
        // Start the game immediately (payment verification will happen in background)
        onGameStart(newSessionId, entryFee)
        onClose()
      } else {
        throw new Error('Failed to create payment request')
      }

    } catch (error) {
      console.error('Error starting FUSE game:', error)
      setError(error instanceof Error ? error.message : 'Failed to start game')
      setCurrentStep('payment')
    }
  }

  const handleGoToMagneticDex = () => {
    if (sessionId) {
      const magneticUrl = getMagneticDexUrl(sessionId, gameType)
      window.open(magneticUrl, '_blank')
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 'check':
        return (
          <div className="text-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
            <p className="text-gray-600">Checking your FUSE trustline...</p>
          </div>
        )

      case 'trustline':
        return (
          <div className="text-center py-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
              <AlertCircle className="h-8 w-8 text-yellow-600 mx-auto mb-3" />
              <h3 className="font-semibold text-yellow-800 mb-2">FUSE Trustline Required</h3>
              <p className="text-yellow-700 text-sm mb-4">
                You need to establish a trustline with the FUSE token before you can play. 
                This is a one-time setup that allows you to receive and send FUSE tokens.
              </p>
              <div className="bg-white rounded-lg p-4 border border-yellow-200">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Token:</span>
                  <Badge variant="outline">FUSE</Badge>
                </div>
                <div className="flex items-center justify-between text-sm mt-2">
                  <span className="text-gray-600">Issuer:</span>
                  <span className="font-mono text-xs text-gray-800">rs2G9J...ouVHo</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <Button 
                onClick={handleGoToMagneticDex}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                size="lg"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Go to Magnetic DEX
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
              
              <p className="text-xs text-gray-500">
                Connect your wallet on Magnetic DEX, establish the FUSE trustline, then return here to play
              </p>

              <Button 
                variant="outline" 
                onClick={checkUserTrustline}
                disabled={trustlineStatus.checking}
                className="w-full"
              >
                {trustlineStatus.checking ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                Check Trustline Again
              </Button>
            </div>
          </div>
        )

      case 'payment':
        return (
          <div className="py-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
              <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-3" />
              <h3 className="font-semibold text-green-800 mb-2 text-center">Trustline Verified!</h3>
              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">FUSE Balance:</span>
                  <Badge variant="outline" className="text-green-700 border-green-300">
                    {formatFuseAmount(trustlineStatus.balance)} FUSE
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm mt-2">
                  <span className="text-gray-600">Entry Fee:</span>
                  <span className="font-semibold text-gray-800">{entryFee} FUSE</span>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6 mb-6">
              <div className="flex items-center mb-3">
                <Trophy className="h-6 w-6 text-purple-600 mr-2" />
                <h3 className="font-semibold text-purple-800">Competitive Gaming</h3>
              </div>
              <ul className="text-sm text-purple-700 space-y-1">
                <li>• Compete for FUSE token prizes</li>
                <li>• Climb the leaderboards</li>
                <li>• Automated prize distribution</li>
                <li>• Verified on-chain transactions</li>
              </ul>
            </div>

            <Button 
              onClick={handleStartGame}
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700"
              size="lg"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Zap className="h-4 w-4 mr-2" />
              )}
              Pay 1 FUSE & Start Game
            </Button>
          </div>
        )

      case 'processing':
        return (
          <div className="text-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-purple-500" />
            <h3 className="font-semibold mb-2">Processing Payment...</h3>
            <p className="text-gray-600 text-sm">
              Please complete the payment in your Xaman wallet
            </p>
            {paymentUrl && (
              <Button 
                variant="outline" 
                onClick={() => window.open(paymentUrl, '_blank')}
                className="mt-4"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Open Payment
              </Button>
            )}
          </div>
        )

      default:
        return null
    }
  }

  if (!user) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Wallet className="h-5 w-5 mr-2" />
              Sign In Required
            </DialogTitle>
            <DialogDescription>
              Please sign in to play competitive games with FUSE tokens.
            </DialogDescription>
          </DialogHeader>
          <div className="text-center py-4">
            <Button onClick={onClose} className="w-full">
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Zap className="h-5 w-5 mr-2 text-purple-500" />
            FUSE Token Gaming
          </DialogTitle>
          <DialogDescription>
            Compete with FUSE tokens for prizes and leaderboard rankings
          </DialogDescription>
        </DialogHeader>

        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {renderStepContent()}
          </motion.div>
        </AnimatePresence>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-4">
            <div className="flex items-center">
              <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          </div>
        )}

        <div className="flex justify-between items-center pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          
          {!isConnected && (
            <Badge variant="outline" className="text-yellow-600 border-yellow-300">
              <Wallet className="h-3 w-3 mr-1" />
              Wallet Required
            </Badge>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
