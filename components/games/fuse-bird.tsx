"use client"

import React, { useEffect, useRef, useState, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Trophy, Target, Zap } from 'lucide-react'
import { FuseCompetitiveModal } from './fuse-competitive-modal'
import { GameLeaderboard } from './game-leaderboard'
import { useFuseGaming } from '@/contexts/fuse-gaming-context'

// Mobile detection hook
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  return isMobile
}

// Haptic feedback utility
const triggerHapticFeedback = () => {
  if (navigator.vibrate) {
    navigator.vibrate(50) // Short vibration for jump
  }
}

const GRAVITY = 0.5
const JUMP_STRENGTH = 10
const PIPE_WIDTH = 52
const PIPE_GAP = 150
const PIPE_SPEED = 2
const BIRD_WIDTH = 34
const BIRD_HEIGHT = 24

interface Bird {
  y: number
  velocity: number
  frame: number
}

interface Pipe {
  x: number
  topHeight: number
}

export function FuseBird() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [bird, setBird] = useState<Bird>({ y: 200, velocity: 0, frame: 0 })
  const [pipes, setPipes] = useState<Pipe[]>([])
  const [score, setScore] = useState(0)
  const [gameOver, setGameOver] = useState(false)
  const [gameStarted, setGameStarted] = useState(false)
  const isMobile = useIsMobile()

  // FUSE competitive gaming state
  const [showFuseModal, setShowFuseModal] = useState(false)
  const [showLeaderboard, setShowLeaderboard] = useState(false)
  const [isCompetitiveMode, setIsCompetitiveMode] = useState(false)
  const [isFuseMode, setIsFuseMode] = useState(false)
  const [competitiveSessionId, setCompetitiveSessionId] = useState<string | null>(null)
  const [entryFee, setEntryFee] = useState(0)
  const [gameStartTime, setGameStartTime] = useState<number | null>(null)
  const [pipesPassed, setPipesPassed] = useState(0)

  const { submitFuseGameScore } = useFuseGaming()

  const birdSprites = useRef<HTMLImageElement[]>([])
  const backgroundImage = useRef<HTMLImageElement | null>(null)
  const numberSprites = useRef<HTMLImageElement[]>([])
  const gameOverImage = useRef<HTMLImageElement | null>(null)
  const messageImage = useRef<HTMLImageElement | null>(null)
  const pipeImage = useRef<HTMLImageElement | null>(null)
  const [assetsLoaded, setAssetsLoaded] = useState(false)

  // Audio refs
  const pointSound = useRef<HTMLAudioElement | null>(null)
  const hitSound = useRef<HTMLAudioElement | null>(null)
  const wingSound = useRef<HTMLAudioElement | null>(null)

  useEffect(() => {
    const birdUrls = [
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/yellowbird-downflap-ZExrg9YxRxwFfLXDu6JijpJUQgByX6.png',
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/yellowbird-midflap-8mBrx070GYsw2As4Ue9BfQJ5XNMUg3.png',
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/yellowbird-upflap-hMo7jE66Ar0TzdbAMTzTMWaEGpTNx2.png'
    ]
    const numberUrls = [
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/0-n6uJmiEzXXFf0NDHejRxdna8JdqZ9P.png',
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/1-2s71zdNWUSfnqIUbOABB2QJzzbG7fR.png',
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/2-QNpaMYRZvP9MgObyqVbxo7wu0MyjYE.png',
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/3-6yXb5a7IxZyl8kdXXBatpxq48enb2d.png',
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/4-9beOrHBy4QSBLifUwqaLXqbNWfK4Hr.png',
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/5-pgAY4wiTYa2Ppho9w3YXtLx3UHryJI.png',
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/6-5v6snji9HWY7UpBuqDkKDtck2zED4B.png',
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/7-zTxqP8uIOG4OYFtl8x6Dby0mqKfNYo.png',
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/8-gkhiN6iBVr2DY7SqrTZIEP7Q3doyo9.png',
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/9-PxwOSLzHQAiMeneqctp2q5mzWAv0Kv.png'
    ]
    const backgroundUrl = 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/background-day-rvpnF7CJRMdBNqqBc8Zfzz3QpIfkBG.png'
    const gameOverUrl = 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/gameover-NwA13AFRtIFat9QoA12T3lpjK76Qza.png'
    const messageUrl = 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/message-g1ru4NKF3KrKoFmiVpzR8fwdeLhwNa.png'
    const pipeUrl = 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/pipe-green-zrz2zTtoVXaLn6xDqgrNVF9luzjW1B.png'

    const loadImage = (url: string) => new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve(img)
      img.onerror = reject
      img.src = url
    })

    const loadAudio = (url: string) => new Promise<HTMLAudioElement>((resolve, reject) => {
      const audio = new Audio(url)
      audio.oncanplaythrough = () => resolve(audio)
      audio.onerror = reject
      audio.src = url
    })

    Promise.all([
      ...birdUrls.map(loadImage),
      ...numberUrls.map(loadImage),
      loadImage(backgroundUrl),
      loadImage(gameOverUrl),
      loadImage(messageUrl),
      loadImage(pipeUrl),
      loadAudio('https://hebbkx1anhila5yf.public.blob.vercel-storage.com/point-SdTORahWMlxujnLCoDbujDLHI6KFeC.wav'),
      loadAudio('https://hebbkx1anhila5yf.public.blob.vercel-storage.com/hit-YVMFYQJEgZASG6O3xPWiyiqPtOLygb.wav'),
      loadAudio('https://hebbkx1anhila5yf.public.blob.vercel-storage.com/wing-oOSsspXpVMDc0enrWj4WWLaHVqs6Hk.wav')
    ]).then((loadedAssets) => {
      birdSprites.current = loadedAssets.slice(0, 3) as HTMLImageElement[]
      numberSprites.current = loadedAssets.slice(3, 13) as HTMLImageElement[]
      backgroundImage.current = loadedAssets[13] as HTMLImageElement
      gameOverImage.current = loadedAssets[14] as HTMLImageElement
      messageImage.current = loadedAssets[15] as HTMLImageElement
      pipeImage.current = loadedAssets[16] as HTMLImageElement
      pointSound.current = loadedAssets[17] as HTMLAudioElement
      hitSound.current = loadedAssets[18] as HTMLAudioElement
      wingSound.current = loadedAssets[19] as HTMLAudioElement
      setAssetsLoaded(true)
    })
  }, [])

  const playSound = useCallback((sound: HTMLAudioElement | null) => {
    if (sound && !gameOver) {
      sound.currentTime = 0
      sound.play().catch(error => console.error("Error playing sound:", error))
    }
  }, [gameOver])

  const jump = useCallback(() => {
    if (!gameOver && gameStarted) {
      setBird(prevBird => ({ ...prevBird, velocity: -JUMP_STRENGTH }))
      playSound(wingSound.current)
      if (isMobile) {
        triggerHapticFeedback()
      }
    } else if (!gameStarted) {
      setGameStarted(true)
    }
  }, [gameOver, gameStarted, playSound, isMobile])

  const restartGame = useCallback(() => {
    setBird({ y: 200, velocity: 0, frame: 0 })
    setPipes([])
    setScore(0)
    setGameOver(false)
    setGameStarted(true)
    setPipesPassed(0)
    if (isCompetitiveMode) {
      setGameStartTime(Date.now())
    }
  }, [isCompetitiveMode])

  const startFuseCompetitiveGame = (sessionId: string, fee: number) => {
    setCompetitiveSessionId(sessionId)
    setEntryFee(fee)
    setIsCompetitiveMode(true)
    setIsFuseMode(true)
    setGameStartTime(Date.now())
    restartGame()
  }

  const handleGameEnd = async () => {
    if (isCompetitiveMode && competitiveSessionId && gameStartTime) {
      const gameDuration = Math.floor((Date.now() - gameStartTime) / 1000)

      const gameData = {
        final_score: score,
        pipes_passed: pipesPassed,
        game_duration_seconds: gameDuration,
        entry_fee: entryFee
      }

      try {
        await submitFuseGameScore(competitiveSessionId, score, gameData)
      } catch (error) {
        console.error('Failed to submit FUSE competitive score:', error)
      }
    }
  }

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.code === 'Space') {
        if (!gameStarted) {
          setGameStarted(true)
        } else if (!gameOver) {
          jump()
        }
      }
    }
    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [jump, gameStarted, gameOver])

  useEffect(() => {
    if (!assetsLoaded) return

    const canvas = canvasRef.current
    const ctx = canvas?.getContext('2d')
    if (!canvas || !ctx) return

    const gameLoop = setInterval(() => {
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw background
      if (backgroundImage.current) {
        ctx.drawImage(backgroundImage.current, 0, 0, canvas.width, canvas.height)
      }

      if (!gameStarted) {
        // Draw message
        if (messageImage.current) {
          const messageWidth = 184
          const messageHeight = 267
          const messageX = (canvas.width - messageWidth) / 2
          const messageY = (canvas.height - messageHeight) / 2
          ctx.drawImage(messageImage.current, messageX, messageY, messageWidth, messageHeight)
        }
        return
      }

      // Update bird position and animation frame
      setBird(prevBird => ({
        y: prevBird.y + prevBird.velocity,
        velocity: prevBird.velocity + GRAVITY,
        frame: (prevBird.frame + 1) % 3
      }))

      // Move pipes
      setPipes(prevPipes => prevPipes.map(pipe => ({ ...pipe, x: pipe.x - PIPE_SPEED })))

      // Generate new pipes
      if (pipes.length === 0 || pipes[pipes.length - 1].x < canvas.width - 200) {
        const topHeight = Math.random() * (canvas.height - PIPE_GAP - 100) + 50
        setPipes(prevPipes => [...prevPipes, { x: canvas.width, topHeight }])
      }

      // Remove off-screen pipes
      setPipes(prevPipes => prevPipes.filter(pipe => pipe.x + PIPE_WIDTH > 0))

      // Check collisions
      const birdRect = { x: 50, y: bird.y, width: BIRD_WIDTH, height: BIRD_HEIGHT }
      for (const pipe of pipes) {
        const topPipeRect = { x: pipe.x, y: 0, width: PIPE_WIDTH, height: pipe.topHeight }
        const bottomPipeRect = { x: pipe.x, y: pipe.topHeight + PIPE_GAP, width: PIPE_WIDTH, height: canvas.height - pipe.topHeight - PIPE_GAP }

        if (
          birdRect.x < topPipeRect.x + topPipeRect.width &&
          birdRect.x + birdRect.width > topPipeRect.x &&
          birdRect.y < topPipeRect.y + topPipeRect.height &&
          birdRect.y + birdRect.height > topPipeRect.y
        ) {
          setGameOver(true)
          playSound(hitSound.current)
          handleGameEnd()
        }

        if (
          birdRect.x < bottomPipeRect.x + bottomPipeRect.width &&
          birdRect.x + birdRect.width > bottomPipeRect.x &&
          birdRect.y < bottomPipeRect.y + bottomPipeRect.height &&
          birdRect.y + birdRect.height > bottomPipeRect.y
        ) {
          setGameOver(true)
          playSound(hitSound.current)
          handleGameEnd()
        }
      }

      // Update score
      if (!gameOver && pipes.some(pipe => pipe.x + PIPE_WIDTH < 50 && pipe.x + PIPE_WIDTH >= 48)) {
        setScore(prevScore => prevScore + 1)
        setPipesPassed(prev => prev + 1)
        playSound(pointSound.current)
      }

      // Draw pipes
      pipes.forEach(pipe => {
        if (pipeImage.current) {
          // Draw top pipe (flipped vertically)
          ctx.save()
          ctx.scale(1, -1)
          ctx.drawImage(pipeImage.current, pipe.x, -pipe.topHeight, PIPE_WIDTH, 320)
          ctx.restore()

          // Draw bottom pipe
          ctx.drawImage(pipeImage.current, pipe.x, pipe.topHeight + PIPE_GAP, PIPE_WIDTH, 320)
        }
      })

      // Draw bird
      ctx.save()
      ctx.translate(50 + BIRD_WIDTH / 2, bird.y + BIRD_HEIGHT / 2)
      ctx.rotate(Math.min(Math.PI / 4, Math.max(-Math.PI / 4, bird.velocity * 0.1)))
      ctx.drawImage(
        birdSprites.current[bird.frame],
        -BIRD_WIDTH / 2,
        -BIRD_HEIGHT / 2,
        BIRD_WIDTH,
        BIRD_HEIGHT
      )
      ctx.restore()

      // Draw score
      const scoreString = score.toString()
      const digitWidth = 24
      const totalWidth = scoreString.length * digitWidth
      const startX = (canvas.width - totalWidth) / 2
      scoreString.split('').forEach((digit, index) => {
        const digitImage = numberSprites.current[parseInt(digit)]
        if (digitImage) {
          ctx.drawImage(digitImage, startX + index * digitWidth, 20, digitWidth, 36)
        }
      })

      if (bird.y > canvas.height || bird.y < 0) {
        setGameOver(true)
        playSound(hitSound.current)
        handleGameEnd()
      }

      if (gameOver) {
        clearInterval(gameLoop)
        if (gameOverImage.current) {
          const gameOverWidth = 192
          const gameOverHeight = 42
          const gameOverX = (canvas.width - gameOverWidth) / 2
          const gameOverY = (canvas.height - gameOverHeight) / 2
          ctx.drawImage(gameOverImage.current, gameOverX, gameOverY, gameOverWidth, gameOverHeight)

          // Draw Restart button
          ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
          ctx.fillRect(canvas.width / 2 - 50, canvas.height / 2 + 50, 100, 40)
          ctx.fillStyle = 'white'
          ctx.font = '20px Arial'
          ctx.fillText('Restart', canvas.width / 2 - 30, canvas.height / 2 + 75)
        }
      }
    }, 1000 / 60) // 60 FPS

    return () => clearInterval(gameLoop)
  }, [bird, pipes, gameOver, score, jump, gameStarted, assetsLoaded, restartGame, playSound])

  // Touch and click handlers for better mobile experience
  const handleCanvasTouch = useCallback((event: React.TouchEvent<HTMLCanvasElement>) => {
    event.preventDefault() // Prevent zoom, scroll, and other default behaviors

    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const touch = event.touches[0] || event.changedTouches[0]
    const x = touch.clientX - rect.left
    const y = touch.clientY - rect.top

    if (gameOver) {
      // Check if touch is within Restart button area
      if (
        x >= canvas.width / 2 - 50 &&
        x <= canvas.width / 2 + 50 &&
        y >= canvas.height / 2 + 50 &&
        y <= canvas.height / 2 + 90
      ) {
        restartGame()
      }
    } else {
      jump()
    }
  }, [gameOver, jump, restartGame])

  const handleCanvasClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    // Only handle mouse clicks on non-mobile devices to avoid conflicts with touch events
    if (isMobile) return

    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    if (gameOver) {
      // Check if click is within Restart button area
      if (
        x >= canvas.width / 2 - 50 &&
        x <= canvas.width / 2 + 50 &&
        y >= canvas.height / 2 + 50 &&
        y <= canvas.height / 2 + 90
      ) {
        restartGame()
      }
    } else {
      jump()
    }
  }, [gameOver, jump, restartGame, isMobile])

  return (
    <div className="flex flex-col items-center justify-center">
      {/* Game Header */}
      <div className="text-center mb-4">
        <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-2">
          🐦 Fuse Bird
          {isCompetitiveMode && (
            <span className="text-lg bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-2 py-1 rounded text-sm">
              🏆 COMPETITIVE
            </span>
          )}
        </h2>
        {isCompetitiveMode && (
          <div className="text-sm text-gray-600">
            Entry Fee: {entryFee} FUSE • Score: {score} • Pipes: {pipesPassed}
          </div>
        )}
      </div>

      <canvas
        ref={canvasRef}
        width={isMobile ? 320 : 288}
        height={isMobile ? 568 : 512}
        className={`border border-gray-300 rounded-lg shadow-lg select-none game-canvas ${
          isMobile ? 'mobile-game-container' : ''
        }`}
        style={{
          touchAction: 'manipulation', // Prevents zoom and other touch gestures
          userSelect: 'none',
          WebkitUserSelect: 'none',
          WebkitTouchCallout: 'none',
          WebkitTapHighlightColor: 'transparent'
        }}
        onClick={handleCanvasClick}
        onTouchStart={handleCanvasTouch}
        onTouchEnd={(e) => e.preventDefault()}
        onTouchMove={(e) => e.preventDefault()}
      />

      <p className="mt-4 text-sm text-gray-600 text-center max-w-xs">
        {isMobile
          ? "Tap anywhere on the game area to jump. Avoid double-tapping to prevent zoom."
          : "Press Space to start/jump or click the game area"
        }
      </p>
      {isMobile && (
        <div className="mt-2 text-xs text-gray-500 text-center">
          💡 Tip: Single taps work best for smooth gameplay
        </div>
      )}

      {/* Game Controls */}
      {!gameStarted && !isCompetitiveMode && (
        <div className="mt-4 flex flex-wrap gap-2 justify-center">
          <Button
            onClick={() => setShowFuseModal(true)}
            className="bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white"
          >
            <Zap className="h-4 w-4 mr-1" />
            Compete with FUSE
          </Button>
          <Button
            onClick={() => setShowLeaderboard(!showLeaderboard)}
            variant="outline"
            className="border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900"
          >
            <Target className="h-4 w-4 mr-1" />
            Leaderboard
          </Button>
        </div>
      )}

      {/* Competitive Game End Message */}
      {gameOver && isCompetitiveMode && (
        <div className="mt-4 text-center p-4 bg-green-50 border border-green-200 rounded-lg max-w-xs">
          <div className="text-lg font-semibold text-green-800 mb-2">
            🎉 Competitive Game Complete!
          </div>
          <div className="text-sm text-green-600">
            Final Score: {score} • Pipes Passed: {pipesPassed}
          </div>
          <div className="text-xs text-green-500 mt-1">
            Score submitted to leaderboard!
          </div>
          <Button
            onClick={() => {
              setIsCompetitiveMode(false)
              setCompetitiveSessionId(null)
              setShowLeaderboard(true)
            }}
            className="mt-3 w-full"
            size="sm"
          >
            View Leaderboard
          </Button>
        </div>
      )}

      {/* Leaderboard */}
      {showLeaderboard && (
        <div className="mt-6 w-full max-w-4xl">
          <GameLeaderboard
            gameType="fuse-bird"
            onOpenCompetitive={() => setShowCompetitiveModal(true)}
          />
        </div>
      )}

      {/* FUSE Competitive Game Modal */}
      <FuseCompetitiveModal
        isOpen={showFuseModal}
        onClose={() => setShowFuseModal(false)}
        gameType="fuse-bird"
        onGameStart={startFuseCompetitiveGame}
      />
    </div>
  )
}
