"use client"

import React, { useState, useCallback, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Trophy, Target, Zap } from 'lucide-react'
import { FuseCompetitiveModal } from './fuse-competitive-modal'
import { GameLeaderboard } from './game-leaderboard'
import { useFuseGaming } from '@/contexts/fuse-gaming-context'

type Choice = 'rock' | 'paper' | 'scissors'
type GameResult = 'win' | 'lose' | 'tie'

interface GameState {
  playerChoice: Choice | null
  computerChoice: Choice | null
  result: GameResult | null
  playerScore: number
  computerScore: number
  isPlaying: boolean
  showResult: boolean
  totalRounds: number
  gameStartTime: number | null
}

const choices: Choice[] = ['rock', 'paper', 'scissors']

const getEmoji = (choice: Choice): string => {
  switch (choice) {
    case 'rock': return '🪨'
    case 'paper': return '📄'
    case 'scissors': return '✂️'
  }
}

const getChoiceName = (choice: Choice): string => {
  switch (choice) {
    case 'rock': return 'Rock'
    case 'paper': return 'Paper'
    case 'scissors': return 'Scissors'
  }
}

const determineWinner = (player: Choice, computer: Choice): GameResult => {
  if (player === computer) return 'tie'
  
  if (
    (player === 'rock' && computer === 'scissors') ||
    (player === 'paper' && computer === 'rock') ||
    (player === 'scissors' && computer === 'paper')
  ) {
    return 'win'
  }
  
  return 'lose'
}

const getResultMessage = (result: GameResult): string => {
  switch (result) {
    case 'win': return '🎉 You Win!'
    case 'lose': return '😅 Computer Wins!'
    case 'tie': return '🤝 It\'s a Tie!'
  }
}

const getResultColor = (result: GameResult): string => {
  switch (result) {
    case 'win': return 'text-green-600'
    case 'lose': return 'text-red-600'
    case 'tie': return 'text-yellow-600'
  }
}

export function RockPaperScissors() {
  const [gameState, setGameState] = useState<GameState>({
    playerChoice: null,
    computerChoice: null,
    result: null,
    playerScore: 0,
    computerScore: 0,
    isPlaying: false,
    showResult: false,
    totalRounds: 0,
    gameStartTime: null
  })

  // FUSE competitive gaming state
  const [showFuseModal, setShowFuseModal] = useState(false)
  const [showLeaderboard, setShowLeaderboard] = useState(false)
  const [isCompetitiveMode, setIsCompetitiveMode] = useState(false)
  const [isFuseMode, setIsFuseMode] = useState(false)
  const [competitiveSessionId, setCompetitiveSessionId] = useState<string | null>(null)
  const [entryFee, setEntryFee] = useState(0)
  const [gameEnded, setGameEnded] = useState(false)

  const { submitFuseGameScore } = useFuseGaming()



  const playGame = useCallback((playerChoice: Choice) => {
    const computerChoice = choices[Math.floor(Math.random() * choices.length)]
    const result = determineWinner(playerChoice, computerChoice)

    setGameState(prev => ({
      ...prev,
      playerChoice,
      computerChoice,
      result,
      isPlaying: true,
      showResult: false,
      totalRounds: prev.totalRounds + 1,
      gameStartTime: prev.gameStartTime || Date.now()
    }))

    // Show result after animation
    setTimeout(() => {
      setGameState(prev => {
        const newPlayerScore = result === 'win' ? prev.playerScore + 1 : prev.playerScore
        const newComputerScore = result === 'lose' ? prev.computerScore + 1 : prev.computerScore

        return {
          ...prev,
          showResult: true,
          playerScore: newPlayerScore,
          computerScore: newComputerScore
        }
      })

      // Check if competitive game should end (first to 5 wins or 10 total rounds)
      if (isCompetitiveMode) {
        setGameState(prev => {
          const shouldEnd = prev.playerScore >= 5 || prev.computerScore >= 5 || prev.totalRounds >= 10
          if (shouldEnd && !gameEnded) {
            setGameEnded(true)
            handleCompetitiveGameEnd(prev.playerScore, prev.computerScore, prev.totalRounds, prev.gameStartTime!)
          }
          return prev
        })
      }
    }, 1500)

    // Reset for next round (only if game hasn't ended)
    setTimeout(() => {
      setGameState(prev => {
        if (gameEnded) return prev
        return {
          ...prev,
          isPlaying: false,
          showResult: false,
          playerChoice: null,
          computerChoice: null,
          result: null
        }
      })
    }, 3500)
  }, [isCompetitiveMode, gameEnded])

  const resetGame = useCallback(() => {
    setGameState({
      playerChoice: null,
      computerChoice: null,
      result: null,
      playerScore: 0,
      computerScore: 0,
      isPlaying: false,
      showResult: false,
      totalRounds: 0,
      gameStartTime: null
    })
    setGameEnded(false)
  }, [])

  const startFuseCompetitiveGame = (sessionId: string, fee: number) => {
    setCompetitiveSessionId(sessionId)
    setEntryFee(fee)
    setIsCompetitiveMode(true)
    setIsFuseMode(true)
    setGameEnded(false)
    resetGame()
  }

  const handleCompetitiveGameEnd = async (playerScore: number, computerScore: number, totalRounds: number, startTime: number) => {
    if (!competitiveSessionId || !startTime) return

    const gameDuration = Math.floor((Date.now() - startTime) / 1000)

    // Calculate final score based on wins and performance
    const winRate = totalRounds > 0 ? (playerScore / totalRounds) * 100 : 0
    const finalScore = Math.floor(playerScore * 100 + winRate * 10) // Base score + win rate bonus

    const gameData = {
      player_score: playerScore,
      computer_score: computerScore,
      total_rounds: totalRounds,
      win_rate: winRate,
      game_duration_seconds: gameDuration,
      entry_fee: entryFee
    }

    try {
      await submitFuseGameScore(competitiveSessionId, finalScore, gameData)
    } catch (error) {
      console.error('Failed to submit FUSE competitive score:', error)
    }
  }

  return (
    <div className="flex flex-col items-center justify-center p-6 max-w-md mx-auto">
      {/* Header */}
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-2">
          Rock Paper Scissors
          {isCompetitiveMode && (
            <span className="text-lg bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-2 py-1 rounded text-sm">
              🏆 COMPETITIVE
            </span>
          )}
        </h2>
        <p className="text-gray-600">
          {isCompetitiveMode
            ? `First to 5 wins or 10 rounds! Entry Fee: ${entryFee} FUSE`
            : 'Challenge the computer!'
          }
        </p>
        {isCompetitiveMode && (
          <div className="text-sm text-gray-500 mt-1">
            Round {gameState.totalRounds}/10
          </div>
        )}
      </div>

      {/* Score Board */}
      <div className="flex justify-between w-full mb-6 bg-gray-50 rounded-lg p-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">{gameState.playerScore}</div>
          <div className="text-sm text-gray-600">You</div>
        </div>
        <div className="text-center">
          <div className="text-lg text-gray-400">VS</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-red-600">{gameState.computerScore}</div>
          <div className="text-sm text-gray-600">Computer</div>
        </div>
      </div>

      {/* Game Area */}
      <div className="w-full mb-6">
        <AnimatePresence mode="wait">
          {!gameState.isPlaying ? (
            <motion.div
              key="choices"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="text-center"
            >
              <p className="text-gray-600 mb-4">Choose your weapon:</p>
              <div className="flex justify-center gap-4">
                {choices.map((choice) => (
                  <motion.button
                    key={choice}
                    onClick={() => playGame(choice)}
                    className="bg-white border-2 border-gray-200 hover:border-blue-400 rounded-lg p-4 transition-all duration-200 hover:shadow-lg"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <div className="text-4xl mb-2">{getEmoji(choice)}</div>
                    <div className="text-sm font-medium text-gray-700">{getChoiceName(choice)}</div>
                  </motion.button>
                ))}
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="battle"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center"
            >
              <div className="flex justify-between items-center mb-6">
                <div className="text-center">
                  <div className="text-sm text-gray-600 mb-2">You</div>
                  <motion.div
                    className="text-6xl"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.5, type: "spring", stiffness: 200 }}
                  >
                    {gameState.playerChoice && getEmoji(gameState.playerChoice)}
                  </motion.div>
                </div>
                
                <motion.div
                  className="text-2xl"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: gameState.showResult ? 0 : Infinity }}
                >
                  ⚔️
                </motion.div>
                
                <div className="text-center">
                  <div className="text-sm text-gray-600 mb-2">Computer</div>
                  <motion.div
                    className="text-6xl"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 1, type: "spring", stiffness: 200 }}
                  >
                    {gameState.computerChoice && getEmoji(gameState.computerChoice)}
                  </motion.div>
                </div>
              </div>

              <AnimatePresence>
                {gameState.showResult && gameState.result && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.5 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.5 }}
                    className="text-center"
                  >
                    <div className={`text-2xl font-bold mb-2 ${getResultColor(gameState.result)}`}>
                      {getResultMessage(gameState.result)}
                    </div>
                    <div className="text-sm text-gray-600">
                      {gameState.playerChoice && gameState.computerChoice && 
                        `${getChoiceName(gameState.playerChoice)} vs ${getChoiceName(gameState.computerChoice)}`
                      }
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Game Controls */}
      <div className="w-full space-y-3">
        {/* Reset Button */}
        {(gameState.playerScore > 0 || gameState.computerScore > 0) && !gameState.isPlaying && !gameEnded && (
          <Button
            onClick={resetGame}
            variant="outline"
            className="w-full"
          >
            Reset Game
          </Button>
        )}

        {/* Competitive Controls */}
        {!isCompetitiveMode && !gameState.isPlaying && (
          <div className="flex gap-2">
            <Button
              onClick={() => setShowFuseModal(true)}
              className="flex-1 bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white"
            >
              <Zap className="h-4 w-4 mr-1" />
              Compete with FUSE
            </Button>
            <Button
              onClick={() => setShowLeaderboard(!showLeaderboard)}
              variant="outline"
              className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900"
            >
              <Target className="h-4 w-4 mr-1" />
              Leaderboard
            </Button>
          </div>
        )}

        {/* Game End Message for Competitive */}
        {gameEnded && isCompetitiveMode && (
          <div className="text-center p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="text-lg font-semibold text-green-800 mb-2">
              🎉 Competitive Game Complete!
            </div>
            <div className="text-sm text-green-600">
              Final Score: You {gameState.playerScore} - {gameState.computerScore} Computer
            </div>
            <div className="text-xs text-green-500 mt-1">
              Score submitted to FUSE leaderboard!
            </div>
            <Button
              onClick={() => {
                setIsCompetitiveMode(false)
                setCompetitiveSessionId(null)
                setShowLeaderboard(true)
                resetGame()
              }}
              className="mt-3 w-full"
              size="sm"
            >
              View Leaderboard
            </Button>
          </div>
        )}
      </div>

      {/* Leaderboard */}
      {showLeaderboard && (
        <div className="mt-6 w-full">
          <GameLeaderboard
            gameType="rock-paper-scissors"
            onOpenCompetitive={() => setShowCompetitiveModal(true)}
          />
        </div>
      )}

      {/* FUSE Competitive Game Modal */}
      <FuseCompetitiveModal
        isOpen={showFuseModal}
        onClose={() => setShowFuseModal(false)}
        gameType="rock-paper-scissors"
        onGameStart={startFuseCompetitiveGame}
      />

      {/* Game Rules */}
      <div className="mt-6 text-xs text-gray-500 text-center">
        <p>Rock beats Scissors • Paper beats Rock • Scissors beats Paper</p>
        {isCompetitiveMode && (
          <p className="mt-1">Competitive: First to 5 wins or complete 10 rounds</p>
        )}
      </div>
    </div>
  )
}
