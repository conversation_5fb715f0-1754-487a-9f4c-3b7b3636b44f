'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { X, Wallet, Trophy, Users, Clock, DollarSign } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useGaming } from '@/contexts/gaming-context'

interface CompetitiveGameModalProps {
  isOpen: boolean
  onClose: () => void
  gameType: 'fuse-bird' | 'rock-paper-scissors' | '2048'
  onStartGame: (sessionId: string, entryFee: number) => void
}

const ENTRY_TIERS = [
  {
    id: 'bronze',
    name: 'Bronze League',
    entryFee: 2,
    prizeMultiplier: 1.6,
    description: 'Entry level competition',
    color: 'from-amber-600 to-amber-800',
    icon: '🥉'
  },
  {
    id: 'silver',
    name: 'Silver League',
    entryFee: 10,
    prizeMultiplier: 1.7,
    description: 'Intermediate competition',
    color: 'from-gray-400 to-gray-600',
    icon: '🥈'
  },
  {
    id: 'gold',
    name: 'Gold League',
    entryFee: 50,
    prizeMultiplier: 1.8,
    description: 'High stakes competition',
    color: 'from-yellow-400 to-yellow-600',
    icon: '🥇'
  }
]

export function CompetitiveGameModal({ isOpen, onClose, gameType, onStartGame }: CompetitiveGameModalProps) {
  const { user } = useAuth()
  const {
    leaderboards,
    startCompetitiveGame,
    isLoading
  } = useGaming()
  
  const [selectedTier, setSelectedTier] = useState<string>('bronze')
  const [step, setStep] = useState<'select' | 'manual-send' | 'success'>('select')
  const [sessionId, setSessionId] = useState<string | null>(null)

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setStep('select')
      setSelectedTier('bronze')
      setSessionId(null)
    }
  }, [isOpen])

  // Get relevant leaderboards for this game type
  const gameLeaderboards = leaderboards.filter(lb => 
    lb.game_type === gameType && 
    lb.entry_fee_tier !== 'free' &&
    lb.leaderboard_type === 'daily'
  )

  const selectedTierData = ENTRY_TIERS.find(tier => tier.id === selectedTier)
  const relevantLeaderboard = gameLeaderboards.find(lb => 
    lb.entry_fee_tier === selectedTier
  )

  const handleStartCompetitive = async () => {
    if (!selectedTierData || !user) return

    try {
      // Start the game session
      const newSessionId = await startCompetitiveGame(gameType, selectedTierData.entryFee)
      if (!newSessionId) {
        throw new Error('Failed to create game session')
      }

      setSessionId(newSessionId)
      setStep('manual-send')
    } catch (error) {
      console.error('Error starting competitive game:', error)
      setStep('select')
    }
  }

  const handleManualSendComplete = () => {
    if (!sessionId) return

    setStep('success')
    // Start the actual game
    setTimeout(() => {
      onStartGame(sessionId, selectedTierData?.entryFee || 0)
      onClose()
    }, 2000)
  }

  const renderSelectTier = () => (
    <div className="p-6">
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          🏆 Competitive {gameType === '2048' ? '2048' : gameType === 'fuse-bird' ? 'Fuse Bird' : 'Rock Paper Scissors'}
        </h3>
        <p className="text-gray-600">
          Pay entry fee to compete on leaderboards and win XRP prizes!
        </p>
      </div>

      <div className="space-y-4 mb-6">
        {ENTRY_TIERS.map((tier) => {
          const leaderboard = gameLeaderboards.find(lb => lb.entry_fee_tier === tier.id)
          
          return (
            <motion.div
              key={tier.id}
              className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all ${
                selectedTier === tier.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedTier(tier.id)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">{tier.icon}</div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{tier.name}</h4>
                    <p className="text-sm text-gray-600">{tier.description}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-lg">{tier.entryFee} XRP</div>
                  <div className="text-sm text-gray-600">
                    Prize Pool: {leaderboard?.prize_pool_xrp.toFixed(2) || '0.00'} XRP
                  </div>
                  <div className="text-xs text-gray-500">
                    {leaderboard?.total_entries || 0} players
                  </div>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {!user ? (
        <div className="text-center">
          <p className="text-gray-600 mb-4">Please sign in to play competitive games</p>
          <Button disabled className="w-full">
            Sign In Required
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center text-blue-800">
              <Trophy className="h-4 w-4 mr-2" />
              <span className="text-sm">
                Ready to compete! Manual payment required.
              </span>
            </div>
          </div>

          <Button
            onClick={handleStartCompetitive}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            disabled={isLoading}
          >
            {isLoading ? 'Starting...' : `Continue to Payment Instructions`}
          </Button>
        </div>
      )}
    </div>
  )

  const renderManualSend = () => (
    <div className="p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <DollarSign className="h-8 w-8 text-yellow-600" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-2">Manual Payment Required</h3>
        <p className="text-gray-600">
          Send XRP manually to participate in competitive gaming
        </p>
      </div>

      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="text-sm text-gray-600 mb-2">Entry Fee</div>
        <div className="text-2xl font-bold text-gray-900 mb-4">{selectedTierData?.entryFee} XRP</div>

        <div className="text-left space-y-3">
          <div>
            <div className="text-sm font-medium text-gray-700">Send to:</div>
            <div className="bg-white p-2 rounded border text-xs font-mono break-all">
              rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx
            </div>
          </div>

          <div>
            <div className="text-sm font-medium text-gray-700">Memo:</div>
            <div className="bg-white p-2 rounded border text-xs">
              Fuse.vip {gameType} {selectedTier} competition entry
            </div>
          </div>

          <div>
            <div className="text-sm font-medium text-gray-700">Session ID:</div>
            <div className="bg-white p-2 rounded border text-xs font-mono">
              {sessionId}
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-3">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="text-sm text-yellow-800">
            <strong>Important:</strong> Include the memo and session ID to ensure your payment is properly credited.
          </div>
        </div>

        <Button
          onClick={handleManualSendComplete}
          className="w-full bg-green-600 hover:bg-green-700"
        >
          I've Sent the Payment - Start Game
        </Button>

        <Button
          onClick={() => setStep('select')}
          variant="outline"
          className="w-full"
        >
          Back to Tier Selection
        </Button>
      </div>
    </div>
  )



  const renderSuccess = () => (
    <div className="p-6 text-center">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <Trophy className="h-8 w-8 text-green-600" />
      </div>
      <h3 className="text-xl font-bold text-gray-900 mb-2">Game Starting!</h3>
      <p className="text-gray-600 mb-4">
        Your competitive game is starting now...
      </p>
      <Badge className="bg-green-100 text-green-800">
        Good luck! 🍀
      </Badge>
    </div>
  )

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all">
                <div className="relative">
                  <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-full p-2 z-10"
                    aria-label="Close"
                  >
                    <X className="h-5 w-5" />
                  </button>

                  <AnimatePresence mode="wait">
                    <motion.div
                      key={step}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.2 }}
                    >
                      {step === 'select' && renderSelectTier()}
                      {step === 'manual-send' && renderManualSend()}
                      {step === 'success' && renderSuccess()}
                    </motion.div>
                  </AnimatePresence>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
