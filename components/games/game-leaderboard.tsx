'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Trophy, Medal, Award, Users, DollarSign, Clock, TrendingUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useGaming } from '@/contexts/gaming-context'
import { useAuth } from '@/contexts/auth-context'

interface GameLeaderboardProps {
  gameType: 'fuse-bird' | 'rock-paper-scissors' | '2048'
  onOpenCompetitive?: () => void
}

const TIER_COLORS = {
  free: 'from-gray-400 to-gray-600',
  bronze: 'from-amber-600 to-amber-800',
  silver: 'from-gray-400 to-gray-600',
  gold: 'from-yellow-400 to-yellow-600',
  platinum: 'from-purple-400 to-purple-600'
}

const TIER_ICONS = {
  free: '🎮',
  bronze: '🥉',
  silver: '🥈',
  gold: '🥇',
  platinum: '💎'
}

export function GameLeaderboard({ gameType, onOpenCompetitive }: GameLeaderboardProps) {
  const { user } = useAuth()
  const { 
    leaderboards, 
    leaderboardEntries, 
    fetchLeaderboards, 
    fetchLeaderboardEntries,
    isLoading 
  } = useGaming()
  
  const [selectedPeriod, setSelectedPeriod] = useState<'daily' | 'weekly' | 'monthly' | 'all-time'>('daily')
  const [selectedTier, setSelectedTier] = useState<'free' | 'bronze' | 'silver' | 'gold'>('free')

  // Filter leaderboards for this game type
  const gameLeaderboards = leaderboards.filter(lb => 
    lb.game_type === gameType &&
    lb.leaderboard_type === selectedPeriod &&
    lb.entry_fee_tier === selectedTier
  )

  const currentLeaderboard = gameLeaderboards[0]
  const currentEntries = currentLeaderboard ? leaderboardEntries[currentLeaderboard.id] || [] : []

  // Fetch data on mount and when filters change
  useEffect(() => {
    fetchLeaderboards(gameType)
  }, [gameType])

  useEffect(() => {
    if (currentLeaderboard) {
      fetchLeaderboardEntries(currentLeaderboard.id)
    }
  }, [currentLeaderboard?.id])

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return '🥇'
      case 2: return '🥈'
      case 3: return '🥉'
      default: return `#${rank}`
    }
  }

  const formatUserName = (entry: any) => {
    const profile = entry.user_profile
    if (profile?.first_name || profile?.last_name) {
      return `${profile.first_name || ''} ${profile.last_name || ''}`.trim()
    }
    return `Player ${entry.user_id.substring(0, 8)}`
  }

  const isCurrentUser = (entry: any) => {
    return user?.id === entry.user_id
  }

  return (
    <div className="w-full max-w-4xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <Trophy className="h-6 w-6" />
              {gameType === '2048' ? '2048' : gameType === 'fuse-bird' ? 'Fuse Bird' : 'Rock Paper Scissors'} Leaderboard
            </h2>
            <p className="text-blue-100 mt-1">
              Compete with players worldwide and win XRP prizes!
            </p>
          </div>
          {onOpenCompetitive && (
            <Button 
              onClick={onOpenCompetitive}
              className="bg-white text-blue-600 hover:bg-blue-50"
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Play Competitive
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="p-6 border-b">
        <Tabs value={selectedPeriod} onValueChange={(value: any) => setSelectedPeriod(value)}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="daily">Daily</TabsTrigger>
            <TabsTrigger value="weekly">Weekly</TabsTrigger>
            <TabsTrigger value="monthly">Monthly</TabsTrigger>
            <TabsTrigger value="all-time">All Time</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="mt-4">
          <div className="flex gap-2 flex-wrap">
            {['free', 'bronze', 'silver', 'gold'].map((tier) => (
              <Button
                key={tier}
                variant={selectedTier === tier ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedTier(tier as any)}
                className="capitalize"
              >
                {TIER_ICONS[tier as keyof typeof TIER_ICONS]} {tier}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Leaderboard Stats */}
      {currentLeaderboard && (
        <div className="p-6 bg-gray-50 border-b">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {currentLeaderboard.total_entries}
              </div>
              <div className="text-sm text-gray-600 flex items-center justify-center gap-1">
                <Users className="h-4 w-4" />
                Total Players
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {currentLeaderboard.prize_pool_xrp.toFixed(2)} XRP
              </div>
              <div className="text-sm text-gray-600 flex items-center justify-center gap-1">
                <DollarSign className="h-4 w-4" />
                Prize Pool
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {currentLeaderboard.entry_fee_tier === 'free' ? 'Free' : `${currentLeaderboard.min_entry_fee} XRP`}
              </div>
              <div className="text-sm text-gray-600 flex items-center justify-center gap-1">
                <Award className="h-4 w-4" />
                Entry Fee
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Leaderboard Entries */}
      <div className="p-6">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Loading leaderboard...</p>
          </div>
        ) : currentEntries.length === 0 ? (
          <div className="text-center py-8">
            <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No entries yet</h3>
            <p className="text-gray-600 mb-4">
              Be the first to compete in this leaderboard!
            </p>
            {onOpenCompetitive && selectedTier !== 'free' && (
              <Button onClick={onOpenCompetitive}>
                Start Competing
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            {currentEntries.map((entry, index) => (
              <motion.div
                key={entry.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className={`flex items-center justify-between p-4 rounded-lg border transition-all ${
                  isCurrentUser(entry)
                    ? 'bg-blue-50 border-blue-200 ring-2 ring-blue-500 ring-opacity-20'
                    : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                }`}
              >
                <div className="flex items-center space-x-4">
                  <div className="text-2xl font-bold min-w-[3rem] text-center">
                    {getRankIcon(entry.rank)}
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 flex items-center gap-2">
                      {formatUserName(entry)}
                      {isCurrentUser(entry) && (
                        <Badge variant="secondary" className="text-xs">You</Badge>
                      )}
                    </div>
                    <div className="text-sm text-gray-600">
                      Score: {entry.score.toLocaleString()}
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  {entry.prize_amount_xrp > 0 && (
                    <div className="text-green-600 font-semibold">
                      +{entry.prize_amount_xrp.toFixed(2)} XRP
                    </div>
                  )}
                  <div className="text-xs text-gray-500">
                    {new Date(entry.created_at).toLocaleDateString()}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-gray-50 px-6 py-4 text-center">
        <p className="text-sm text-gray-600">
          {selectedTier === 'free' 
            ? 'Free leaderboards are for fun - no entry fees or prizes'
            : 'Competitive leaderboards require entry fees and offer XRP prizes'
          }
        </p>
      </div>
    </div>
  )
}
