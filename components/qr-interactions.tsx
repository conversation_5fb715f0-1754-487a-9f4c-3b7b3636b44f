"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { supabase } from '@/lib/supabase';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, Building2, Calendar, ArrowRight, RefreshCw } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface QRInteraction {
  id: string;
  scanner_user_id: string;
  scanned_user_id: string | null;
  scanned_business_id: string | null;
  interaction_type: 'user_scan' | 'business_scan';
  created_at: string;
  scanner_profile?: {
    first_name: string;
    last_name: string;
    user_email: string;
    is_card_holder: boolean;
  };
  scanned_profile?: {
    first_name: string;
    last_name: string;
    user_email: string;
    is_card_holder: boolean;
  };
  business?: {
    name: string;
    category: string;
    logo_url: string;
  };
}

interface QRInteractionsProps {
  limit?: number;
  showTitle?: boolean;
  className?: string;
}

export function QRInteractions({ 
  limit = 10, 
  showTitle = true, 
  className = "" 
}: QRInteractionsProps) {
  const { user } = useAuth();
  const [interactions, setInteractions] = useState<QRInteraction[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchInteractions = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      // Fetch interactions where user is either scanner or scanned
      const { data, error } = await supabase
        .from('qr_interactions')
        .select(`
          id,
          scanner_user_id,
          scanned_user_id,
          scanned_business_id,
          interaction_type,
          created_at
        `)
        .or(`scanner_user_id.eq.${user.id},scanned_user_id.eq.${user.id}`)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching QR interactions:', error);
        setInteractions([]);
        return;
      }

      // Fetch related profiles and businesses separately
      const interactionsWithDetails = await Promise.all(
        (data || []).map(async (interaction) => {
          const result: QRInteraction = { ...interaction };

          // Fetch scanner profile
          if (interaction.scanner_user_id) {
            const { data: scannerProfile } = await supabase
              .from('profiles')
              .select('first_name, last_name, user_email, is_card_holder')
              .eq('id', interaction.scanner_user_id)
              .single();
            result.scanner_profile = scannerProfile;
          }

          // Fetch scanned user profile
          if (interaction.scanned_user_id) {
            const { data: scannedProfile } = await supabase
              .from('profiles')
              .select('first_name, last_name, user_email, is_card_holder')
              .eq('id', interaction.scanned_user_id)
              .single();
            result.scanned_profile = scannedProfile;
          }

          // Fetch business details
          if (interaction.scanned_business_id) {
            const { data: business } = await supabase
              .from('businesses')
              .select('name, category, logo_url')
              .eq('id', interaction.scanned_business_id)
              .single();
            result.business = business;
          }

          return result;
        })
      );

      setInteractions(interactionsWithDetails);


    } catch (error) {
      console.error('Error fetching QR interactions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchInteractions();
    }
  }, [user, limit]);

  const getInteractionDescription = (interaction: QRInteraction) => {
    const isScanner = interaction.scanner_user_id === user?.id;
    
    if (interaction.interaction_type === 'business_scan') {
      if (isScanner) {
        return {
          action: 'You visited',
          target: interaction.business?.name || 'Unknown Business',
          icon: Building2,
          color: 'bg-blue-100 text-blue-800'
        };
      } else {
        return {
          action: 'Someone visited your business',
          target: interaction.business?.name || 'Your Business',
          icon: Building2,
          color: 'bg-green-100 text-green-800'
        };
      }
    } else {
      if (isScanner) {
        const targetName = interaction.scanned_profile 
          ? `${interaction.scanned_profile.first_name} ${interaction.scanned_profile.last_name}`
          : 'Unknown User';
        return {
          action: 'You connected with',
          target: targetName,
          icon: Users,
          color: 'bg-purple-100 text-purple-800'
        };
      } else {
        const scannerName = interaction.scanner_profile 
          ? `${interaction.scanner_profile.first_name} ${interaction.scanner_profile.last_name}`
          : 'Someone';
        return {
          action: 'Connected with you',
          target: scannerName,
          icon: Users,
          color: 'bg-orange-100 text-orange-800'
        };
      }
    }
  };

  if (!user) {
    return null;
  }

  return (
    <Card className={`w-full ${className}`}>
      {showTitle && (
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <ArrowRight className="h-5 w-5" />
              Recent QR Interactions
            </span>
            {isLoading && <RefreshCw className="h-4 w-4 animate-spin" />}
          </CardTitle>
        </CardHeader>
      )}
      
      <CardContent>
        {isLoading ? (
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : interactions.length === 0 ? (
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-2">No QR interactions yet</p>
            <p className="text-sm text-gray-400">
              Share your QR code or scan others to see interactions here
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {interactions.map((interaction) => {
              const description = getInteractionDescription(interaction);
              const IconComponent = description.icon;
              
              return (
                <div key={interaction.id} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className={`p-2 rounded-full ${description.color}`}>
                    <IconComponent className="h-4 w-4" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      {description.action} <span className="font-semibold">{description.target}</span>
                    </p>
                    
                    <div className="flex items-center gap-2 mt-1">
                      <p className="text-xs text-gray-500 flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {formatDistanceToNow(new Date(interaction.created_at), { addSuffix: true })}
                      </p>
                      
                      {interaction.interaction_type === 'user_scan' && (
                        <Badge variant="secondary" className="text-xs">
                          User Connection
                        </Badge>
                      )}
                      
                      {interaction.interaction_type === 'business_scan' && (
                        <Badge variant="outline" className="text-xs">
                          Business Visit
                        </Badge>
                      )}
                      
                      {/* Show VIP badge if applicable */}
                      {interaction.scanned_profile?.is_card_holder && (
                        <Badge className="text-xs bg-orange-100 text-orange-800">
                          VIP
                        </Badge>
                      )}
                      
                      {interaction.scanner_profile?.is_card_holder && interaction.scanner_user_id !== user.id && (
                        <Badge className="text-xs bg-orange-100 text-orange-800">
                          VIP
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
