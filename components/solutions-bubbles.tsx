"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface BubbleProps {
  title: string
  content: React.ReactNode
  color: string
  delay: number
  className?: string
}

const Bubble = ({ title, content, color, delay, className }: BubbleProps) => {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <motion.div
      className={cn(
        "relative p-6 rounded-xl overflow-hidden transition-all duration-500",
        "shadow-[0_0_15px_rgba(0,0,0,0.1)] backdrop-blur-sm",
        "transform perspective-1000",
        className,
      )}
      style={{
        background: `linear-gradient(135deg, ${color}66, ${color}88)`, // Darkened background
        boxShadow: isHovered ? `0 0 25px ${color}88` : `0 0 15px ${color}44`,
        border: `1px solid ${color}33`,
      }}
      initial={{ opacity: 0, y: 50, rotateX: -10 }}
      animate={{ opacity: 1, y: 0, rotateX: 0 }}
      transition={{
        duration: 0.8,
        delay: delay * 0.2,
        ease: "easeOut",
      }}
      whileHover={{
        scale: 1.03,
        rotateY: 5,
        boxShadow: `0 0 30px ${color}aa`,
        transition: { duration: 0.3 },
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <motion.div
        className="absolute inset-0 opacity-20"
        style={{
          background: `radial-gradient(circle at ${isHovered ? "60%" : "40%"} ${isHovered ? "40%" : "60%"}, ${color}88, transparent 70%)`,
        }}
        animate={{
          opacity: isHovered ? 0.4 : 0.2,
        }}
        transition={{ duration: 1 }}
      />

      <div className="relative z-10">
        <h3 className="text-xl font-bold text-white drop-shadow-md mb-3">{title}</h3>
        <div className="text-white/90 text-sm leading-relaxed">{content}</div>
      </div>

      <motion.div
        className="absolute bottom-0 right-0 w-24 h-24 rounded-full opacity-20"
        style={{ background: color }}
        animate={{
          scale: isHovered ? 1.2 : 1,
          x: isHovered ? -5 : 10,
          y: isHovered ? -5 : 10,
        }}
        transition={{ duration: 1 }}
      />
    </motion.div>
  )
}

export function SolutionsBubbles() {
  const bubbles = [
    {
      title: "Full-Stack Growth Assistant",
      color: "#3A56FF",
      content: (
        <>
          <p className="mb-2">No more guessing.</p>
          <p className="mb-2">No more $3,000/month agency retainers.</p>
          <p className="mb-2">No more duct-taping 17 platforms together.</p>
          <p className="font-bold">
            FUSE is your full-stack growth assistant — made <em>specifically</em> for small businesses.
          </p>
        </>
      ),
    },
    {
      title: "Seamless Integration",
      color: "#FF3A2F",
      content: (
        <>
          <p className="mb-2">And this isn't just talk.</p>
          <p className="mb-2">We're integrating the best tools in:</p>
          <p className="mb-2">Copywriting</p>
          <p className="mb-2">Content creation</p>
          <p className="mb-2">Sales optimization</p>
          <p className="mb-2">Chat + follow-ups</p>
          <p className="mb-2">AI automation</p>
          <p className="font-bold">FUSE combines the best of modern tech into one seamless interface.</p>
        </>
      ),
    },
    {
      title: "We Simplify",
      color: "#FFD700",
      content: (
        <>
          <p className="mb-2">We don't overwhelm.</p>
          <p className="mb-2">We simplify.</p>
          <p className="mb-2">Businesses shouldn't have to become software experts to thrive.</p>
          <p className="font-bold">FUSE does the work so they can get back to doing what they love.</p>
        </>
      ),
    },
    {
      title: "One Tool. One Card.",
      color: "#00C2A8",
      content: (
        <>
          <p className="mb-2">One tool. One card.</p>
          <p className="mb-2">
            Every small business deserves the engine of a Fortune 500 company — with the feel of a local handshake.
          </p>
          <p className="font-bold">That's what FUSE.vip is building.</p>
        </>
      ),
    },
    {
      title: "Join the Movement",
      color: "#9747FF",
      content: (
        <>
          <p className="mb-2">We're not just here to compete.</p>
          <p className="mb-2">We're here to redefine how business gets done in America.</p>
          <p className="mb-2">Join the movement.</p>
          <p className="font-bold">Let's fuse tech, community, and prosperity — one business at a time.</p>
        </>
      ),
    },
  ]

  return (
    <div className="py-12 relative overflow-hidden">
      {/* Background glow effects */}
      <div className="absolute top-0 left-1/4 w-1/2 h-1/2 bg-[#3A56FF] opacity-10 blur-[100px] rounded-full" />
      <div className="absolute bottom-0 right-1/4 w-1/2 h-1/2 bg-[#FF3A2F] opacity-10 blur-[100px] rounded-full" />

      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#3A56FF] to-[#FF3A2F]">
            The FUSE Solution
          </span>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* First row */}
          <Bubble
            title={bubbles[0].title}
            content={bubbles[0].content}
            color={bubbles[0].color}
            delay={0}
            className="lg:col-span-2"
          />
          <Bubble
            title={bubbles[1].title}
            content={bubbles[1].content}
            color={bubbles[1].color}
            delay={1}
            className="lg:col-span-1"
          />

          {/* Second row */}
          <Bubble
            title={bubbles[2].title}
            content={bubbles[2].content}
            color={bubbles[2].color}
            delay={2}
            className="lg:col-span-1"
          />
          <Bubble
            title={bubbles[3].title}
            content={bubbles[3].content}
            color={bubbles[3].color}
            delay={3}
            className="lg:col-span-2"
          />

          {/* Third row - centered */}
          <div className="md:col-span-2 lg:col-span-3 flex justify-center">
            <div className="w-full md:w-2/3 lg:w-1/2">
              <Bubble
                title={bubbles[4].title}
                content={bubbles[4].content}
                color={bubbles[4].color}
                delay={4}
                className=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
