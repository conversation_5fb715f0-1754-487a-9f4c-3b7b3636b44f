"use client";

import { useEffect, useRef, useState } from 'react';
import { Html5QrcodeScanner } from 'html5-qrcode';

interface QRCodeScannerProps {
  onScanSuccess: (decodedText: string, decodedResult: any) => void;
  onScanError?: (error: string) => void;
  fps?: number;
  qrbox?: number | { width: number; height: number };
  aspectRatio?: number;
  disableFlip?: boolean;
}

export default function QRCodeScanner({ 
  onScanSuccess, 
  onScanError,
  fps = 10,
  qrbox = 250,
  aspectRatio = 1.0,
  disableFlip = false
}: QRCodeScannerProps) {
  const readerRef = useRef<HTMLDivElement>(null);
  const scannerRef = useRef<Html5QrcodeScanner | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window === "undefined") return;

    const startScanner = async () => {
      try {
        setIsScanning(true);
        setError(null);

        // Dynamically import to avoid SSR issues
        const { Html5QrcodeScanner } = await import('html5-qrcode');
        
        scannerRef.current = new Html5QrcodeScanner(
          'qr-reader',
          { 
            fps,
            qrbox,
            aspectRatio,
            disableFlip,
            // Request camera permissions explicitly
            rememberLastUsedCamera: true,
            // Show torch button if available
            showTorchButtonIfSupported: true,
            // Show zoom slider if available
            showZoomSliderIfSupported: true,
          },
          false // verbose logging
        );

        scannerRef.current.render(
          (decodedText, decodedResult) => {
            onScanSuccess(decodedText, decodedResult);
          },
          (error) => {
            // Only log errors, don't show them to user as they're frequent during scanning
            console.warn("QR Scan error:", error);
            if (onScanError) {
              onScanError(error);
            }
          }
        );
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize camera';
        setError(errorMessage);
        if (onScanError) {
          onScanError(errorMessage);
        }
      }
    };

    startScanner();

    // Cleanup function to stop the camera when unmounting
    return () => {
      if (scannerRef.current) {
        scannerRef.current.clear().catch((err) => {
          console.error("Error clearing scanner:", err);
        });
      }
    };
  }, [onScanSuccess, onScanError, fps, qrbox, aspectRatio, disableFlip]);

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="text-red-500 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Camera Access Required</h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <p className="text-sm text-gray-500">
          Please allow camera access in your browser settings and refresh the page.
        </p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div 
        id="qr-reader" 
        ref={readerRef} 
        className="w-full"
        style={{ 
          minHeight: '300px',
          // Ensure the scanner takes full width
          width: '100%'
        }} 
      />
      {isScanning && (
        <div className="mt-4 text-center">
          <p className="text-sm text-gray-600">
            Position the QR code within the frame to scan
          </p>
        </div>
      )}
    </div>
  );
}
