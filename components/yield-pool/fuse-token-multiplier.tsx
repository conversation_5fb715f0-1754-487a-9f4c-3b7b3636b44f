"use client";

import { motion } from "framer-motion";
import { useState } from "react";
import { 
  Flame, 
  Zap, 
  TrendingUp, 
  Star,
  Spark<PERSON>,
  Clock,
  ArrowRight,
  Gift,
  Target,
  Crown
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface MultiplierTier {
  id: string;
  name: string;
  fuseRequired: number;
  multiplier: number;
  color: string;
  icon: React.ReactNode;
  benefits: string[];
  comingSoon?: boolean;
}

const MULTIPLIER_TIERS: MultiplierTier[] = [
  {
    id: 'bronze',
    name: 'Bronze Boost',
    fuseRequired: 1000,
    multiplier: 1.25,
    color: 'from-orange-600 to-orange-400',
    icon: <Flame className="h-5 w-5" />,
    benefits: [
      '25% yield multiplier',
      'Priority grid bot execution',
      'Basic analytics dashboard'
    ]
  },
  {
    id: 'silver',
    name: 'Silver Surge',
    fuseRequired: 5000,
    multiplier: 1.5,
    color: 'from-gray-400 to-gray-200',
    icon: <Zap className="h-5 w-5" />,
    benefits: [
      '50% yield multiplier',
      'Advanced grid strategies',
      'Weekly performance reports',
      'Early access to new features'
    ]
  },
  {
    id: 'gold',
    name: 'Gold Rush',
    fuseRequired: 15000,
    multiplier: 2.0,
    color: 'from-yellow-500 to-yellow-300',
    icon: <Star className="h-5 w-5" />,
    benefits: [
      '100% yield multiplier',
      'Premium grid algorithms',
      'Daily performance insights',
      'VIP customer support',
      'Exclusive strategy access'
    ]
  },
  {
    id: 'platinum',
    name: 'Platinum Power',
    fuseRequired: 50000,
    multiplier: 3.0,
    color: 'from-purple-500 to-purple-300',
    icon: <Crown className="h-5 w-5" />,
    benefits: [
      '200% yield multiplier',
      'AI-powered grid optimization',
      'Real-time strategy adjustments',
      'Personal yield strategist',
      'Beta feature access',
      'Governance voting rights'
    ],
    comingSoon: true
  }
];

interface FuseTokenMultiplierProps {
  currentYield?: number;
  depositAmount?: number;
  assetType?: 'XRP' | 'USDC';
  selectedSchedule?: {
    apy: number;
    duration: number;
  };
}

export function FuseTokenMultiplier({ 
  currentYield = 6.8, 
  depositAmount = 0,
  assetType = 'XRP',
  selectedSchedule 
}: FuseTokenMultiplierProps) {
  const [selectedTier, setSelectedTier] = useState<string | null>(null);
  const [showComingSoon, setShowComingSoon] = useState(true);

  const calculateBoostedYield = (tier: MultiplierTier) => {
    const baseAPY = selectedSchedule?.apy || currentYield;
    return baseAPY * tier.multiplier;
  };

  const calculateBoostedEarnings = (tier: MultiplierTier) => {
    if (!depositAmount || !selectedSchedule) return 0;
    const boostedAPY = calculateBoostedYield(tier);
    return (depositAmount * boostedAPY / 365 * selectedSchedule.duration) / 100;
  };

  const getOriginalEarnings = () => {
    if (!depositAmount || !selectedSchedule) return 0;
    const baseAPY = selectedSchedule.apy;
    return (depositAmount * baseAPY / 365 * selectedSchedule.duration) / 100;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Coming Soon Header */}
      {showComingSoon && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="relative overflow-hidden"
        >
          <Card className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 border-purple-500/50 backdrop-blur-sm relative">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-pink-500/5 to-purple-500/5 animate-pulse"></div>
            <CardHeader className="relative">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white flex items-center gap-2">
                  <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full">
                    <Flame className="h-6 w-6 text-white animate-pulse" />
                  </div>
                  Burn FUSE Tokens for Yield Multipliers
                  <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white border-0 animate-bounce shadow-lg">
                    Coming Soon
                  </Badge>
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowComingSoon(false)}
                  className="text-white/70 hover:text-white"
                >
                  ✕
                </Button>
              </div>
              <CardDescription className="text-white/80">
                Burn FUSE tokens to unlock powerful yield multipliers and exclusive grid bot features. 
                The more you burn, the higher your rewards!
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert className="bg-white/10 border-white/20 backdrop-blur-sm">
                <Sparkles className="h-4 w-4 text-yellow-400" />
                <AlertTitle className="text-white font-semibold">Early Access Preview</AlertTitle>
                <AlertDescription className="text-white/90">
                  Get ready to supercharge your yields! FUSE token burning will launch soon with
                  multipliers up to 3x your current earnings. Start planning your strategy now.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Multiplier Tiers */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {MULTIPLIER_TIERS.map((tier, index) => (
          <motion.div
            key={tier.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1, duration: 0.5 }}
            whileHover={{ scale: 1.02 }}
            className="relative"
          >
            <Card 
              className={`cursor-pointer transition-all duration-300 ${
                selectedTier === tier.id
                  ? 'ring-2 ring-purple-500/50 shadow-lg shadow-purple-500/20'
                  : 'hover:shadow-lg hover:shadow-white/10'
              } bg-gradient-to-br from-gray-900/80 to-gray-800/80 border-gray-700/50 backdrop-blur-sm relative overflow-hidden`}
              onClick={() => setSelectedTier(selectedTier === tier.id ? null : tier.id)}
            >
              {/* Tier Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${tier.color} opacity-10`}></div>
              
              {/* Coming Soon Badge */}
              {tier.comingSoon && (
                <div className="absolute top-2 right-2 z-10">
                  <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 text-xs">
                    Soon
                  </Badge>
                </div>
              )}
              
              <CardHeader className="relative pb-3">
                <div className="flex items-center gap-3">
                  <div className={`p-2 bg-gradient-to-r ${tier.color} rounded-full text-white`}>
                    {tier.icon}
                  </div>
                  <div>
                    <CardTitle className="text-white text-lg">{tier.name}</CardTitle>
                    <CardDescription className="text-white/70 text-sm">
                      {tier.fuseRequired.toLocaleString()} FUSE to burn
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="relative space-y-4">
                {/* Multiplier Display */}
                <div className="text-center">
                  <div className={`text-3xl font-bold bg-gradient-to-r ${tier.color} bg-clip-text text-transparent`}>
                    {tier.multiplier}x
                  </div>
                  <div className="text-white/70 text-sm">Yield Multiplier</div>
                </div>

                {/* Boosted APY Preview */}
                {selectedSchedule && (
                  <div className="bg-white/10 rounded-lg p-3 text-center">
                    <div className="text-sm text-white/70 mb-1">Boosted APY</div>
                    <div className="text-xl font-bold text-green-400">
                      {calculateBoostedYield(tier).toFixed(1)}%
                    </div>
                    <div className="text-xs text-white/50">
                      vs {selectedSchedule.apy}% base
                    </div>
                  </div>
                )}

                {/* Benefits List */}
                <div className="space-y-2">
                  <div className="text-sm font-medium text-white/80">Benefits:</div>
                  <ul className="space-y-1">
                    {tier.benefits.slice(0, 3).map((benefit, idx) => (
                      <li key={idx} className="text-xs text-white/70 flex items-start gap-2">
                        <div className="w-1 h-1 bg-green-400 rounded-full mt-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                    {tier.benefits.length > 3 && (
                      <li className="text-xs text-white/50 italic">
                        +{tier.benefits.length - 3} more benefits
                      </li>
                    )}
                  </ul>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Selected Tier Details */}
      {selectedTier && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          {(() => {
            const tier = MULTIPLIER_TIERS.find(t => t.id === selectedTier);
            if (!tier) return null;

            return (
              <Card className="bg-gradient-to-br from-gray-900/60 to-gray-800/60 border-gray-700/30 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <div className={`p-2 bg-gradient-to-r ${tier.color} rounded-full text-white`}>
                      {tier.icon}
                    </div>
                    {tier.name} - Detailed Preview
                  </CardTitle>
                  <CardDescription className="text-white/70">
                    See how burning {tier.fuseRequired.toLocaleString()} FUSE tokens will boost your earnings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Earnings Comparison */}
                  {depositAmount > 0 && selectedSchedule && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white/10 rounded-lg p-4">
                        <div className="text-sm text-white/70 mb-2">Current Earnings</div>
                        <div className="text-2xl font-bold text-white">
                          {getOriginalEarnings().toFixed(4)} {assetType}
                        </div>
                        <div className="text-xs text-white/50">
                          {selectedSchedule.apy}% APY over {selectedSchedule.duration} days
                        </div>
                      </div>
                      <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 border border-green-500/30 rounded-lg p-4">
                        <div className="text-sm text-green-400 mb-2">Boosted Earnings</div>
                        <div className="text-2xl font-bold text-green-400">
                          {calculateBoostedEarnings(tier).toFixed(4)} {assetType}
                        </div>
                        <div className="text-xs text-green-300">
                          {calculateBoostedYield(tier).toFixed(1)}% APY with {tier.multiplier}x boost
                        </div>
                        <div className="mt-2 text-sm text-green-400 font-medium">
                          +{(calculateBoostedEarnings(tier) - getOriginalEarnings()).toFixed(4)} {assetType} extra
                        </div>
                      </div>
                    </div>
                  )}

                  {/* All Benefits */}
                  <div>
                    <h4 className="text-white font-medium mb-3 flex items-center gap-2">
                      <Gift className="h-4 w-4 text-purple-400" />
                      Complete Benefits Package
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {tier.benefits.map((benefit, idx) => (
                        <div key={idx} className="flex items-center gap-2 text-sm text-white/70">
                          <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex-shrink-0" />
                          {benefit}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Call to Action */}
                  <Alert className="bg-gradient-to-r from-blue-900/30 to-indigo-900/30 border-blue-400/60">
                    <Clock className="h-4 w-4 text-blue-400" />
                    <AlertTitle className="text-white font-semibold">Coming Soon</AlertTitle>
                    <AlertDescription className="text-gray-100">
                      FUSE token burning and yield multipliers will be available soon.
                      Start accumulating FUSE tokens now to be ready for launch!
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            );
          })()}
        </motion.div>
      )}

      {/* How It Works */}
      <Card className="bg-gradient-to-br from-gray-900/40 to-gray-800/40 border-gray-700/30 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-400" />
            How FUSE Token Burning Works
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center space-y-3">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto">
                <span className="text-white font-bold">1</span>
              </div>
              <h4 className="text-white font-medium">Acquire FUSE Tokens</h4>
              <p className="text-gray-200 text-sm">
                Earn or purchase FUSE tokens through our ecosystem
              </p>
            </div>
            <div className="text-center space-y-3">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto">
                <span className="text-white font-bold">2</span>
              </div>
              <h4 className="text-white font-medium">Choose Your Tier</h4>
              <p className="text-gray-200 text-sm">
                Select the multiplier tier that matches your strategy
              </p>
            </div>
            <div className="text-center space-y-3">
              <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto">
                <span className="text-white font-bold">3</span>
              </div>
              <h4 className="text-white font-medium">Burn & Earn</h4>
              <p className="text-gray-200 text-sm">
                Burn tokens to activate multipliers and boost your yields
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
