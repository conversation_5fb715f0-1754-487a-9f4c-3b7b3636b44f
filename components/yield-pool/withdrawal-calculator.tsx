"use client";

import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { 
  AlertTriangle, 
  Calculator, 
  Clock, 
  TrendingDown,
  DollarSign,
  Calendar
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";

interface WithdrawalCalculatorProps {
  depositAmount: number;
  assetType: 'XRP' | 'USDC';
  depositDate: Date;
  maturityDate: Date;
  scheduleAPY: number;
  earlyWithdrawalPenalty: number;
  currentXRPPrice?: number;
  depositXRPPrice?: number;
}

export function WithdrawalCalculator({
  depositAmount,
  assetType,
  depositDate,
  maturityDate,
  scheduleAPY,
  earlyWithdrawalPenalty,
  currentXRPPrice = 0.52,
  depositXRPPrice = 0.50
}: WithdrawalCalculatorProps) {
  const [daysRemaining, setDaysRemaining] = useState(0);
  const [isEarlyWithdrawal, setIsEarlyWithdrawal] = useState(false);
  const [penaltyAmount, setPenaltyAmount] = useState(0);
  const [netWithdrawal, setNetWithdrawal] = useState(0);
  const [priceImpact, setPriceImpact] = useState(0);

  useEffect(() => {
    const now = new Date();
    const daysLeft = Math.max(0, Math.ceil((maturityDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
    const isEarly = daysLeft > 0;
    
    setDaysRemaining(daysLeft);
    setIsEarlyWithdrawal(isEarly);

    // Calculate penalty
    let penalty = 0;
    if (isEarly) {
      // Base penalty + time-based penalty
      const basePenalty = depositAmount * (earlyWithdrawalPenalty / 100);
      const timePenalty = basePenalty * (daysLeft / 30); // Scale with remaining time
      penalty = Math.min(basePenalty + timePenalty, depositAmount * 0.1); // Cap at 10%
    }
    setPenaltyAmount(penalty);

    // Calculate price impact for XRP
    let priceEffect = 0;
    if (assetType === 'XRP' && depositXRPPrice && currentXRPPrice) {
      const priceChange = (currentXRPPrice - depositXRPPrice) / depositXRPPrice;
      priceEffect = depositAmount * priceChange;
    }
    setPriceImpact(priceEffect);

    // Calculate net withdrawal amount
    const grossAmount = depositAmount + priceEffect;
    const netAmount = Math.max(0, grossAmount - penalty);
    setNetWithdrawal(netAmount);
  }, [depositAmount, maturityDate, earlyWithdrawalPenalty, assetType, currentXRPPrice, depositXRPPrice]);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getImpactColor = (amount: number) => {
    if (amount > 0) return 'text-green-400';
    if (amount < 0) return 'text-red-400';
    return 'text-white/70';
  };

  const getRiskLevel = () => {
    const totalLoss = penaltyAmount + Math.abs(Math.min(0, priceImpact));
    const lossPercentage = (totalLoss / depositAmount) * 100;
    
    if (lossPercentage < 2) return { level: 'LOW', color: 'text-green-400 bg-green-500/20' };
    if (lossPercentage < 5) return { level: 'MEDIUM', color: 'text-yellow-400 bg-yellow-500/20' };
    return { level: 'HIGH', color: 'text-red-400 bg-red-500/20' };
  };

  const riskLevel = getRiskLevel();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-4"
    >
      <Card className="bg-gradient-to-br from-gray-900/40 to-gray-800/40 border-gray-700/30 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Calculator className="h-5 w-5 text-blue-400" />
            Withdrawal Calculator
          </CardTitle>
          <CardDescription className="text-white/70">
            Understand the impact of withdrawing your {assetType} now
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Deposit Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white/10 rounded-lg p-3">
              <div className="text-sm text-white/70 mb-1">Original Deposit</div>
              <div className="text-lg font-bold text-white">
                {depositAmount.toLocaleString()} {assetType}
              </div>
              <div className="text-xs text-white/50">
                {formatDate(depositDate)}
              </div>
            </div>
            <div className="bg-white/10 rounded-lg p-3">
              <div className="text-sm text-white/70 mb-1">Maturity Date</div>
              <div className="text-lg font-bold text-white">
                {formatDate(maturityDate)}
              </div>
              <div className="text-xs text-white/50">
                {daysRemaining} days remaining
              </div>
            </div>
          </div>

          {/* Risk Assessment */}
          <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
            <span className="text-white/70">Withdrawal Risk Level:</span>
            <Badge className={`${riskLevel.color} border`}>
              {riskLevel.level}
            </Badge>
          </div>

          {/* Price Impact (XRP only) */}
          {assetType === 'XRP' && (
            <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <TrendingDown className="h-4 w-4 text-blue-400" />
                <span className="text-blue-400 font-medium">XRP Price Impact</span>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between text-white/70">
                  <span>Deposit price:</span>
                  <span>${depositXRPPrice?.toFixed(4)}</span>
                </div>
                <div className="flex justify-between text-white/70">
                  <span>Current price:</span>
                  <span>${currentXRPPrice.toFixed(4)}</span>
                </div>
                <div className="border-t border-blue-500/20 pt-2">
                  <div className="flex justify-between">
                    <span className="text-white/70">Price impact:</span>
                    <span className={`font-medium ${getImpactColor(priceImpact)}`}>
                      {priceImpact >= 0 ? '+' : ''}{priceImpact.toFixed(4)} {assetType}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Early Withdrawal Penalty */}
          {isEarlyWithdrawal && (
            <Alert className="bg-red-500/10 border-red-500/30">
              <AlertTriangle className="h-4 w-4 text-red-400" />
              <AlertTitle className="text-red-400">Early Withdrawal Penalty</AlertTitle>
              <AlertDescription className="text-white/70">
                <div className="space-y-2 mt-2">
                  <div className="flex justify-between text-sm">
                    <span>Base penalty ({earlyWithdrawalPenalty}%):</span>
                    <span className="text-red-400">
                      -{(depositAmount * earlyWithdrawalPenalty / 100).toFixed(4)} {assetType}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Time penalty ({daysRemaining} days):</span>
                    <span className="text-red-400">
                      -{(penaltyAmount - (depositAmount * earlyWithdrawalPenalty / 100)).toFixed(4)} {assetType}
                    </span>
                  </div>
                  <div className="border-t border-red-500/20 pt-2">
                    <div className="flex justify-between font-medium">
                      <span>Total penalty:</span>
                      <span className="text-red-400">
                        -{penaltyAmount.toFixed(4)} {assetType}
                      </span>
                    </div>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Final Calculation */}
          <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/30 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <DollarSign className="h-4 w-4 text-purple-400" />
              <span className="text-purple-400 font-medium">Net Withdrawal Amount</span>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between text-white/70">
                <span>Original deposit:</span>
                <span>{depositAmount.toFixed(4)} {assetType}</span>
              </div>
              {assetType === 'XRP' && (
                <div className="flex justify-between text-white/70">
                  <span>Price impact:</span>
                  <span className={getImpactColor(priceImpact)}>
                    {priceImpact >= 0 ? '+' : ''}{priceImpact.toFixed(4)} {assetType}
                  </span>
                </div>
              )}
              {isEarlyWithdrawal && (
                <div className="flex justify-between text-white/70">
                  <span>Early withdrawal penalty:</span>
                  <span className="text-red-400">-{penaltyAmount.toFixed(4)} {assetType}</span>
                </div>
              )}
              <div className="border-t border-purple-500/20 pt-2">
                <div className="flex justify-between text-lg font-bold">
                  <span className="text-white">You will receive:</span>
                  <span className={`${netWithdrawal >= depositAmount ? 'text-green-400' : 'text-red-400'}`}>
                    {netWithdrawal.toFixed(4)} {assetType}
                  </span>
                </div>
                <div className="text-xs text-white/50 mt-1">
                  {netWithdrawal >= depositAmount ? 'Profitable withdrawal' : 'Loss on withdrawal'}
                </div>
              </div>
            </div>
          </div>

          {/* Recommendation */}
          {isEarlyWithdrawal && (
            <Alert className="bg-yellow-500/10 border-yellow-500/30">
              <Clock className="h-4 w-4 text-yellow-400" />
              <AlertTitle className="text-yellow-400">Recommendation</AlertTitle>
              <AlertDescription className="text-white/70">
                {daysRemaining <= 7 ? (
                  `Consider waiting ${daysRemaining} more days to avoid the early withdrawal penalty and maximize your returns.`
                ) : (
                  `Withdrawing now will result in a penalty. The grid bot strategy works best over longer periods. Consider the ${daysRemaining}-day commitment you made.`
                )}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
