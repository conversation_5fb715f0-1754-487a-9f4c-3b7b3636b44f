"use client";

import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { 
  AlertTriangle, 
  Shield, 
  TrendingDown, 
  Zap, 
  Lock, 
  Eye,
  CheckCircle,
  XCircle,
  Info
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";

export function RiskDisclosure() {
  const [acknowledgedRisks, setAcknowledgedRisks] = useState<string[]>([]);
  const [showFullDisclosure, setShowFullDisclosure] = useState(false);

  // Load acknowledged risks from localStorage on component mount
  useEffect(() => {
    const savedRisks = localStorage.getItem('fuse-acknowledged-risks');
    if (savedRisks) {
      try {
        const parsedRisks = JSON.parse(savedRisks);
        if (Array.isArray(parsedRisks)) {
          setAcknowledgedRisks(parsedRisks);
        }
      } catch (error) {
        console.error('Error parsing saved acknowledged risks:', error);
      }
    }
  }, []);

  // Save acknowledged risks to localStorage whenever they change
  useEffect(() => {
    if (acknowledgedRisks.length > 0) {
      localStorage.setItem('fuse-acknowledged-risks', JSON.stringify(acknowledgedRisks));
    }
  }, [acknowledgedRisks]);

  const risks = [
    {
      id: "market-risk",
      icon: <TrendingDown className="h-5 w-5 text-red-400" />,
      title: "Market Risk",
      description: "Cryptocurrency values can go up and down",
      details: "The value of your deposited crypto (XRP/USDC) may fluctuate due to market conditions. While USDC is a stablecoin designed to maintain its value, XRP can be volatile. If XRP price drops and you withdraw during that time, you will receive less XRP than originally deposited."
    },
    {
      id: "grid-bot-risk",
      icon: <Zap className="h-5 w-5 text-orange-400" />,
      title: "Grid Bot Trading Risk",
      description: "Automated trading strategies carry execution risks",
      details: "Your funds are deployed in grid bot trading strategies that buy low and sell high within price ranges. While this can generate profits in sideways markets, trending markets may result in reduced holdings of the appreciating asset. Grid bots work best in volatile, range-bound markets."
    },
    {
      id: "early-withdrawal-risk",
      icon: <Lock className="h-5 w-5 text-red-400" />,
      title: "Early Withdrawal Impact",
      description: "Withdrawing before your selected schedule may result in losses",
      details: "If you withdraw before your chosen yield schedule (7, 30, or 90 days) completes, especially during XRP price declines, you may receive significantly less XRP than deposited. Longer schedules offer higher yields but carry greater market exposure risk."
    },
    {
      id: "smart-contract-risk",
      icon: <Zap className="h-5 w-5 text-yellow-400" />,
      title: "Technology Risk",
      description: "Blockchain technology, while secure, is not risk-free",
      details: "Although built on the secure XRP Ledger, any blockchain-based system carries inherent technical risks including potential bugs or network issues."
    },
    {
      id: "liquidity-risk",
      icon: <Lock className="h-5 w-5 text-blue-400" />,
      title: "Liquidity Risk",
      description: "In extreme conditions, withdrawals might be delayed",
      details: "While we aim for instant withdrawals, during high demand or market stress, there might be temporary delays in processing withdrawal requests."
    }
  ];

  const safetyMeasures = [
    {
      icon: <Shield className="h-5 w-5 text-green-400" />,
      title: "XRP Ledger Security",
      description: "Built on one of the most secure and proven blockchain networks"
    },
    {
      icon: <Eye className="h-5 w-5 text-blue-400" />,
      title: "Full Transparency",
      description: "All transactions are publicly verifiable on the blockchain"
    },
    {
      icon: <CheckCircle className="h-5 w-5 text-purple-400" />,
      title: "No Lock-up Periods",
      description: "Withdraw your funds anytime without penalties"
    }
  ];

  const handleRiskAcknowledgment = (riskId: string, checked: boolean) => {
    // Only allow checking, not unchecking - once acknowledged, it stays acknowledged
    if (checked && !acknowledgedRisks.includes(riskId)) {
      setAcknowledgedRisks([...acknowledgedRisks, riskId]);
    }
  };

  const allRisksAcknowledged = risks.length === acknowledgedRisks.length;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 1.6, duration: 0.5 }}
      className="mt-16"
    >
      <Card className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 border-gray-600/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <AlertTriangle className="h-6 w-6 text-yellow-400" />
            Important: Understand the Risks
          </CardTitle>
          <CardDescription className="text-gray-200">
            Like any DeFi, yield pools carry risks. We believe in complete transparency.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Key Message */}
          <Alert className="bg-yellow-500/20 border-yellow-400/50">
            <Info className="h-4 w-4 text-yellow-300" />
            <AlertTitle className="text-yellow-200 font-semibold">Golden Rule of Fusing</AlertTitle>
            <AlertDescription className="text-gray-100">
              Never Fuse more than you can afford to lose. Treat this as a long-term Project, not a get-rich-quick scheme.
            </AlertDescription>
          </Alert>

          {/* Risk Factors */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Key Risks to Consider</h3>
              <p className="text-xs text-gray-400 italic">
                Once acknowledged, risks remain permanently checked
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {risks.map((risk) => (
                <div
                  key={risk.id}
                  className="bg-gray-800/60 border border-gray-600/40 rounded-lg p-4 space-y-3"
                >
                  <div className="flex items-center gap-2">
                    {risk.icon}
                    <h4 className="font-medium text-white">{risk.title}</h4>
                  </div>
                  <p className="text-gray-200 text-sm">{risk.description}</p>
                  {showFullDisclosure && (
                    <p className="text-gray-300 text-xs border-t border-gray-600/40 pt-2">
                      {risk.details}
                    </p>
                  )}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={risk.id}
                      checked={acknowledgedRisks.includes(risk.id)}
                      onCheckedChange={(checked) => handleRiskAcknowledgment(risk.id, !!checked)}
                      disabled={acknowledgedRisks.includes(risk.id)}
                      className="border-gray-400 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500 disabled:opacity-100"
                    />
                    <label
                      htmlFor={risk.id}
                      className={`text-xs cursor-pointer ${
                        acknowledgedRisks.includes(risk.id)
                          ? 'text-green-300 font-medium'
                          : 'text-gray-200'
                      }`}
                    >
                      {acknowledgedRisks.includes(risk.id)
                        ? '✓ Risk acknowledged'
                        : 'I understand this risk'
                      }
                    </label>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Safety Measures */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-4">How We Keep Your Funds Safe</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {safetyMeasures.map((measure, index) => (
                <div
                  key={index}
                  className="bg-green-500/20 border border-green-400/40 rounded-lg p-4 space-y-2"
                >
                  <div className="flex items-center gap-2">
                    {measure.icon}
                    <h4 className="font-medium text-white">{measure.title}</h4>
                  </div>
                  <p className="text-gray-200 text-sm">{measure.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Treasury Information */}
          <div className="bg-blue-500/20 border border-blue-400/40 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-400" />
              Treasury Information
            </h3>
            <div className="space-y-3">
              <div>
                <span className="text-gray-300 text-sm">Treasury Wallet Address:</span>
                <div className="bg-gray-800/60 rounded p-2 mt-1 font-mono text-sm text-blue-300 break-all">
                  rHMKAoZT33VAQKzXxB7EdfDQ5pdPJ3sbU
                </div>
              </div>
              <div>
                <span className="text-gray-300 text-sm">Yield Pool Launch Date:</span>
                <div className="text-white font-medium">June 13, 2025</div>
              </div>
              <p className="text-gray-200 text-sm">
                All deposits are sent to this verified treasury wallet on the XRP Ledger.
                You can verify all transactions on the public blockchain.
              </p>
            </div>
          </div>

          {/* Full Disclosure Toggle */}
          <div className="text-center">
            <Button
              variant="outline"
              onClick={() => setShowFullDisclosure(!showFullDisclosure)}
              className="border-gray-400/40 text-gray-200 hover:bg-gray-700/50 hover:text-white"
            >
              {showFullDisclosure ? "Hide" : "Show"} Full Risk Disclosure
            </Button>
          </div>

          {/* Risk Acknowledgment Status */}
          <div className="bg-gray-800/60 border border-gray-600/40 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {allRisksAcknowledged ? (
                  <CheckCircle className="h-5 w-5 text-green-400" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-400" />
                )}
                <span className="text-white font-medium">
                  Risk Acknowledgment Status
                </span>
              </div>
              <span className="text-gray-200 text-sm">
                {acknowledgedRisks.length} of {risks.length} risks acknowledged
              </span>
            </div>
            {allRisksAcknowledged ? (
              <p className="text-green-300 text-sm mt-2 flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                All risks acknowledged! You can now proceed with deposits.
              </p>
            ) : (
              <p className="text-gray-300 text-sm mt-2">
                Please acknowledge all risks above before proceeding with deposits.
              </p>
            )}
          </div>

          {/* Final Disclaimer */}
          {showFullDisclosure && (
            <Alert className="bg-gray-700/40 border-gray-500/50">
              <AlertTriangle className="h-4 w-4 text-gray-300" />
              <AlertTitle className="text-gray-200 font-semibold">Legal Disclaimer</AlertTitle>
              <AlertDescription className="text-gray-200 text-sm">
                This yield pool is not FDIC insured and is not guaranteed by any government agency.
                Past performance does not indicate future results. Cryptocurrency is
                subject to market risk, including the potential loss of principal. Please consult
                with a financial advisor before making decisions.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
