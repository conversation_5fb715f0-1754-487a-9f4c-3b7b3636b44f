"use client"

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  TrendingUp,
  Shield,
  Zap,
  Trophy,
  Gamepad2,
  Calculator,
  HelpCircle,
  Wallet
} from 'lucide-react'
import { MobileToggleSection } from '@/components/ui/mobile-toggle-section'
import { BurnFuseComingSoon } from '@/components/burn-fuse/burn-fuse-coming-soon'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useIsMobile } from '@/hooks/use-mobile'
import { getBrowserInfo } from '@/lib/mobile-browser-utils'
import { GamingWalletInfo } from '@/components/gaming/gaming-wallet-info'
import { useFuseGaming } from '@/contexts/fuse-gaming-context'
import { useAuth } from '@/contexts/auth-context'
import { useWallet } from '@/hooks/use-wallet'
import Link from 'next/link'


interface CompartmentalizedYieldPoolProps {
  onOpenGame?: (game: 'fuse-bird' | 'rock-paper-scissors' | '2048') => void
}

export function CompartmentalizedYieldPool({ onOpenGame }: CompartmentalizedYieldPoolProps) {
  const isMobile = useIsMobile()
  const [browserInfo, setBrowserInfo] = useState(getBrowserInfo())
  const { user } = useAuth()
  const { isConnected } = useWallet()
  const { trustlineStatus } = useFuseGaming()


  useEffect(() => {
    setBrowserInfo(getBrowserInfo())
  }, [])

  const gameOptions = [
    {
      id: 'fuse-bird' as const,
      name: 'Fuse Bird',
      icon: '🐦',
      description: 'Navigate through obstacles',
      difficulty: 'Medium'
    },
    {
      id: 'rock-paper-scissors' as const,
      name: 'Rock Paper Scissors',
      icon: '✂️',
      description: 'Classic strategy game',
      difficulty: 'Easy'
    },
    {
      id: '2048' as const,
      name: '2048',
      icon: '🔢',
      description: 'Number puzzle challenge',
      difficulty: 'Hard'
    }
  ]

  const handleGameSelect = (gameId: 'fuse-bird' | 'rock-paper-scissors' | '2048') => {
    onOpenGame?.(gameId)

    // Haptic feedback for mobile
    if (browserInfo.supportsVibration && isMobile) {
      navigator.vibrate(50)
    }
  }



  return (
    <div className="space-y-6 max-w-6xl mx-auto px-4">
      {/* Burn FUSE Coming Soon Section */}
      <MobileToggleSection
        title="Burn $FUSE Tokens"
        subtitle="Unlock multipliers and exclusive features"
        icon={<Zap className="h-6 w-6" />}
        defaultOpen={true}
        persistKey="burn-fuse"
        className="mb-6"
      >
        <BurnFuseComingSoon />
      </MobileToggleSection>





      {/* Gaming Section */}
      <MobileToggleSection
        title="FUSE Token Gaming"
        subtitle="Compete with FUSE tokens for prizes and leaderboard rankings"
        icon={<Gamepad2 className="h-6 w-6" />}
        defaultOpen={false}
        persistKey="gaming"
        className="mb-6"
      >
        <div className="space-y-6">
          {/* Gaming Introduction */}
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h3 className="text-2xl font-bold text-white mb-4">
                🎮 FUSE Token Gaming
              </h3>
              <p className="text-white/80 mb-6 max-w-2xl mx-auto">
                Play skill-based games for free or compete with FUSE tokens for real prizes.
                Native token gaming is live with leaderboards and automated rewards!
              </p>
            </motion.div>
          </div>

          {/* Game Selection Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {gameOptions.map((game, index) => (
              <motion.div
                key={game.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-lg p-6 border border-gray-700/30 hover:border-blue-500/50 transition-all duration-300"
              >
                <div className="text-center">
                  <div className="text-4xl mb-3">{game.icon}</div>
                  <h4 className="text-lg font-semibold text-white mb-2">
                    {game.name}
                  </h4>
                  <p className="text-white/70 text-sm mb-3">
                    {game.description}
                  </p>
                  <Badge
                    variant="outline"
                    className={`mb-4 ${
                      game.difficulty === 'Easy' ? 'border-green-500 text-green-400' :
                      game.difficulty === 'Medium' ? 'border-yellow-500 text-yellow-400' :
                      'border-red-500 text-red-400'
                    }`}
                  >
                    {game.difficulty}
                  </Badge>
                  <div className="space-y-2">
                    <Button
                      onClick={() => handleGameSelect(game.id)}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                      size={isMobile ? "lg" : "default"}
                    >
                      🎮 Play Free
                    </Button>
                    <Button
                      onClick={() => handleGameSelect(game.id)}
                      className="w-full bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white font-semibold"
                      size={isMobile ? "lg" : "default"}
                    >
                      🏆 Compete with FUSE
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* FUSE Gaming Status */}
          <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/20 rounded-lg p-6">
            <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Zap className="h-5 w-5 text-purple-400" />
              FUSE Token Gaming - Live Now!
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white/70 text-sm">Account</span>
                  <Badge variant={user ? "default" : "secondary"} className="text-xs">
                    {user ? "Ready" : "Sign In"}
                  </Badge>
                </div>
                <div className="text-white text-sm">
                  {user ? "✓ Authenticated" : "Create account to compete"}
                </div>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white/70 text-sm">Wallet</span>
                  <Badge variant={isConnected ? "default" : "secondary"} className="text-xs">
                    {isConnected ? "Connected" : "Connect"}
                  </Badge>
                </div>
                <div className="text-white text-sm">
                  {isConnected ? "✓ Xaman ready" : "Connect Xaman wallet"}
                </div>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white/70 text-sm">FUSE Trustline</span>
                  <Badge
                    variant={trustlineStatus.hasTrustline ? "default" : "secondary"}
                    className="text-xs"
                  >
                    {trustlineStatus.hasTrustline ? "Active" : "Setup"}
                  </Badge>
                </div>
                <div className="text-white text-sm">
                  {trustlineStatus.hasTrustline
                    ? `✓ ${trustlineStatus.balance} FUSE`
                    : "Establish trustline"
                  }
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-lg p-4 text-center border border-purple-500/30">
                <div className="text-2xl mb-2">🎮</div>
                <div className="text-white font-medium">Live Games</div>
                <div className="text-white/70 text-sm">Fuse Bird, 2048, Rock Paper Scissors</div>
              </div>
              <div className="bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-lg p-4 text-center border border-purple-500/30">
                <div className="text-2xl mb-2">💎</div>
                <div className="text-white font-medium">FUSE Prizes</div>
                <div className="text-white/70 text-sm">Win real FUSE token rewards</div>
              </div>
              <div className="bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-lg p-4 text-center border border-purple-500/30">
                <div className="text-2xl mb-2">🏆</div>
                <div className="text-white font-medium">Leaderboards</div>
                <div className="text-white/70 text-sm">Daily, weekly, monthly rankings</div>
              </div>
            </div>

            {(!user || !isConnected || !trustlineStatus.hasTrustline) && (
              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Trophy className="h-4 w-4 text-yellow-400" />
                  <span className="text-yellow-400 font-medium text-sm">Setup Required for Competitive Play</span>
                </div>
                <p className="text-yellow-300/80 text-sm mb-3">
                  Complete the setup to compete with FUSE tokens for prizes and leaderboard rankings
                </p>
                <div className="flex flex-wrap gap-2">
                  {!user && (
                    <Link href="/register">
                      <Button size="sm" variant="outline" className="border-yellow-400 text-yellow-300 hover:bg-yellow-500/20">
                        Sign Up Free
                      </Button>
                    </Link>
                  )}
                  {!trustlineStatus.hasTrustline && user && isConnected && (
                    <Button
                      size="sm"
                      variant="outline"
                      className="border-yellow-400 text-yellow-300 hover:bg-yellow-500/20"
                      onClick={() => window.open('https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet', '_blank')}
                    >
                      Setup FUSE Trustline
                    </Button>
                  )}
                  <Link href="/games">
                    <Button size="sm" className="bg-purple-500 hover:bg-purple-600 text-white">
                      View Gaming Hub
                    </Button>
                  </Link>
                </div>
              </div>
            )}

            {user && isConnected && trustlineStatus.hasTrustline && (
              <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Trophy className="h-4 w-4 text-green-400" />
                  <span className="text-green-400 font-medium">Ready to Compete!</span>
                </div>
                <p className="text-green-300/80 text-sm mb-3">
                  Your account is fully set up for FUSE token competitive gaming
                </p>
                <Link href="/games">
                  <Button className="bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white">
                    Start Gaming Now
                  </Button>
                </Link>
              </div>
            )}
          </div>

          {/* Gaming Wallet Information */}
          <GamingWalletInfo />
        </div>
      </MobileToggleSection>



      {/* Performance Metrics for Mobile */}
      {isMobile && (
        <div className="mt-8 p-4 bg-gray-900/20 rounded-lg border border-gray-700/20">
          <div className="text-xs text-white/50 text-center">
            Optimized for {browserInfo.isSafari ? 'Safari' : browserInfo.isChrome ? 'Chrome' : browserInfo.isFirefox ? 'Firefox' : 'Mobile'} •
            Touch: {browserInfo.supportsTouch ? '✓' : '✗'} •
            Vibration: {browserInfo.supportsVibration ? '✓' : '✗'}
          </div>
        </div>
      )}


    </div>
  )
}
