"use client";

import { useEffect, useState, ReactNode } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { checkAuthStatus } from "@/lib/auth-client";
import { Loader2 } from "lucide-react";

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: string;
  businessOnly?: boolean;
}

export function ProtectedRoute({ children, requiredRole, businessOnly = false }: ProtectedRouteProps) {
  const { user, isLoading, isAdmin, isBusinessOwner, roles, portalRoles } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [redirectAttempted, setRedirectAttempted] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      // Only check once to prevent redirect loops
      if (!isLoading && !redirectAttempted) {
        setRedirectAttempted(true);

        // First check JWT authentication status
        const isJWTAuthenticated = await checkAuthStatus();

        // Check if user is logged in (either through context or JWT)
        if (!user && !isJWTAuthenticated) {
          const storedUserId = localStorage.getItem('user_id');

          // If we have a stored user ID but no session, give more time for new registrations
          if (storedUserId) {
            // Wait longer for new users who just registered
            setTimeout(async () => {
              const stillAuthenticated = await checkAuthStatus();
              if (!user && !stillAuthenticated) {
                // Clear stored data if authentication fails
                localStorage.removeItem('user_id');
                localStorage.removeItem('user_email');
                router.push("/login?redirect=" + encodeURIComponent(window.location.pathname));
              }
            }, 5000); // Increased timeout for new users
          } else {
            router.push("/login?redirect=" + encodeURIComponent(window.location.pathname));
          }
          return;
        }

        // If we have JWT auth but no user in context, wait for context to update
        if (!user && isJWTAuthenticated) {
          // Give more time for the auth context to sync, especially for new users
          setTimeout(() => {
            setIsCheckingAuth(false);
            setIsAuthorized(true); // Allow access if JWT is valid
          }, 2000); // Increased timeout
          return;
        }

        // Check if role is required
        if (requiredRole && user) {
          const hasRole = roles.includes(requiredRole) || portalRoles.includes(requiredRole);
          const isAdminAccess = requiredRole === "admin" && isAdmin;

          if (!hasRole && !isAdminAccess) {
            router.push("/dashboard");
            return;
          }
        }

        // Check if business only
        if (businessOnly && !isBusinessOwner) {
          router.push("/dashboard");
          return;
        }

        setIsAuthorized(true);
        setIsCheckingAuth(false);
      }
    };

    checkAuth();
  }, [user, isLoading, isAdmin, isBusinessOwner, roles, portalRoles, router, requiredRole, businessOnly, redirectAttempted]);

  if (isLoading || isCheckingAuth) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[#3A56FF]" />
          <p className="text-gray-600">Setting up your dashboard...</p>
          <p className="text-gray-400 text-sm mt-2">This may take a moment for new accounts</p>
        </div>
      </div>
    );
  }

  if (!isAuthorized) {
    return null;
  }

  return <>{children}</>;
}
