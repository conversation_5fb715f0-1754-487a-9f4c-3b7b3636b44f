"use client";

import { useEffect, useState, ReactNode } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { Loader2 } from "lucide-react";

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: string;
  businessOnly?: boolean;
}

export function ProtectedRoute({ children, requiredRole, businessOnly = false }: ProtectedRouteProps) {
  const { user, isLoading, isAdmin, isBusinessOwner, roles, refreshSession } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [refreshAttempted, setRefreshAttempted] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      setIsCheckingAuth(true);
      
      // If still loading, wait
      if (isLoading) {
        return;
      }
      
      // If no user and we haven't tried refreshing yet
      if (!user && !refreshAttempted) {
        setRefreshAttempted(true);
        try {
          await refreshSession();
          // After refresh, we'll come back through this effect
          return;
        } catch (error) {
          console.error("Failed to refresh session:", error);
        }
      }
      
      // No user after refresh attempt, redirect to login
      if (!user) {
        const currentPath = window.location.pathname;
        router.push(`/login?redirect=${encodeURIComponent(currentPath)}`);
        return;
      }
      
      // Check role requirements
      let hasRequiredRole = true;
      if (requiredRole) {
        if (requiredRole === 'admin') {
          hasRequiredRole = isAdmin;
        } else {
          hasRequiredRole = roles.includes(requiredRole);
        }
      }
      
      // Check business owner requirement
      const meetsBusinessRequirement = !businessOnly || isBusinessOwner;
      
      // Set authorization status
      const authorized = hasRequiredRole && meetsBusinessRequirement;
      setIsAuthorized(authorized);
      
      // Redirect if not authorized
      if (!authorized) {
        router.push('/dashboard');
      }
      
      setIsCheckingAuth(false);
    };
    
    checkAuth();
  }, [user, isLoading, isAdmin, isBusinessOwner, roles, requiredRole, businessOnly, router, refreshSession, refreshAttempted]);

  // Show loading state
  if (isLoading || isCheckingAuth) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[#3A56FF]" />
          <p className="text-gray-600">Setting up your dashboard...</p>
          <p className="text-gray-400 text-sm mt-2">This may take a moment for new accounts</p>
        </div>
      </div>
    );
  }

  if (!isAuthorized) {
    return null;
  }

  return <>{children}</>;
}
