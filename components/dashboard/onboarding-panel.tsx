"use client";

import { useState, useEffect } from "react";
import { AnimatedCard } from "@/components/animated-card";
import { useAuth } from "@/contexts/auth-context";
import { supabase } from "@/lib/supabase";
import { User, Heart, CheckCircle, Loader2, X, ChevronDown, Edit } from "lucide-react";


export function OnboardingPanel() {
  const { user, profile, refreshSession } = useAuth();
  const [activeStep, setActiveStep] = useState("profile");
  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    phone: "",
    wallet_address: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isDismissed, setIsDismissed] = useState(false);

  // Load initial data from localStorage if available
  useEffect(() => {
    if (user) {
      const storedFirstName = localStorage.getItem(`user_first_name_${user.id}`);
      const storedLastName = localStorage.getItem(`user_last_name_${user.id}`);
      const storedPhone = localStorage.getItem(`user_phone_${user.id}`);

      setFormData(prev => ({
        ...prev,
        first_name: profile?.first_name || storedFirstName || "",
        last_name: profile?.last_name || storedLastName || "",
        phone: profile?.phone || storedPhone || "",
        wallet_address: profile?.wallet_address || "",
      }));

      const onboardingCompleted = localStorage.getItem(`onboarding_complete_${user.id}`) === "true";
      if (onboardingCompleted || profile?.onboarding_completed) {
        setIsComplete(true);
      }

      // Check if panel was dismissed
      const panelDismissed = localStorage.getItem(`onboarding_panel_dismissed_${user.id}`) === "true";
      setIsDismissed(panelDismissed);

      setIsLoading(false);
    }
  }, [user, profile]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleDismiss = () => {
    if (user) {
      localStorage.setItem(`onboarding_panel_dismissed_${user.id}`, "true");
      setIsDismissed(true);
    }
  };

  const handleExpand = () => {
    if (user) {
      localStorage.removeItem(`onboarding_panel_dismissed_${user.id}`);
      setIsDismissed(false);
    }
  };

  const handleSubmit = async () => {
    if (!user) return;

    setIsSubmitting(true);

    try {
      const userId = user.id;

      if (userId) {
        // Update profile in Supabase
        const { error } = await supabase
          .from("profiles")
          .upsert({
            id: userId,
            first_name: formData.first_name,
            last_name: formData.last_name,
            phone: formData.phone,

          });

        if (error) {
          console.error("Error updating profile:", error);
          throw error;
        }

        // Refresh session to get updated profile data
        await refreshSession();
        setIsComplete(true);
        localStorage.setItem(`onboarding_complete_${userId}`, "true");

        // Clear localStorage items as they're no longer needed
        localStorage.removeItem(`user_first_name_${userId}`);
        localStorage.removeItem(`user_last_name_${userId}`);
        localStorage.removeItem(`user_phone_${userId}`);
        localStorage.removeItem(`onboarding_panel_dismissed_${userId}`);
      } else {
        throw new Error("User ID not found");
      }
    } catch (err) {
      console.error("Error updating profile:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isComplete || (profile && profile.onboarding_completed)) {
    return null;
  }

  if (isLoading) {
    return (
      <AnimatedCard className="bg-[#1A1A1A] p-6 rounded-lg text-white mb-8">
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-[#3A56FF]" />
        </div>
      </AnimatedCard>
    );
  }

  // Show collapsed button when dismissed
  if (isDismissed) {
    return (
      <AnimatedCard className="bg-[#1A1A1A] p-3 rounded-lg text-white mb-8">
        <button
          onClick={handleExpand}
          className="w-full flex items-center justify-between text-left hover:bg-[#2A2A2A] p-2 rounded transition-colors"
        >
          <div className="flex items-center">
            <Edit className="h-4 w-4 mr-2 text-[#3A56FF]" />
            <span className="text-sm font-medium">Complete your profile</span>
          </div>
          <ChevronDown className="h-4 w-4 text-gray-400" />
        </button>
      </AnimatedCard>
    );
  }

  return (
    <AnimatedCard className="bg-[#1A1A1A] p-6 rounded-lg text-white mb-8">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-bold text-xl">Edit your profile</h3>
        <button
          onClick={handleDismiss}
          className="text-gray-400 hover:text-white transition-colors p-1 rounded hover:bg-[#2A2A2A]"
          title="Dismiss panel"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      <p className="text-gray-300 mb-6">
        Please take a moment and change or complete your profile information to get the most out of your experience.
      </p>

      <div className="flex border-b border-gray-700 mb-6 overflow-x-auto">
        <button
          className={`py-2 px-4 font-medium whitespace-nowrap ${activeStep === "profile" ? "text-[#3A56FF] border-b-2 border-[#3A56FF]" : "text-gray-400"
            }`}
          onClick={() => setActiveStep("profile")}
        >
          <User className="h-4 w-4 inline mr-2" />
          Profile
        </button>
        <button
          className={`py-2 px-4 font-medium whitespace-nowrap ${activeStep === "interests" ? "text-[#3A56FF] border-b-2 border-[#3A56FF]" : "text-gray-400"
            }`}
          onClick={() => setActiveStep("interests")}
        >
          <Heart className="h-4 w-4 inline mr-2" />
          Interests
        </button>

      </div>

      {activeStep === "profile" && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">First Name</label>
            <input
              type="text"
              name="first_name"
              value={formData.first_name}
              onChange={handleInputChange}
              className="w-full p-2 bg-[#2A2A2A] border border-gray-700 rounded-md text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Last Name</label>
            <input
              type="text"
              name="last_name"
              value={formData.last_name}
              onChange={handleInputChange}
              className="w-full p-2 bg-[#2A2A2A] border border-gray-700 rounded-md text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Phone</label>
            <input
              type="text"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className="w-full p-2 bg-[#2A2A2A] border border-gray-700 rounded-md text-white"
            />
          </div>
        </div>
      )}

      {activeStep === "interests" && (
        <div>
          <p className="mb-4">Interest selection coming soon</p>
        </div>
      )}

      <div className="mt-6 flex justify-end">
        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="bg-[#3A56FF] text-white px-4 py-2 rounded-md text-sm flex items-center"
        >
          {isSubmitting ? (
            <span className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></span>
          ) : (
            <CheckCircle className="h-4 w-4 mr-2" />
          )}
          Save Profile
        </button>
      </div>
    </AnimatedCard>
  );
}
