"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { generateUniqueUserQRId, generateQRCodeDataURL } from '@/lib/qr-code-generator';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { QrCode, Download, Share2, RefreshCw, Copy, Check } from 'lucide-react';
import { toast } from 'sonner';

interface UserQRCodeProps {
  size?: number;
  showActions?: boolean;
  className?: string;
}

export function UserQRCode({ 
  size = 256, 
  showActions = true, 
  className = "" 
}: UserQRCodeProps) {
  const { user, profile } = useAuth();
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [copied, setCopied] = useState(false);

  const generateQRCode = async (forceRegenerate = false) => {
    if (!user) return;

    try {
      setIsLoading(true);

      // Use the API endpoint to generate QR code
      const response = await fetch('/api/generate-qr-codes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          forceRegenerate
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate QR code');
      }

      if (result.success && result.qrCodeUrl) {
        setQrCodeUrl(result.qrCodeUrl);
        if (forceRegenerate) {
          toast.success('QR code regenerated successfully!');
        } else if (result.message === 'QR code generated successfully') {
          toast.success('QR code generated successfully!');
        }
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('Error generating QR code:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to generate QR code');
    } finally {
      setIsLoading(false);
      setIsRegenerating(false);
    }
  };

  const handleRegenerate = async () => {
    setIsRegenerating(true);
    await generateQRCode(true);
  };

  const handleDownload = () => {
    if (!qrCodeUrl) return;

    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = `fuse-vip-qr-${user?.email || 'user'}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success('QR code downloaded!');
  };

  const handleShare = async () => {
    if (!qrCodeUrl) return;

    try {
      // Convert data URL to blob
      const response = await fetch(qrCodeUrl);
      const blob = await response.blob();
      const file = new File([blob], 'fuse-vip-qr.png', { type: 'image/png' });

      if (navigator.share && navigator.canShare({ files: [file] })) {
        await navigator.share({
          title: 'My Fuse.VIP QR Code',
          text: 'Scan my QR code to connect on Fuse.VIP!',
          files: [file]
        });
        toast.success('QR code shared!');
      } else {
        // Fallback: copy to clipboard
        await handleCopy();
      }
    } catch (error) {
      console.error('Error sharing QR code:', error);
      toast.error('Failed to share QR code');
    }
  };

  const handleCopy = async () => {
    if (!qrCodeUrl) return;

    try {
      // Copy the data URL to clipboard
      await navigator.clipboard.writeText(qrCodeUrl);
      setCopied(true);
      toast.success('QR code copied to clipboard!');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Error copying QR code:', error);
      toast.error('Failed to copy QR code');
    }
  };

  useEffect(() => {
    if (user) {
      generateQRCode();
    }
  }, [user]);

  if (!user) {
    return null;
  }

  return (
    <Card className={`w-full max-w-md mx-auto ${className}`}>
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          <QrCode className="h-5 w-5" />
          Your Fuse.VIP QR Code
        </CardTitle>
        <p className="text-sm text-gray-600">
          Share this code with others to connect on Fuse.VIP
        </p>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex justify-center">
          {isLoading ? (
            <div className="flex items-center justify-center w-64 h-64 bg-gray-100 rounded-lg">
              <div className="text-center">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-500">Generating QR code...</p>
              </div>
            </div>
          ) : qrCodeUrl ? (
            <div className="relative">
              <img 
                src={qrCodeUrl} 
                alt="Your Fuse.VIP QR Code" 
                className="rounded-lg shadow-md"
                style={{ width: size, height: size }}
              />
              {isRegenerating && (
                <div className="absolute inset-0 bg-white/80 flex items-center justify-center rounded-lg">
                  <RefreshCw className="h-6 w-6 animate-spin text-gray-600" />
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center w-64 h-64 bg-gray-100 rounded-lg">
              <p className="text-sm text-gray-500">Failed to generate QR code</p>
            </div>
          )}
        </div>

        {profile && (
          <div className="text-center text-sm text-gray-600">
            <p className="font-medium">
              {profile.first_name} {profile.last_name}
            </p>
            <p>{user.email}</p>
            {profile.is_card_holder && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800 mt-1">
                VIP Member
              </span>
            )}
          </div>
        )}

        {showActions && qrCodeUrl && (
          <div className="flex flex-wrap gap-2 justify-center">
            <Button
              onClick={handleDownload}
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              <Download className="h-4 w-4" />
              Download
            </Button>
            
            <Button
              onClick={handleShare}
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              <Share2 className="h-4 w-4" />
              Share
            </Button>
            
            <Button
              onClick={handleCopy}
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              {copied ? (
                <Check className="h-4 w-4 text-green-600" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
              {copied ? 'Copied!' : 'Copy'}
            </Button>
            
            <Button
              onClick={handleRegenerate}
              variant="outline"
              size="sm"
              disabled={isRegenerating}
              className="flex items-center gap-1"
            >
              <RefreshCw className={`h-4 w-4 ${isRegenerating ? 'animate-spin' : ''}`} />
              Regenerate
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
