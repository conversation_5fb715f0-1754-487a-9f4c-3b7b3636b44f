{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^3.9.1", "@lottiefiles/dotlottie-react": "latest", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.4", "@types/jszip": "^3.4.0", "@types/qrcode": "^1.5.5", "autoprefixer": "^10.4.20", "canvas-confetti": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "date-fns": "^4.1.0", "embla-carousel-react": "latest", "fast-text-encoding": "^1.0.6", "framer-motion": "latest", "html5-qrcode": "^2.3.8", "input-otp": "latest", "jose": "^5.9.6", "jszip": "^3.10.1", "lucide-react": "^0.454.0", "mapbox-gl": "latest", "next": "15.3.2", "next-themes": "latest", "postcss": "^8", "qrcode": "^1.5.4", "react": "^19", "react-day-picker": "latest", "react-dom": "^19", "react-hook-form": "latest", "react-native-get-random-values": "^1.11.0", "react-resizable-panels": "latest", "recharts": "latest", "sonner": "^2.0.5", "stripe": "^18.1.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "latest", "xrpl": "^4.2.5", "xumm": "^1.8.0", "xumm-sdk": "^1.11.2", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.26.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-tailwindcss": "^3.18.0", "next": "15.3.2", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}