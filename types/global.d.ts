interface Window {
  calendar?: {
    schedulingButton: {
      load: (options: {
        url: string
        color: string
        label: string
        target: HTMLElement
      }) => void
    }
  }
  xumm?: {
    payload: {
      create: (payload: any) => Promise<{ uuid: string }>
      get: (uuid: string) => Promise<{
        meta: {
          resolved: boolean
          signed: boolean
        }
        response: {
          account: string
        }
      }>
    }
  }
}
