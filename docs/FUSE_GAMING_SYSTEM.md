# 🎮 FUSE Token Gaming System

## Overview

The FUSE Token Gaming System enables users to compete in games using FUSE tokens as entry fees, with automated prize distribution and leaderboard management. This system integrates seamlessly with the existing XRP gaming infrastructure while providing a native token-based gaming experience.

## 🏗️ System Architecture

### Core Components

1. **FUSE Gaming Context** (`contexts/fuse-gaming-context.tsx`)
   - Manages FUSE token gaming sessions and state
   - Handles trustline verification and token payments
   - Provides unified interface for FUSE gaming features

2. **FUSE Token Utilities** (`lib/fuse-token-utils.ts`)
   - Trustline checking via XRPL API
   - Token payment creation for Xaman wallet
   - Transaction verification on XRPL
   - Integration with Magnetic DEX for trustline setup

3. **Database Schema** (Migration: `20250616000000_fuse_gaming_system.sql`)
   - `fuse_gaming_sessions` - FUSE token-based game sessions
   - `fuse_trustlines` - User trustline tracking
   - `fuse_gaming_transactions` - Token transaction logs
   - `fuse_leaderboards` - FUSE-specific leaderboards
   - `fuse_leaderboard_entries` - Player rankings and prizes

4. **UI Components**
   - `FuseCompetitiveModal` - Entry flow with trustline checking
   - Updated game components with FUSE token support
   - Seamless integration with existing gaming UI

## 🎯 User Experience Flow

### 1. Entry Flow
```
User clicks "Compete with FUSE" → Check Trustline → Payment → Game Start
```

**Detailed Steps:**
1. **Trustline Check**: Automatically verify FUSE trustline via XRPL API
2. **Setup Guidance**: If no trustline, direct to Magnetic DEX with return URL
3. **Payment Creation**: Generate Xaman payment for 1 FUSE token
4. **Game Session**: Start verified competitive session
5. **Score Submission**: Automatic leaderboard entry and ranking

### 2. Trustline Management
- **Detection**: Query user's wallet via `account_lines` XRPL method
- **Setup**: Seamless redirect to Magnetic DEX with return parameters
- **Verification**: Real-time balance and limit checking
- **Caching**: Database storage for quick access

### 3. Payment Processing
- **Token Format**: Standard XRPL token payment structure
- **Verification**: On-chain transaction confirmation
- **Gaming Wallet**: `rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx`
- **Entry Fee**: 1 FUSE token per game

## 🔧 Technical Implementation

### FUSE Token Details
```javascript
const FUSE_TOKEN = {
  currency: 'FUSE',
  issuer: 'rs2G9J95qwL3yw241JTRdgms2hhcLouVHo',
  name: 'Fuse.vip Token'
}
```

### Payment Structure
```javascript
{
  TransactionType: 'Payment',
  Destination: 'rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx',
  Amount: {
    currency: 'FUSE',
    issuer: 'rs2G9J95qwL3yw241JTRdgms2hhcLouVHo',
    value: '1'
  }
}
```

### Trustline Verification
```javascript
// Check via XRPL account_lines method
const response = await fetch(xrplEndpoint, {
  method: 'POST',
  body: JSON.stringify({
    method: 'account_lines',
    params: [{
      account: walletAddress,
      peer: FUSE_TOKEN.issuer,
      ledger_index: 'validated'
    }]
  })
})
```

## 🏆 Leaderboard System

### Leaderboard Types
- **Daily**: 10 FUSE prize pool, resets daily
- **Weekly**: 50 FUSE prize pool, resets weekly  
- **Monthly**: 200 FUSE prize pool, resets monthly
- **All-Time**: 1000 FUSE prize pool, permanent

### Prize Distribution
- Automated calculation based on rankings
- On-chain prize distribution (future enhancement)
- Real-time leaderboard updates

## 🔗 Integration Points

### Magnetic DEX Integration
- **Trustline Setup**: Direct users to establish FUSE trustline
- **Return URL**: Seamless flow back to gaming session
- **URL Format**: `https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet&return_to={returnUrl}`

### Xaman Wallet Integration
- **Payment Requests**: Native FUSE token payments
- **Transaction Signing**: Secure on-chain verification
- **Return Handling**: Automatic game session continuation

### Existing Gaming System
- **Dual Mode**: Both XRP and FUSE competitive options
- **Shared Components**: Reuse existing game logic
- **Unified UI**: Consistent gaming experience

## 📊 Database Functions

### `upsert_fuse_trustline`
Creates or updates user trustline records with verification status.

### `submit_fuse_competitive_score`
Handles score submission, leaderboard entry, and ranking calculation.

## 🚀 API Endpoints

### `/api/games/fuse-submit-score`
- **POST**: Submit competitive scores with transaction verification
- **GET**: Retrieve user's FUSE gaming session history
- **PATCH**: Manual transaction verification

## 🎮 Supported Games

1. **Fuse Bird** - Flappy Bird style game
2. **2048** - Number puzzle game
3. **Rock Paper Scissors** - Strategy game

All games support both XRP and FUSE token competitive modes.

## 🔒 Security Features

- **Transaction Verification**: All payments verified on XRPL
- **User Authentication**: Supabase auth integration
- **Wallet Validation**: Xaman wallet address verification
- **Anti-fraud**: Session-based game tracking
- **RLS Policies**: Row-level security for all tables

## 🛠️ Development Setup

1. **Database Migration**: Run the FUSE gaming system migration
2. **Environment Variables**: Ensure XRPL endpoints are configured
3. **Provider Setup**: Add FuseGamingProvider to your app
4. **Component Integration**: Import and use FUSE gaming components

## 📈 Future Enhancements

- **Automated Prize Distribution**: On-chain FUSE token rewards
- **Tournament System**: Special events with larger prize pools
- **NFT Integration**: Game achievement tokens
- **Cross-Game Rewards**: Unified progression system
- **Mobile App**: Native mobile gaming experience

## 🎯 Benefits

- **Native Token Ecosystem**: Strengthens FUSE token utility
- **User Engagement**: Competitive gaming drives retention
- **Seamless UX**: Leverages existing infrastructure
- **Scalable Architecture**: Easy to add new games
- **Community Building**: Leaderboards foster competition

## 📞 Support

For technical issues or questions about the FUSE gaming system:
- Email: <EMAIL>
- Discord: https://discord.gg/n9d7PEbm
- Documentation: This file and inline code comments
