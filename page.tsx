"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import { X, Play, Check, ArrowRight, ChevronRight, Star } from 'lucide-react'
import { Link } from "@/components/ui/link"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { CtaSection } from "@/components/cta-section"
import { AnimatedSection } from "@/components/animated-section"
import { BusinessSpotlight } from "@/components/business/business-spotlight"
import { LottieAnimation } from "@/components/lottie-animation"
import { AnimatedCubes } from "@/components/animated-cubes"
import { Dialog, Transition } from "@headlessui/react"
import { Fragment } from "react"

// Define types for better code organization
type PricingTier = {
  name: string;
  price: string;
  description: string;
  features: string[];
  cta: string;
  ctaLink: string;
  popular?: boolean;
};

type Feature = {
  title: string;
  description: string;
  icon: React.ReactNode;
  link: string;
};

type VideoModalProps = {
  videoId: string;
  isOpen: boolean;
  onClose: () => void;
  title: string;
};

// Data for pricing tiers
const pricingTiers: PricingTier[] = [
  {
    name: "Starter",
    price: "Free",
    description: "Perfect for small businesses just getting started",
    features: [
      "Basic loyalty program",
      "Unlimited Customers",
      "Standard analytics",
      "Email support"
    ],
    cta: "Start Free",
    ctaLink: "/register",
  },
  {
    name: "Growth",
    price: "$100",
    description: "For businesses ready to scale their loyalty program",
    features: [
      "Advanced loyalty program",
      "Unlimited customers",
      "Advanced analytics",
      "Priority support",
      "Custom rewards",
      "Multi-location support"
    ],
    cta: "Start 14-Day Trial",
    ctaLink: "/register",
    popular: true
  },
  {
    name: "Enterprise",
    price: "Custom",
    description: "For large businesses with complex needs",
    features: [
      "Everything in Growth",
      "Dedicated account manager",
      "Custom integration",
      "API access",
      "White-label solution",
      "24/7 phone support"
    ],
    cta: "Contact Sales",
    ctaLink: "/book-call"
  },
];

// Key features data
const keyFeatures: Feature[] = [
  {
    title: "Tokenized Loyalty",
    description: "Convert traditional points into valuable $FUSE tokens that customers can use across the network.",
    icon: <Star className="h-6 w-6 text-[#FFD700]" />,
    link: "/fuse"
  },
  {
    title: "Cross-Business Rewards",
    description: "Enable customers to earn and redeem rewards at any participating business in the network.",
    icon: <ArrowRight className="h-6 w-6 text-[#316bff]" />,
    link: "/industry"
  },
  {
    title: "Real-Time Analytics",
    description: "Access powerful insights about customer behavior and preferences to optimize your offerings.",
    icon: <Check className="h-6 w-6 text-green-500" />,
    link: "/solutions"
  },
];

const VideoModal = ({ videoId, isOpen, onClose, title }: VideoModalProps) => {
  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-lg bg-black text-left align-middle shadow-xl transition-all">
                <div className="relative">
                  <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 z-10"
                    aria-label="Close video"
                  >
                    <X className="h-6 w-6" />
                  </button>
                  <div className="relative pb-[56.25%] h-0">
                    <iframe
                      src={`https://www.youtube.com/embed/${videoId}?autoplay=1`}
                      title={title}
                      className="absolute top-0 left-0 w-full h-full"
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    />
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default function Home() {
  const [showVideo, setShowVideo] = useState(false)
  const [showTokenVideo, setShowTokenVideo] = useState(false)

  const youtubeVideoId = "uBDquOBgd0c"
  const tokenVideoId = "qjc966ZwKkk"

  return (
    <>
      {/* Hero Section - Refined messaging and layout */}
      <section className="relative w-full min-h-screen bg-gradient-to-b from-[#000814] via-[#000d1a] to-black overflow-hidden flex items-center justify-center text-white px-4 sm:px-8 py-20">
        {/* Background Image with overlay */}
        <div className="absolute inset-0 z-0 after:content-[''] after:absolute after:inset-0 after:bg-radial-gradient after:from-transparent after:to-black/70">
          <Image
            src="/images/landing-bckgrnd-2.png"
            alt="Fuse.vip Background"
            width={1600}
            height={900}
            className="object-cover w-full h-full opacity-30"
            priority
          />
        </div>

        {/* Foreground Content */}
        <div className="relative z-10 flex flex-col lg:flex-row w-full max-w-7xl mx-auto items-center justify-between gap-12">
          {/* Left Column - Value Proposition */}
          <div className="flex flex-col space-y-6 max-w-xl">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Badge className="bg-[#316bff]/20 text-[#316bff] hover:bg-[#316bff]/30 mb-4">
                Revolutionizing Customer Loyalty
              </Badge>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.1 }}
              className="text-4xl sm:text-5xl md:text-6xl font-bold text-white leading-tight"
            >
              Loyalty Reimagined <span className="text-[#FF914D]">Commerce Reconnected</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
              className="text-lg sm:text-xl text-gray-300"
            >
              Helping businesses and customers transform their loyalty experiences through innovative digital solutions that drive engagement and growth.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.3 }}
              className="pt-2 space-y-4 sm:space-y-0 sm:flex sm:flex-row sm:gap-4"
            >
              <Link href="/register">
                <Button
                  size="lg"
                  className="w-full sm:w-auto bg-[#316bff] hover:bg-[#2151d3] text-white text-base sm:text-lg px-6 sm:px-8 py-6 h-auto"
                >
                  Start Free Trial
                </Button>
              </Link>

              <Button
                size="lg"
                variant="outline"
                className="w-full sm:w-auto border-white text-black hover:bg-white/10 text-base sm:text-lg px-6 sm:px-8 py-6 h-auto flex items-center justify-center gap-2"
                onClick={() => setShowVideo(true)}
              >
                <Play className="h-5 w-5" /> Watch Demo
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.4 }}
              className="pt-4"
            >
              <p className="text-sm text-gray-400 flex items-center gap-2">
                <Check className="h-4 w-4 text-green-500" /> No credit card required
              </p>
              <p className="text-sm text-gray-400 flex items-center gap-2 mt-1">
                <Check className="h-4 w-4 text-green-500" /> Join 70+ businesses already using Fuse.vip
              </p>
            </motion.div>
          </div>

          {/* Right Column - Product Showcase */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="relative w-full max-w-md lg:max-w-lg xl:max-w-xl"
          >
            <div className="relative bg-gradient-to-br from-[#1a1f2e] to-[#0d1117] p-4 rounded-xl border border-gray-800 shadow-2xl">
              <div className="absolute -top-2 -right-2 bg-[#FFD700] text-black text-xs font-bold py-1 px-3 rounded-full">
                PREMIUM
              </div>
              <div className="flex items-center justify-between border-b border-gray-800 pb-3 mb-4">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
                <div className="text-xs text-gray-400">Fuse.vip Dashboard</div>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-semibold">FUSE.vip Network</h3>
                    <p className="text-sm text-gray-400">Est. March, 2025</p>
                  </div>
                  <Badge className="bg-green-500/20 text-green-400 hover:bg-green-500/30">
                    +24% ↑
                  </Badge>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div className="bg-white/5 p-3 rounded-lg">
                    <p className="text-xs text-gray-400">Active Users</p>
                    <p className="text-xl font-bold">256</p>
                  </div>
                  <div className="bg-white/5 p-3 rounded-lg">
                    <p className="text-xs text-gray-400">New Businesses</p>
                    <p className="text-xl font-bold">74</p>
                  </div>
                  <div className="bg-white/5 p-3 rounded-lg">
                    <p className="text-xs text-gray-400">Increased Revenue</p>
                    <p className="text-xl font-bold">5%</p>
                  </div>
                </div>
                <div className="bg-white/5 p-4 rounded-lg">
                  <h4 className="text-sm font-medium mb-3">Customer Retention</h4>
                  <div className="h-24 flex items-end gap-1">
                    {[35, 45, 30, 60, 75, 65, 80].map((height, i) => (
                      <div
                        key={i}
                        className="bg-[#316bff] rounded-t w-full"
                        style={{ height: `${height}%` }}
                      ></div>
                    ))}
                  </div>
                  <div className="flex justify-between mt-2 text-xs text-gray-400">
                    <span>Mon</span>
                    <span>Tue</span>
                    <span>Wed</span>
                    <span>Thu</span>
                    <span>Fri</span>
                    <span>Sat</span>
                    <span>Sun</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Image
                    src="/images/premium-card.png"
                    alt="Premium Fuse Card"
                    width={80}
                    height={50}
                    className="rounded-md"
                  />
                  <div>
                    <h4 className="text-sm font-medium">Premium Membership Card</h4>
                    <p className="text-xs text-gray-400">Issued to 256 VIP members</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="bg-white py-10">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap items-center justify-center gap-6 md:gap-10">
            <p className="text-gray-500 text-sm font-medium w-full text-center mb-4">BUILT ON PROVEN TECHNOLOGIES</p>
            {[
              { name: 'Stripe', url: 'https://stripe.com/', },
              { name: 'XRP', url: 'https://xrpl.org/', },
              { name: 'Xaman Wallet', url: 'https://xumm.app/', },
              { name: 'Xahau', url: 'https://xahau.network/', },
              { name: 'Vercel', url: 'https://vercel.com/', }
            ].map((brand, index) => (
              <a
                key={index} //
                href={brand.url}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-[#316bff] hover:bg-[#2151d3] text-white px-4 py-2 rounded-md text-sm sm:text-base font-medium transition-all"
              >
                {brand.name}
              </a>
            ))}
          </div>
        </div>
      </section>

      {/* Key Features Section */}
      <section className="py-20 bg-gray-50" id="features">
        <div className="container mx-auto px-4">
          <AnimatedSection>
            <div className="text-center mb-16">
              <Badge className="bg-[#316bff]/10 text-[#316bff] mb-4">
                Key Features
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[#1e293b]">
                Everything You Need to Succeed
              </h2>
              <p className="text-xl text-[#4a4a4a] max-w-2xl mx-auto">
                Our comprehensive platform provides all the tools you need to create, manage, and grow your loyalty program.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {keyFeatures.map((feature, index) => (
              <AnimatedSection key={index} delay={index * 0.2}>
                <Link href={feature.link}>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    transition={{ type: "spring", stiffness: 300 }}
                    className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl hover:bg-gray-50 transition-all h-full cursor-pointer"
                  >
                    <div className="w-12 h-12 bg-[#316bff]/10 rounded-lg flex items-center justify-center mb-6">
                      {feature.icon}
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-[#1e293b]">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </motion.div>
                </Link>
              </AnimatedSection>
            ))}
          </div>


          <AnimatedSection delay={0.4}>
            <div className="bg-white rounded-xl shadow-xl overflow-hidden border border-gray-100">
              <div className="grid grid-cols-1 lg:grid-cols-2">
                <div className="p-8 md:p-12 flex flex-col justify-center">
                  <Badge className="bg-[#316bff]/10 text-[#316bff] mb-4 w-fit">
                    Powerful Platform
                  </Badge>
                  <h3 className="text-2xl md:text-3xl font-bold mb-4 text-[#1e293b]">
                    Designed for Modern Businesses
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Our platform combines the best of traditional loyalty programs with the innovation of blockchain technology,
                    creating a seamless experience for both businesses and customers.
                  </p>
                  <ul className="space-y-3 mb-8">
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                      <span>Easy to set up and manage</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                      <span>Customizable to your brand</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                      <span>Integrates with your existing systems</span>
                    </li>
                    <li className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                      <span>No technical expertise required</span>
                    </li>
                  </ul>
                  <Link href="/resources">
                    <Button className="bg-[#316bff] hover:bg-[#2151d3] text-white w-fit">
                      Learn More <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </Link>
                </div>
                <div className="bg-gradient-to-br from-[#0f172a] to-[#1e293b] p-8 md:p-12 text-white">
                  <h3 className="text-2xl font-bold mb-6">How It Works</h3>
                  <ol className="space-y-6">
                    <li className="flex">
                      <div className="flex-shrink-0 w-8 h-8 bg-[#316bff] rounded-full flex items-center justify-center mr-4 mt-0.5">
                        1
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg mb-1">Sign Up</h4>
                        <p className="text-white/70">Create your account and customize your loyalty program to match your brand.</p>
                      </div>
                    </li>
                    <li className="flex">
                      <div className="flex-shrink-0 w-8 h-8 bg-[#316bff] rounded-full flex items-center justify-center mr-4 mt-0.5">
                        2
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg mb-1">Onboard Customers</h4>
                        <p className="text-white/70">Invite your customers to join your program through email, SMS, or in-store.</p>
                      </div>
                    </li>
                    <li className="flex">
                      <div className="flex-shrink-0 w-8 h-8 bg-[#316bff] rounded-full flex items-center justify-center mr-4 mt-0.5">
                        3
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg mb-1">Reward Transactions</h4>
                        <p className="text-white/70">Customers earn $FUSE tokens automatically with every purchase.</p>
                      </div>
                    </li>
                    <li className="flex">
                      <div className="flex-shrink-0 w-8 h-8 bg-[#316bff] rounded-full flex items-center justify-center mr-4 mt-0.5">
                        4
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg mb-1">Analyze & Optimize</h4>
                        <p className="text-white/70">Use our analytics to understand customer behavior and optimize your program.</p>
                      </div>
                    </li>
                  </ol>
                </div>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Business Spotlight - Renamed to Customer Success */}
      <section className="py-20 bg-white" id="customers">
        <div className="container mx-auto px-4">
          <AnimatedSection>
            <div className="text-center mb-16">
              <Badge className="bg-[#316bff]/10 text-[#316bff] mb-4">
                Success Stories
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[#1e293b]">
                Businesses Thriving with Fuse.vip
              </h2>
              <p className="text-xl text-[#4a4a4a] max-w-2xl mx-auto">
                See how businesses like yours are transforming their customer relationships and driving growth with our platform.
              </p>
            </div>
          </AnimatedSection>

          <BusinessSpotlight />

          <div className="mt-12 text-center">
            <Link href="/reviews">
              <Button className="bg-[#316bff] hover:bg-[#2151d3] text-white">
                View All Case Studies <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-gray-50" id="pricing">
        <div className="container mx-auto px-4">
          <AnimatedSection>
            <div className="text-center mb-16">
              <Badge className="bg-[#316bff]/10 text-[#316bff] mb-4">
                Pricing
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[#1e293b]">
                Simple, Transparent Pricing
              </h2>
              <p className="text-xl text-[#4a4a4a] max-w-2xl mx-auto">
                Choose the plan that's right for your business. All plans include access to our core features.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {pricingTiers.map((tier, index) => (
              <AnimatedSection key={index} delay={index * 0.2}>
                <div className={`bg-white rounded-xl overflow-hidden shadow-lg border ${tier.popular ? 'border-[#316bff]' : 'border-gray-200'} h-full flex flex-col`}>
                  {tier.popular && (
                    <div className="bg-[#316bff] text-white text-center py-2 text-sm font-medium">
                      Most Popular
                    </div>
                  )}
                  <div className="p-8">
                    <h3 className="text-xl font-bold mb-2 text-[#1e293b]">{tier.name}</h3>
                    <div className="mb-4">
                      <span className="text-4xl font-bold text-[#1e293b]">{tier.price}</span>
                      {tier.price !== "Free" && tier.price !== "Custom" && (
                        <span className="text-gray-500">/year</span>
                      )}
                    </div>
                    <p className="text-gray-600 mb-6">{tier.description}</p>

                    {/*Wrap Button Inside Link */}
                    <Link href={tier.ctaLink}>
                      <Button
                        className={`w-full ${tier.popular
                          ? 'bg-[#316bff] hover:bg-[#2151d3] text-white'
                          : 'bg-white border border-[#316bff] text-[#316bff] hover:bg-[#316bff]/5'}`}
                      >
                        {tier.cta}
                      </Button>
                    </Link>

                  </div>
                  <div className="p-8 bg-gray-50 flex-grow">
                    <p className="font-medium text-[#1e293b] mb-4">What's included:</p>
                    <ul className="space-y-3">
                      {tier.features.map((feature, i) => (
                        <li key={i} className="flex items-start">
                          <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </AnimatedSection>
            ))}
          </div>

          <div className="mt-12 text-center">
            <p className="text-gray-500 mb-4">All plans include a 14-day free trial. No credit card required.</p>
            <p className="text-gray-500">
              Need a custom solution? <Link href="/book-call" className="text-[#316bff] hover:underline">Contact our sales team</Link>
            </p>
          </div>
        </div>
      </section>

      {/* Token Section - Reframed as Technology */}
      <section className="py-20 bg-white" id="technology">
        <div className="container mx-auto px-4">
          <AnimatedSection>
            <div className="text-center mb-12">
              <Badge className="bg-[#316bff]/10 text-[#316bff] mb-4">
                Our Technology
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[#1e293b]">$FUSE Token Ecosystem</h2>
              <p className="text-xl text-[#4a4a4a] max-w-2xl mx-auto">
                Powered by innovative blockchain technology that makes loyalty programs more valuable and engaging.
              </p>
            </div>
          </AnimatedSection>

          <AnimatedSection delay={0.2}>
            <div className="flex flex-col md:flex-row items-center justify-center gap-12">
              <div
                className="md:w-1/2 lg:w-2/5 relative group cursor-pointer overflow-hidden rounded-lg"
                onClick={() => setShowTokenVideo(true)}
                role="button"
              >
                <div className="relative overflow-hidden rounded-lg">
                  <Image
                    src={`https://img.youtube.com/vi/${tokenVideoId}/maxresdefault.jpg`}
                    alt="Fuse Token Video Thumbnail"
                    width={640}
                    height={360}
                    className="w-full h-auto rounded-lg shadow-lg group-hover:brightness-75 transition-all duration-300"
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="bg-white bg-opacity-80 rounded-full p-4 shadow-lg transform transition-transform duration-300 group-hover:scale-110">
                      <Play className="h-8 w-8 text-[#316bff]" />
                    </div>
                  </div>
                </div>
              </div>
              <div className="md:w-1/2 lg:w-3/5 space-y-6">
                <h3 className="text-2xl font-bold text-[#1e293b]">The Power of Tokenized Loyalty</h3>
                <p className="text-lg text-gray-600">
                  Our $FUSE tokens transform traditional loyalty points into a valuable digital asset that customers can use across our entire network of businesses.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                    <span>Tokens never expire, unlike traditional points</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                    <span>Customers can earn and redeem across multiple businesses</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                    <span>Transparent and secure blockchain technology</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                    <span>No technical knowledge required - we handle the complexity</span>
                  </li>
                </ul>
                <div className="flex flex-wrap gap-4">
                  <Button
                    className="bg-[#316bff] hover:bg-[#2151d3] text-white"
                    onClick={() => setShowTokenVideo(true)}
                  >
                    Watch Video
                  </Button>
                  <Link href="/resources">
                    <Button
                      variant="outline"
                      className="border-[#316bff] text-[#316bff] hover:bg-[#316bff]/5"
                    >
                      Technical Documentation
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* CTA Section - Enhanced */}
      <section className="py-20 bg-gradient-to-r from-[#0f172a] to-[#1e293b] text-white" >
        <div className="container mx-auto px-4">
          <AnimatedSection>
            <div className="max-w-4xl mx-auto text-center">
              <Badge className="bg-white/10 text-white hover:bg-white/20 mb-4">
                Get Started Today
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">
                Ready to Transform Your Customer Relationships?
              </h2>
              <p className="text-xl text-white/80 max-w-2xl mx-auto mb-8">
                Join over 300 businesses already using Fuse.vip to increase customer retention and drive growth.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Link href="/register">
                  <Button
                    size="lg"
                    className="bg-[#FF914D] hover:bg-[#FF915D] text-black font-medium w-full sm:w-auto"
                  >
                    Start Your Free Trial
                  </Button>
                </Link>
                <Link href="/book-call">
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-white text-black hover:bg-white/10 w-full sm:w-auto"
                  >
                    Schedule a Demo
                  </Button>
                </Link>
              </div>
              <p className="text-sm text-white/60 mt-6">
                No credit card required. 14-day free trial.
              </p>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Video Modals */}
      <VideoModal
        videoId={youtubeVideoId}
        isOpen={showVideo}
        onClose={() => setShowVideo(false)}
        title="Fuse.Vip Introduction"
      />
      <VideoModal
        videoId={tokenVideoId}
        isOpen={showTokenVideo}
        onClose={() => setShowTokenVideo(false)}
        title="Fuse Token Information"
      />
    </>
  );
}


